# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ampproject/remapping@^2.2.0":
  version "2.3.0"
  resolved "https://registry-npm-next.huolala.work/@ampproject/remapping/-/remapping-2.3.0.tgz#ed441b6fa600072520ce18b43d2c8cc8caecc7f4"
  integrity sha1-7UQbb6YAByUgzhi0PSyMyMrsx/Q=
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.12.13", "@babel/code-frame@^7.26.2", "@babel/code-frame@^7.27.1":
  version "7.27.1"
  resolved "https://registry-npm-next.huolala.work/@babel/code-frame/-/code-frame-7.27.1.tgz#200f715e66d52a23b221a9435534a91cc13ad5be"
  integrity sha1-IA9xXmbVKiOyIalDVTSpHME61b4=
  dependencies:
    "@babel/helper-validator-identifier" "^7.27.1"
    js-tokens "^4.0.0"
    picocolors "^1.1.1"

"@babel/compat-data@^7.27.2":
  version "7.28.0"
  resolved "https://registry-npm-next.huolala.work/@babel/compat-data/-/compat-data-7.28.0.tgz#9fc6fd58c2a6a15243cd13983224968392070790"
  integrity sha1-n8b9WMKmoVJDzROYMiSWg5IHB5A=

"@babel/core@^7.11.6", "@babel/core@^7.12.3", "@babel/core@^7.23.9", "@babel/core@^7.27.4":
  version "7.28.0"
  resolved "https://registry-npm-next.huolala.work/@babel/core/-/core-7.28.0.tgz#55dad808d5bf3445a108eefc88ea3fdf034749a4"
  integrity sha1-VdrYCNW/NEWhCO78iOo/3wNHSaQ=
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.28.0"
    "@babel/helper-compilation-targets" "^7.27.2"
    "@babel/helper-module-transforms" "^7.27.3"
    "@babel/helpers" "^7.27.6"
    "@babel/parser" "^7.28.0"
    "@babel/template" "^7.27.2"
    "@babel/traverse" "^7.28.0"
    "@babel/types" "^7.28.0"
    convert-source-map "^2.0.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.3"
    semver "^6.3.1"

"@babel/generator@^7.28.0", "@babel/generator@^7.7.2":
  version "7.28.0"
  resolved "https://registry-npm-next.huolala.work/@babel/generator/-/generator-7.28.0.tgz#9cc2f7bd6eb054d77dc66c2664148a0c5118acd2"
  integrity sha1-nML3vW6wVNd9xmwmZBSKDFEYrNI=
  dependencies:
    "@babel/parser" "^7.28.0"
    "@babel/types" "^7.28.0"
    "@jridgewell/gen-mapping" "^0.3.12"
    "@jridgewell/trace-mapping" "^0.3.28"
    jsesc "^3.0.2"

"@babel/helper-compilation-targets@^7.27.2":
  version "7.27.2"
  resolved "https://registry-npm-next.huolala.work/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz#46a0f6efab808d51d29ce96858dd10ce8732733d"
  integrity sha1-RqD276uAjVHSnOloWN0Qzocycz0=
  dependencies:
    "@babel/compat-data" "^7.27.2"
    "@babel/helper-validator-option" "^7.27.1"
    browserslist "^4.24.0"
    lru-cache "^5.1.1"
    semver "^6.3.1"

"@babel/helper-globals@^7.28.0":
  version "7.28.0"
  resolved "https://registry-npm-next.huolala.work/@babel/helper-globals/-/helper-globals-7.28.0.tgz#b9430df2aa4e17bc28665eadeae8aa1d985e6674"
  integrity sha1-uUMN8qpOF7woZl6t6uiqHZheZnQ=

"@babel/helper-module-imports@^7.27.1":
  version "7.27.1"
  resolved "https://registry-npm-next.huolala.work/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz#7ef769a323e2655e126673bb6d2d6913bbead204"
  integrity sha1-fvdpoyPiZV4SZnO7bS1pE7vq0gQ=
  dependencies:
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helper-module-transforms@^7.27.3":
  version "7.27.3"
  resolved "https://registry-npm-next.huolala.work/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz#db0bbcfba5802f9ef7870705a7ef8788508ede02"
  integrity sha1-2wu8+6WAL573hwcFp++HiFCO3gI=
  dependencies:
    "@babel/helper-module-imports" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"
    "@babel/traverse" "^7.27.3"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.27.1", "@babel/helper-plugin-utils@^7.8.0":
  version "7.27.1"
  resolved "https://registry-npm-next.huolala.work/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz#ddb2f876534ff8013e6c2b299bf4d39b3c51d44c"
  integrity sha1-3bL4dlNP+AE+bCspm/TTmzxR1Ew=

"@babel/helper-string-parser@^7.27.1":
  version "7.27.1"
  resolved "https://registry-npm-next.huolala.work/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz#54da796097ab19ce67ed9f88b47bb2ec49367687"
  integrity sha1-VNp5YJerGc5n7Z+ItHuy7Ek2doc=

"@babel/helper-validator-identifier@^7.27.1":
  version "7.27.1"
  resolved "https://registry-npm-next.huolala.work/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz#a7054dcc145a967dd4dc8fee845a57c1316c9df8"
  integrity sha1-pwVNzBRaln3U3I/uhFpXwTFsnfg=

"@babel/helper-validator-option@^7.27.1":
  version "7.27.1"
  resolved "https://registry-npm-next.huolala.work/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz#fa52f5b1e7db1ab049445b421c4471303897702f"
  integrity sha1-+lL1sefbGrBJRFtCHERxMDiXcC8=

"@babel/helpers@^7.27.6":
  version "7.27.6"
  resolved "https://registry-npm-next.huolala.work/@babel/helpers/-/helpers-7.27.6.tgz#6456fed15b2cb669d2d1fabe84b66b34991d812c"
  integrity sha1-ZFb+0VsstmnS0fq+hLZrNJkdgSw=
  dependencies:
    "@babel/template" "^7.27.2"
    "@babel/types" "^7.27.6"

"@babel/parser@^7.1.0", "@babel/parser@^7.14.7", "@babel/parser@^7.20.7", "@babel/parser@^7.23.9", "@babel/parser@^7.27.2", "@babel/parser@^7.28.0":
  version "7.28.0"
  resolved "https://registry-npm-next.huolala.work/@babel/parser/-/parser-7.28.0.tgz#979829fbab51a29e13901e5a80713dbcb840825e"
  integrity sha1-l5gp+6tRop4TkB5agHE9vLhAgl4=
  dependencies:
    "@babel/types" "^7.28.0"

"@babel/plugin-syntax-async-generators@^7.8.4":
  version "7.8.4"
  resolved "https://registry-npm-next.huolala.work/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz#a983fb1aeb2ec3f6ed042a210f640e90e786fe0d"
  integrity sha1-qYP7Gusuw/btBCohD2QOkOeG/g0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-bigint@^7.8.3":
  version "7.8.3"
  resolved "https://registry-npm-next.huolala.work/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.8.3.tgz#4c9a6f669f5d0cdf1b90a1671e9a146be5300cea"
  integrity sha1-TJpvZp9dDN8bkKFnHpoUa+UwDOo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.12.13":
  version "7.12.13"
  resolved "https://registry-npm-next.huolala.work/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz#b5c987274c4a3a82b89714796931a6b53544ae10"
  integrity sha1-tcmHJ0xKOoK4lxR5aTGmtTVErhA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-class-static-block@^7.14.5":
  version "7.14.5"
  resolved "https://registry-npm-next.huolala.work/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz#195df89b146b4b78b3bf897fd7a257c84659d406"
  integrity sha1-GV34mxRrS3izv4l/16JXyEZZ1AY=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-import-attributes@^7.24.7":
  version "7.27.1"
  resolved "https://registry-npm-next.huolala.work/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.27.1.tgz#34c017d54496f9b11b61474e7ea3dfd5563ffe07"
  integrity sha1-NMAX1USW+bEbYUdOfqPf1VY//gc=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-import-meta@^7.10.4":
  version "7.10.4"
  resolved "https://registry-npm-next.huolala.work/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz#ee601348c370fa334d2207be158777496521fd51"
  integrity sha1-7mATSMNw+jNNIge+FYd3SWUh/VE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-json-strings@^7.8.3":
  version "7.8.3"
  resolved "https://registry-npm-next.huolala.work/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz#01ca21b668cd8218c9e640cb6dd88c5412b2c96a"
  integrity sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.7.2":
  version "7.27.1"
  resolved "https://registry-npm-next.huolala.work/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.27.1.tgz#2f9beb5eff30fa507c5532d107daac7b888fa34c"
  integrity sha1-L5vrXv8w+lB8VTLRB9qse4iPo0w=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-logical-assignment-operators@^7.10.4":
  version "7.10.4"
  resolved "https://registry-npm-next.huolala.work/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz#ca91ef46303530448b906652bac2e9fe9941f699"
  integrity sha1-ypHvRjA1MESLkGZSusLp/plB9pk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  version "7.8.3"
  resolved "https://registry-npm-next.huolala.work/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz#167ed70368886081f74b5c36c65a88c03b66d1a9"
  integrity sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.10.4":
  version "7.10.4"
  resolved "https://registry-npm-next.huolala.work/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz#b9b070b3e33570cd9fd07ba7fa91c0dd37b9af97"
  integrity sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.8.3":
  version "7.8.3"
  resolved "https://registry-npm-next.huolala.work/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz#60e225edcbd98a640332a2e72dd3e66f1af55871"
  integrity sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  version "7.8.3"
  resolved "https://registry-npm-next.huolala.work/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz#6111a265bcfb020eb9efd0fdfd7d26402b9ed6c1"
  integrity sha1-YRGiZbz7Ag6579D9/X0mQCue1sE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.3":
  version "7.8.3"
  resolved "https://registry-npm-next.huolala.work/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz#4f69c2ab95167e0180cd5336613f8c5788f7d48a"
  integrity sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-private-property-in-object@^7.14.5":
  version "7.14.5"
  resolved "https://registry-npm-next.huolala.work/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz#0dc6671ec0ea22b6e94a1114f857970cd39de1ad"
  integrity sha1-DcZnHsDqIrbpShEU+FeXDNOd4a0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-top-level-await@^7.14.5":
  version "7.14.5"
  resolved "https://registry-npm-next.huolala.work/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz#c1cfdadc35a646240001f06138247b741c34d94c"
  integrity sha1-wc/a3DWmRiQAAfBhOCR7dBw02Uw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-typescript@^7.7.2":
  version "7.27.1"
  resolved "https://registry-npm-next.huolala.work/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.27.1.tgz#5147d29066a793450f220c63fa3a9431b7e6dd18"
  integrity sha1-UUfSkGank0UPIgxj+jqUMbfm3Rg=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-react-jsx-self@^7.27.1":
  version "7.27.1"
  resolved "https://registry-npm-next.huolala.work/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.27.1.tgz#af678d8506acf52c577cac73ff7fe6615c85fc92"
  integrity sha1-r2eNhQas9SxXfKxz/3/mYVyF/JI=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-react-jsx-source@^7.27.1":
  version "7.27.1"
  resolved "https://registry-npm-next.huolala.work/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.27.1.tgz#dcfe2c24094bb757bf73960374e7c55e434f19f0"
  integrity sha1-3P4sJAlLt1e/c5YDdOfFXkNPGfA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/template@^7.27.2", "@babel/template@^7.3.3":
  version "7.27.2"
  resolved "https://registry-npm-next.huolala.work/@babel/template/-/template-7.27.2.tgz#fa78ceed3c4e7b63ebf6cb39e5852fca45f6809d"
  integrity sha1-+njO7TxOe2Pr9ss55YUvykX2gJ0=
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/parser" "^7.27.2"
    "@babel/types" "^7.27.1"

"@babel/traverse@^7.27.1", "@babel/traverse@^7.27.3", "@babel/traverse@^7.28.0":
  version "7.28.0"
  resolved "https://registry-npm-next.huolala.work/@babel/traverse/-/traverse-7.28.0.tgz#518aa113359b062042379e333db18380b537e34b"
  integrity sha1-UYqhEzWbBiBCN54zPbGDgLU340s=
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.28.0"
    "@babel/helper-globals" "^7.28.0"
    "@babel/parser" "^7.28.0"
    "@babel/template" "^7.27.2"
    "@babel/types" "^7.28.0"
    debug "^4.3.1"

"@babel/types@^7.0.0", "@babel/types@^7.20.7", "@babel/types@^7.27.1", "@babel/types@^7.27.6", "@babel/types@^7.28.0", "@babel/types@^7.3.3":
  version "7.28.1"
  resolved "https://registry-npm-next.huolala.work/@babel/types/-/types-7.28.1.tgz#2aaf3c10b31ba03a77ac84f52b3912a0edef4cf9"
  integrity sha1-Kq88ELMboDp3rIT1KzkSoO3vTPk=
  dependencies:
    "@babel/helper-string-parser" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"

"@bcoe/v8-coverage@^0.2.3":
  version "0.2.3"
  resolved "https://registry-npm-next.huolala.work/@bcoe/v8-coverage/-/v8-coverage-0.2.3.tgz#75a2e8b51cb758a7553d6804a5932d7aace75c39"
  integrity sha1-daLotRy3WKdVPWgEpZMteqznXDk=

"@esbuild/aix-ppc64@0.25.6":
  version "0.25.6"
  resolved "https://registry-npm-next.huolala.work/@esbuild/aix-ppc64/-/aix-ppc64-0.25.6.tgz#164b19122e2ed54f85469df9dea98ddb01d5e79e"
  integrity sha1-FksZEi4u1U+FRp353qmN2wHV554=

"@esbuild/android-arm64@0.25.6":
  version "0.25.6"
  resolved "https://registry-npm-next.huolala.work/@esbuild/android-arm64/-/android-arm64-0.25.6.tgz#8f539e7def848f764f6432598e51cc3820fde3a5"
  integrity sha1-j1Oefe+Ej3ZPZDJZjlHMOCD946U=

"@esbuild/android-arm@0.25.6":
  version "0.25.6"
  resolved "https://registry-npm-next.huolala.work/@esbuild/android-arm/-/android-arm-0.25.6.tgz#4ceb0f40113e9861169be83e2a670c260dd234ff"
  integrity sha1-TOsPQBE+mGEWm+g+KmcMJg3SNP8=

"@esbuild/android-x64@0.25.6":
  version "0.25.6"
  resolved "https://registry-npm-next.huolala.work/@esbuild/android-x64/-/android-x64-0.25.6.tgz#ad4f280057622c25fe985c08999443a195dc63a8"
  integrity sha1-rU8oAFdiLCX+mFwImZRDoZXcY6g=

"@esbuild/darwin-arm64@0.25.6":
  version "0.25.6"
  resolved "https://registry-npm-next.huolala.work/@esbuild/darwin-arm64/-/darwin-arm64-0.25.6.tgz#d1f04027396b3d6afc96bacd0d13167dfd9f01f7"
  integrity sha1-0fBAJzlrPWr8lrrNDRMWff2fAfc=

"@esbuild/darwin-x64@0.25.6":
  version "0.25.6"
  resolved "https://registry-npm-next.huolala.work/@esbuild/darwin-x64/-/darwin-x64-0.25.6.tgz#2b4a6cedb799f635758d7832d75b23772c8ef68f"
  integrity sha1-K0ps7beZ9jV1jXgy11sjdyyO9o8=

"@esbuild/freebsd-arm64@0.25.6":
  version "0.25.6"
  resolved "https://registry-npm-next.huolala.work/@esbuild/freebsd-arm64/-/freebsd-arm64-0.25.6.tgz#a26266cc97dd78dc3c3f3d6788b1b83697b1055d"
  integrity sha1-omJmzJfdeNw8Pz1niLG4NpexBV0=

"@esbuild/freebsd-x64@0.25.6":
  version "0.25.6"
  resolved "https://registry-npm-next.huolala.work/@esbuild/freebsd-x64/-/freebsd-x64-0.25.6.tgz#9feb8e826735c568ebfd94859b22a3fbb6a9bdd2"
  integrity sha1-n+uOgmc1xWjr/ZSFmyKj+7apvdI=

"@esbuild/linux-arm64@0.25.6":
  version "0.25.6"
  resolved "https://registry-npm-next.huolala.work/@esbuild/linux-arm64/-/linux-arm64-0.25.6.tgz#c07cbed8e249f4c28e7f32781d36fc4695293d28"
  integrity sha1-wHy+2OJJ9MKOfzJ4HTb8RpUpPSg=

"@esbuild/linux-arm@0.25.6":
  version "0.25.6"
  resolved "https://registry-npm-next.huolala.work/@esbuild/linux-arm/-/linux-arm-0.25.6.tgz#d6e2cd8ef3196468065d41f13fa2a61aaa72644a"
  integrity sha1-1uLNjvMZZGgGXUHxP6KmGqpyZEo=

"@esbuild/linux-ia32@0.25.6":
  version "0.25.6"
  resolved "https://registry-npm-next.huolala.work/@esbuild/linux-ia32/-/linux-ia32-0.25.6.tgz#3e682bd47c4eddcc4b8f1393dfc8222482f17997"
  integrity sha1-Pmgr1HxO3cxLjxOT38giJILxeZc=

"@esbuild/linux-loong64@0.25.6":
  version "0.25.6"
  resolved "https://registry-npm-next.huolala.work/@esbuild/linux-loong64/-/linux-loong64-0.25.6.tgz#473f5ea2e52399c08ad4cd6b12e6dbcddd630f05"
  integrity sha1-Rz9eouUjmcCK1M1rEubbzd1jDwU=

"@esbuild/linux-mips64el@0.25.6":
  version "0.25.6"
  resolved "https://registry-npm-next.huolala.work/@esbuild/linux-mips64el/-/linux-mips64el-0.25.6.tgz#9960631c9fd61605b0939c19043acf4ef2b51718"
  integrity sha1-mWBjHJ/WFgWwk5wZBDrPTvK1Fxg=

"@esbuild/linux-ppc64@0.25.6":
  version "0.25.6"
  resolved "https://registry-npm-next.huolala.work/@esbuild/linux-ppc64/-/linux-ppc64-0.25.6.tgz#477cbf8bb04aa034b94f362c32c86b5c31db8d3e"
  integrity sha1-R3y/i7BKoDS5TzYsMshrXDHbjT4=

"@esbuild/linux-riscv64@0.25.6":
  version "0.25.6"
  resolved "https://registry-npm-next.huolala.work/@esbuild/linux-riscv64/-/linux-riscv64-0.25.6.tgz#bcdb46c8fb8e93aa779e9a0a62cd4ac00dcac626"
  integrity sha1-vNtGyPuOk6p3npoKYs1KwA3KxiY=

"@esbuild/linux-s390x@0.25.6":
  version "0.25.6"
  resolved "https://registry-npm-next.huolala.work/@esbuild/linux-s390x/-/linux-s390x-0.25.6.tgz#f412cf5fdf0aea849ff51c73fd817c6c0234d46d"
  integrity sha1-9BLPX98K6oSf9Rxz/YF8bAI01G0=

"@esbuild/linux-x64@0.25.6":
  version "0.25.6"
  resolved "https://registry-npm-next.huolala.work/@esbuild/linux-x64/-/linux-x64-0.25.6.tgz#d8233c09b5ebc0c855712dc5eeb835a3a3341108"
  integrity sha1-2CM8CbXrwMhVcS3F7rg1o6M0EQg=

"@esbuild/netbsd-arm64@0.25.6":
  version "0.25.6"
  resolved "https://registry-npm-next.huolala.work/@esbuild/netbsd-arm64/-/netbsd-arm64-0.25.6.tgz#f51ae8dd1474172e73cf9cbaf8a38d1c72dd8f1a"
  integrity sha1-9Rro3RR0Fy5zz5y6+KONHHLdjxo=

"@esbuild/netbsd-x64@0.25.6":
  version "0.25.6"
  resolved "https://registry-npm-next.huolala.work/@esbuild/netbsd-x64/-/netbsd-x64-0.25.6.tgz#a267538602c0e50a858cf41dcfe5d8036f8da8e7"
  integrity sha1-omdThgLA5QqFjPQdz+XYA2+NqOc=

"@esbuild/openbsd-arm64@0.25.6":
  version "0.25.6"
  resolved "https://registry-npm-next.huolala.work/@esbuild/openbsd-arm64/-/openbsd-arm64-0.25.6.tgz#a51be60c425b85c216479b8c344ad0511635f2d2"
  integrity sha1-pRvmDEJbhcIWR5uMNErQURY18tI=

"@esbuild/openbsd-x64@0.25.6":
  version "0.25.6"
  resolved "https://registry-npm-next.huolala.work/@esbuild/openbsd-x64/-/openbsd-x64-0.25.6.tgz#7e4a743c73f75562e29223ba69d0be6c9c9008da"
  integrity sha1-fkp0PHP3VWLikiO6adC+bJyQCNo=

"@esbuild/openharmony-arm64@0.25.6":
  version "0.25.6"
  resolved "https://registry-npm-next.huolala.work/@esbuild/openharmony-arm64/-/openharmony-arm64-0.25.6.tgz#2087a5028f387879154ebf44bdedfafa17682e5b"
  integrity sha1-IIelAo84eHkVTr9Eve36+hdoLls=

"@esbuild/sunos-x64@0.25.6":
  version "0.25.6"
  resolved "https://registry-npm-next.huolala.work/@esbuild/sunos-x64/-/sunos-x64-0.25.6.tgz#56531f861723ea0dc6283a2bb8837304223cb736"
  integrity sha1-VlMfhhcj6g3GKDoruINzBCI8tzY=

"@esbuild/win32-arm64@0.25.6":
  version "0.25.6"
  resolved "https://registry-npm-next.huolala.work/@esbuild/win32-arm64/-/win32-arm64-0.25.6.tgz#f4989f033deac6fae323acff58764fa8bc01436e"
  integrity sha1-9JifAz3qxvrjI6z/WHZPqLwBQ24=

"@esbuild/win32-ia32@0.25.6":
  version "0.25.6"
  resolved "https://registry-npm-next.huolala.work/@esbuild/win32-ia32/-/win32-ia32-0.25.6.tgz#b260e9df71e3939eb33925076d39f63cec7d1525"
  integrity sha1-smDp33Hjk56zOSUHbTn2POx9FSU=

"@esbuild/win32-x64@0.25.6":
  version "0.25.6"
  resolved "https://registry-npm-next.huolala.work/@esbuild/win32-x64/-/win32-x64-0.25.6.tgz#4276edd5c105bc28b11c6a1f76fb9d29d1bd25c1"
  integrity sha1-Qnbt1cEFvCixHGofdvudKdG9JcE=

"@eslint-community/eslint-utils@^4.2.0", "@eslint-community/eslint-utils@^4.7.0":
  version "4.7.0"
  resolved "https://registry-npm-next.huolala.work/@eslint-community/eslint-utils/-/eslint-utils-4.7.0.tgz#607084630c6c033992a082de6e6fbc1a8b52175a"
  integrity sha1-YHCEYwxsAzmSoILebm+8GotSF1o=
  dependencies:
    eslint-visitor-keys "^3.4.3"

"@eslint-community/regexpp@^4.10.0", "@eslint-community/regexpp@^4.12.1":
  version "4.12.1"
  resolved "https://registry-npm-next.huolala.work/@eslint-community/regexpp/-/regexpp-4.12.1.tgz#cfc6cffe39df390a3841cde2abccf92eaa7ae0e0"
  integrity sha1-z8bP/jnfOQo4Qc3iq8z5Lqp64OA=

"@eslint/config-array@^0.21.0":
  version "0.21.0"
  resolved "https://registry-npm-next.huolala.work/@eslint/config-array/-/config-array-0.21.0.tgz#abdbcbd16b124c638081766392a4d6b509f72636"
  integrity sha1-q9vL0WsSTGOAgXZjkqTWtQn3JjY=
  dependencies:
    "@eslint/object-schema" "^2.1.6"
    debug "^4.3.1"
    minimatch "^3.1.2"

"@eslint/config-helpers@^0.3.0":
  version "0.3.0"
  resolved "https://registry-npm-next.huolala.work/@eslint/config-helpers/-/config-helpers-0.3.0.tgz#3e09a90dfb87e0005c7694791e58e97077271286"
  integrity sha1-PgmpDfuH4ABcdpR5HljpcHcnEoY=

"@eslint/core@^0.15.0", "@eslint/core@^0.15.1":
  version "0.15.1"
  resolved "https://registry-npm-next.huolala.work/@eslint/core/-/core-0.15.1.tgz#d530d44209cbfe2f82ef86d6ba08760196dd3b60"
  integrity sha1-1TDUQgnL/i+C74bWugh2AZbdO2A=
  dependencies:
    "@types/json-schema" "^7.0.15"

"@eslint/eslintrc@^3.3.1":
  version "3.3.1"
  resolved "https://registry-npm-next.huolala.work/@eslint/eslintrc/-/eslintrc-3.3.1.tgz#e55f7f1dd400600dd066dbba349c4c0bac916964"
  integrity sha1-5V9/HdQAYA3QZtu6NJxMC6yRaWQ=
  dependencies:
    ajv "^6.12.4"
    debug "^4.3.2"
    espree "^10.0.1"
    globals "^14.0.0"
    ignore "^5.2.0"
    import-fresh "^3.2.1"
    js-yaml "^4.1.0"
    minimatch "^3.1.2"
    strip-json-comments "^3.1.1"

"@eslint/js@9.31.0", "@eslint/js@^9.25.0":
  version "9.31.0"
  resolved "https://registry-npm-next.huolala.work/@eslint/js/-/js-9.31.0.tgz#adb1f39953d8c475c4384b67b67541b0d7206ed8"
  integrity sha1-rbHzmVPYxHXEOEtntnVBsNcgbtg=

"@eslint/object-schema@^2.1.6":
  version "2.1.6"
  resolved "https://registry-npm-next.huolala.work/@eslint/object-schema/-/object-schema-2.1.6.tgz#58369ab5b5b3ca117880c0f6c0b0f32f6950f24f"
  integrity sha1-WDaatbWzyhF4gMD2wLDzL2lQ8k8=

"@eslint/plugin-kit@^0.3.1":
  version "0.3.3"
  resolved "https://registry-npm-next.huolala.work/@eslint/plugin-kit/-/plugin-kit-0.3.3.tgz#32926b59bd407d58d817941e48b2a7049359b1fd"
  integrity sha1-MpJrWb1AfVjYF5QeSLKnBJNZsf0=
  dependencies:
    "@eslint/core" "^0.15.1"
    levn "^0.4.1"

"@hll/use-event-listener@>=0.0.1", "@hll/use-event-listener@^0.0.1":
  version "0.0.1"
  resolved "https://registry-npm-next.huolala.work/@hll/use-event-listener/-/use-event-listener-0.0.1.tgz#1ff44cff9f78be25d78e09959d2401e9cd2846a3"
  integrity sha1-H/RM/594viXXjgmVnSQB6c0oRqM=

"@hll/use-slot@^0.0.6":
  version "0.0.6"
  resolved "https://registry-npm-next.huolala.work/@hll/use-slot/-/use-slot-0.0.6.tgz#64ba60b16453c55435a218e58d0cd3530ae6fb8a"
  integrity sha1-ZLpgsWRTxVQ1ohjljQzTUwrm+4o=

"@hll/vite-plugin-van@^0.0.9":
  version "0.0.9"
  resolved "https://registry-npm-next.huolala.work/@hll/vite-plugin-van/-/vite-plugin-van-0.0.9.tgz#de0e4403a786c7dc3fa6ae57f337b87bb52aac8b"
  integrity sha1-3g5EA6eGx9w/pq5X8ze4e7UqrIs=
  dependencies:
    chalk "^4.0.0"
    globby "^11.0.0"

"@humanfs/core@^0.19.1":
  version "0.19.1"
  resolved "https://registry-npm-next.huolala.work/@humanfs/core/-/core-0.19.1.tgz#17c55ca7d426733fe3c561906b8173c336b40a77"
  integrity sha1-F8Vcp9Qmcz/jxWGQa4Fzwza0Cnc=

"@humanfs/node@^0.16.6":
  version "0.16.6"
  resolved "https://registry-npm-next.huolala.work/@humanfs/node/-/node-0.16.6.tgz#ee2a10eaabd1131987bf0488fd9b820174cd765e"
  integrity sha1-7ioQ6qvRExmHvwSI/ZuCAXTNdl4=
  dependencies:
    "@humanfs/core" "^0.19.1"
    "@humanwhocodes/retry" "^0.3.0"

"@humanwhocodes/module-importer@^1.0.1":
  version "1.0.1"
  resolved "https://registry-npm-next.huolala.work/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz#af5b2691a22b44be847b0ca81641c5fb6ad0172c"
  integrity sha1-r1smkaIrRL6EewyoFkHF+2rQFyw=

"@humanwhocodes/retry@^0.3.0":
  version "0.3.1"
  resolved "https://registry-npm-next.huolala.work/@humanwhocodes/retry/-/retry-0.3.1.tgz#c72a5c76a9fbaf3488e231b13dc52c0da7bab42a"
  integrity sha1-xypcdqn7rzSI4jGxPcUsDae6tCo=

"@humanwhocodes/retry@^0.4.2":
  version "0.4.3"
  resolved "https://registry-npm-next.huolala.work/@humanwhocodes/retry/-/retry-0.4.3.tgz#c2b9d2e374ee62c586d3adbea87199b1d7a7a6ba"
  integrity sha1-wrnS43TuYsWG062+qHGZsdenpro=

"@istanbuljs/load-nyc-config@^1.0.0":
  version "1.1.0"
  resolved "https://registry-npm-next.huolala.work/@istanbuljs/load-nyc-config/-/load-nyc-config-1.1.0.tgz#fd3db1d59ecf7cf121e80650bb86712f9b55eced"
  integrity sha1-/T2x1Z7PfPEh6AZQu4ZxL5tV7O0=
  dependencies:
    camelcase "^5.3.1"
    find-up "^4.1.0"
    get-package-type "^0.1.0"
    js-yaml "^3.13.1"
    resolve-from "^5.0.0"

"@istanbuljs/schema@^0.1.2", "@istanbuljs/schema@^0.1.3":
  version "0.1.3"
  resolved "https://registry-npm-next.huolala.work/@istanbuljs/schema/-/schema-0.1.3.tgz#e45e384e4b8ec16bce2fd903af78450f6bf7ec98"
  integrity sha1-5F44TkuOwWvOL9kDr3hFD2v37Jg=

"@jest/console@^29.7.0":
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/@jest/console/-/console-29.7.0.tgz#cd4822dbdb84529265c5a2bdb529a3c9cc950ffc"
  integrity sha1-zUgi29uEUpJlxaK9tSmjycyVD/w=
  dependencies:
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    chalk "^4.0.0"
    jest-message-util "^29.7.0"
    jest-util "^29.7.0"
    slash "^3.0.0"

"@jest/core@^29.7.0":
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/@jest/core/-/core-29.7.0.tgz#b6cccc239f30ff36609658c5a5e2291757ce448f"
  integrity sha1-tszMI58w/zZglljFpeIpF1fORI8=
  dependencies:
    "@jest/console" "^29.7.0"
    "@jest/reporters" "^29.7.0"
    "@jest/test-result" "^29.7.0"
    "@jest/transform" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    ansi-escapes "^4.2.1"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    exit "^0.1.2"
    graceful-fs "^4.2.9"
    jest-changed-files "^29.7.0"
    jest-config "^29.7.0"
    jest-haste-map "^29.7.0"
    jest-message-util "^29.7.0"
    jest-regex-util "^29.6.3"
    jest-resolve "^29.7.0"
    jest-resolve-dependencies "^29.7.0"
    jest-runner "^29.7.0"
    jest-runtime "^29.7.0"
    jest-snapshot "^29.7.0"
    jest-util "^29.7.0"
    jest-validate "^29.7.0"
    jest-watcher "^29.7.0"
    micromatch "^4.0.4"
    pretty-format "^29.7.0"
    slash "^3.0.0"
    strip-ansi "^6.0.0"

"@jest/environment@^29.7.0":
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/@jest/environment/-/environment-29.7.0.tgz#24d61f54ff1f786f3cd4073b4b94416383baf2a7"
  integrity sha1-JNYfVP8feG881Ac7S5RBY4O68qc=
  dependencies:
    "@jest/fake-timers" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    jest-mock "^29.7.0"

"@jest/expect-utils@^29.7.0":
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/@jest/expect-utils/-/expect-utils-29.7.0.tgz#023efe5d26a8a70f21677d0a1afc0f0a44e3a1c6"
  integrity sha1-Aj7+XSaopw8hZ30KGvwPCkTjocY=
  dependencies:
    jest-get-type "^29.6.3"

"@jest/expect@^29.7.0":
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/@jest/expect/-/expect-29.7.0.tgz#76a3edb0cb753b70dfbfe23283510d3d45432bf2"
  integrity sha1-dqPtsMt1O3Dfv+Iyg1ENPUVDK/I=
  dependencies:
    expect "^29.7.0"
    jest-snapshot "^29.7.0"

"@jest/fake-timers@^29.7.0":
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/@jest/fake-timers/-/fake-timers-29.7.0.tgz#fd91bf1fffb16d7d0d24a426ab1a47a49881a565"
  integrity sha1-/ZG/H/+xbX0NJKQmqxpHpJiBpWU=
  dependencies:
    "@jest/types" "^29.6.3"
    "@sinonjs/fake-timers" "^10.0.2"
    "@types/node" "*"
    jest-message-util "^29.7.0"
    jest-mock "^29.7.0"
    jest-util "^29.7.0"

"@jest/globals@^29.7.0":
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/@jest/globals/-/globals-29.7.0.tgz#8d9290f9ec47ff772607fa864ca1d5a2efae1d4d"
  integrity sha1-jZKQ+exH/3cmB/qGTKHVou+uHU0=
  dependencies:
    "@jest/environment" "^29.7.0"
    "@jest/expect" "^29.7.0"
    "@jest/types" "^29.6.3"
    jest-mock "^29.7.0"

"@jest/reporters@^29.7.0":
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/@jest/reporters/-/reporters-29.7.0.tgz#04b262ecb3b8faa83b0b3d321623972393e8f4c7"
  integrity sha1-BLJi7LO4+qg7Cz0yFiOXI5Po9Mc=
  dependencies:
    "@bcoe/v8-coverage" "^0.2.3"
    "@jest/console" "^29.7.0"
    "@jest/test-result" "^29.7.0"
    "@jest/transform" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@jridgewell/trace-mapping" "^0.3.18"
    "@types/node" "*"
    chalk "^4.0.0"
    collect-v8-coverage "^1.0.0"
    exit "^0.1.2"
    glob "^7.1.3"
    graceful-fs "^4.2.9"
    istanbul-lib-coverage "^3.0.0"
    istanbul-lib-instrument "^6.0.0"
    istanbul-lib-report "^3.0.0"
    istanbul-lib-source-maps "^4.0.0"
    istanbul-reports "^3.1.3"
    jest-message-util "^29.7.0"
    jest-util "^29.7.0"
    jest-worker "^29.7.0"
    slash "^3.0.0"
    string-length "^4.0.1"
    strip-ansi "^6.0.0"
    v8-to-istanbul "^9.0.1"

"@jest/schemas@^29.6.3":
  version "29.6.3"
  resolved "https://registry-npm-next.huolala.work/@jest/schemas/-/schemas-29.6.3.tgz#430b5ce8a4e0044a7e3819663305a7b3091c8e03"
  integrity sha1-Qwtc6KTgBEp+OBlmMwWnswkcjgM=
  dependencies:
    "@sinclair/typebox" "^0.27.8"

"@jest/source-map@^29.6.3":
  version "29.6.3"
  resolved "https://registry-npm-next.huolala.work/@jest/source-map/-/source-map-29.6.3.tgz#d90ba772095cf37a34a5eb9413f1b562a08554c4"
  integrity sha1-2Quncglc83o0peuUE/G1YqCFVMQ=
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.18"
    callsites "^3.0.0"
    graceful-fs "^4.2.9"

"@jest/test-result@^29.7.0":
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/@jest/test-result/-/test-result-29.7.0.tgz#8db9a80aa1a097bb2262572686734baed9b1657c"
  integrity sha1-jbmoCqGgl7siYlcmhnNLrtmxZXw=
  dependencies:
    "@jest/console" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/istanbul-lib-coverage" "^2.0.0"
    collect-v8-coverage "^1.0.0"

"@jest/test-sequencer@^29.7.0":
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/@jest/test-sequencer/-/test-sequencer-29.7.0.tgz#6cef977ce1d39834a3aea887a1726628a6f072ce"
  integrity sha1-bO+XfOHTmDSjrqiHoXJmKKbwcs4=
  dependencies:
    "@jest/test-result" "^29.7.0"
    graceful-fs "^4.2.9"
    jest-haste-map "^29.7.0"
    slash "^3.0.0"

"@jest/transform@^29.7.0":
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/@jest/transform/-/transform-29.7.0.tgz#df2dd9c346c7d7768b8a06639994640c642e284c"
  integrity sha1-3y3Zw0bH13aLigZjmZRkDGQuKEw=
  dependencies:
    "@babel/core" "^7.11.6"
    "@jest/types" "^29.6.3"
    "@jridgewell/trace-mapping" "^0.3.18"
    babel-plugin-istanbul "^6.1.1"
    chalk "^4.0.0"
    convert-source-map "^2.0.0"
    fast-json-stable-stringify "^2.1.0"
    graceful-fs "^4.2.9"
    jest-haste-map "^29.7.0"
    jest-regex-util "^29.6.3"
    jest-util "^29.7.0"
    micromatch "^4.0.4"
    pirates "^4.0.4"
    slash "^3.0.0"
    write-file-atomic "^4.0.2"

"@jest/types@^29.6.3":
  version "29.6.3"
  resolved "https://registry-npm-next.huolala.work/@jest/types/-/types-29.6.3.tgz#1131f8cf634e7e84c5e77bab12f052af585fba59"
  integrity sha1-ETH4z2NOfoTF53urEvBSr1hfulk=
  dependencies:
    "@jest/schemas" "^29.6.3"
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^3.0.0"
    "@types/node" "*"
    "@types/yargs" "^17.0.8"
    chalk "^4.0.0"

"@jridgewell/gen-mapping@^0.3.12", "@jridgewell/gen-mapping@^0.3.5":
  version "0.3.12"
  resolved "https://registry-npm-next.huolala.work/@jridgewell/gen-mapping/-/gen-mapping-0.3.12.tgz#2234ce26c62889f03db3d7fea43c1932ab3e927b"
  integrity sha1-IjTOJsYoifA9s9f+pDwZMqs+kns=
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.5.0"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.1.0":
  version "3.1.2"
  resolved "https://registry-npm-next.huolala.work/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz#7a0ee601f60f99a20c7c7c5ff0c80388c1189bd6"
  integrity sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y=

"@jridgewell/source-map@^0.3.3":
  version "0.3.10"
  resolved "https://registry-npm-next.huolala.work/@jridgewell/source-map/-/source-map-0.3.10.tgz#a35714446a2e84503ff9bfe66f1d1d4846f2075b"
  integrity sha1-o1cURGouhFA/+b/mbx0dSEbyB1s=
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"

"@jridgewell/sourcemap-codec@^1.4.14", "@jridgewell/sourcemap-codec@^1.5.0":
  version "1.5.4"
  resolved "https://registry-npm-next.huolala.work/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.4.tgz#7358043433b2e5da569aa02cbc4c121da3af27d7"
  integrity sha1-c1gENDOy5dpWmqAsvEwSHaOvJ9c=

"@jridgewell/trace-mapping@^0.3.12", "@jridgewell/trace-mapping@^0.3.18", "@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.25", "@jridgewell/trace-mapping@^0.3.28":
  version "0.3.29"
  resolved "https://registry-npm-next.huolala.work/@jridgewell/trace-mapping/-/trace-mapping-0.3.29.tgz#a58d31eaadaf92c6695680b2e1d464a9b8fbf7fc"
  integrity sha1-pY0x6q2vksZpVoCy4dRkqbj79/w=
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@monaco-editor/loader@^1.5.0":
  version "1.5.0"
  resolved "https://registry-npm-next.huolala.work/@monaco-editor/loader/-/loader-1.5.0.tgz#dcdbc7fe7e905690fb449bed1c251769f325c55d"
  integrity sha1-3NvH/n6QVpD7RJvtHCUXafMlxV0=
  dependencies:
    state-local "^1.0.6"

"@monaco-editor/react@^4.7.0":
  version "4.7.0"
  resolved "https://registry-npm-next.huolala.work/@monaco-editor/react/-/react-4.7.0.tgz#35a1ec01bfe729f38bfc025df7b7bac145602a60"
  integrity sha1-NaHsAb/nKfOL/AJd97e6wUVgKmA=
  dependencies:
    "@monaco-editor/loader" "^1.5.0"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "https://registry-npm-next.huolala.work/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz#7619c2eb21b25483f6d167548b4cfd5a7488c3d5"
  integrity sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.5", "@nodelib/fs.stat@^2.0.2":
  version "2.0.5"
  resolved "https://registry-npm-next.huolala.work/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz#5bd262af94e9d25bd1e71b05deed44876a222e8b"
  integrity sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=

"@nodelib/fs.walk@^1.2.3":
  version "1.2.8"
  resolved "https://registry-npm-next.huolala.work/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz#e95737e8bb6746ddedf69c556953494f196fe69a"
  integrity sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@rolldown/pluginutils@1.0.0-beta.19":
  version "1.0.0-beta.19"
  resolved "https://registry-npm-next.huolala.work/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.19.tgz#fc3b95145a8e7a3bf92754269d8e4f40eea8a244"
  integrity sha1-/DuVFFqOejv5J1QmnY5PQO6ookQ=

"@rollup/plugin-commonjs@^25.0.0":
  version "25.0.8"
  resolved "https://registry-npm-next.huolala.work/@rollup/plugin-commonjs/-/plugin-commonjs-25.0.8.tgz#c77e608ab112a666b7f2a6bea625c73224f7dd34"
  integrity sha1-x35girESpma38qa+piXHMiT33TQ=
  dependencies:
    "@rollup/pluginutils" "^5.0.1"
    commondir "^1.0.1"
    estree-walker "^2.0.2"
    glob "^8.0.3"
    is-reference "1.2.1"
    magic-string "^0.30.3"

"@rollup/plugin-node-resolve@^15.0.0":
  version "15.3.1"
  resolved "https://registry-npm-next.huolala.work/@rollup/plugin-node-resolve/-/plugin-node-resolve-15.3.1.tgz#66008953c2524be786aa319d49e32f2128296a78"
  integrity sha1-ZgCJU8JSS+eGqjGdSeMvISgpang=
  dependencies:
    "@rollup/pluginutils" "^5.0.1"
    "@types/resolve" "1.20.2"
    deepmerge "^4.2.2"
    is-module "^1.0.0"
    resolve "^1.22.1"

"@rollup/plugin-terser@latest":
  version "0.4.4"
  resolved "https://registry-npm-next.huolala.work/@rollup/plugin-terser/-/plugin-terser-0.4.4.tgz#15dffdb3f73f121aa4fbb37e7ca6be9aeea91962"
  integrity sha1-Fd/9s/c/Ehqk+7N+fKa+mu6pGWI=
  dependencies:
    serialize-javascript "^6.0.1"
    smob "^1.0.0"
    terser "^5.17.4"

"@rollup/plugin-typescript@^11.0.0":
  version "11.1.6"
  resolved "https://registry-npm-next.huolala.work/@rollup/plugin-typescript/-/plugin-typescript-11.1.6.tgz#724237d5ec12609ec01429f619d2a3e7d4d1b22b"
  integrity sha1-ckI31ewSYJ7AFCn2GdKj59TRsis=
  dependencies:
    "@rollup/pluginutils" "^5.1.0"
    resolve "^1.22.1"

"@rollup/pluginutils@^5.0.1", "@rollup/pluginutils@^5.1.0":
  version "5.2.0"
  resolved "https://registry-npm-next.huolala.work/@rollup/pluginutils/-/pluginutils-5.2.0.tgz#eac25ca5b0bdda4ba735ddaca5fbf26bd435f602"
  integrity sha1-6sJcpbC92kunNd2spfvya9Q19gI=
  dependencies:
    "@types/estree" "^1.0.0"
    estree-walker "^2.0.2"
    picomatch "^4.0.2"

"@rollup/rollup-android-arm-eabi@4.45.0":
  version "4.45.0"
  resolved "https://registry-npm-next.huolala.work/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.45.0.tgz#0592252f7550bc0ea0474bb5a22430850f92bdbd"
  integrity sha1-BZIlL3VQvA6gR0u1oiQwhQ+Svb0=

"@rollup/rollup-android-arm64@4.45.0":
  version "4.45.0"
  resolved "https://registry-npm-next.huolala.work/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.45.0.tgz#00a51d1d4380cc677da80ac9da1a19e7806bf57e"
  integrity sha1-AKUdHUOAzGd9qArJ2hoZ54Br9X4=

"@rollup/rollup-darwin-arm64@4.45.0":
  version "4.45.0"
  resolved "https://registry-npm-next.huolala.work/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.45.0.tgz#6638299dd282ebde1ebdf7dc5b0f150aa6e256e5"
  integrity sha1-ZjgpndKC694evffcWw8VCqbiVuU=

"@rollup/rollup-darwin-x64@4.45.0":
  version "4.45.0"
  resolved "https://registry-npm-next.huolala.work/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.45.0.tgz#33e61daa0a66890059648feda78e1075d4ea1bcb"
  integrity sha1-M+YdqgpmiQBZZI/tp44QddTqG8s=

"@rollup/rollup-freebsd-arm64@4.45.0":
  version "4.45.0"
  resolved "https://registry-npm-next.huolala.work/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.45.0.tgz#2cc4bd3ba7026cd5374e902285ce76e8fae0f6eb"
  integrity sha1-LMS9O6cCbNU3TpAihc526Prg9us=

"@rollup/rollup-freebsd-x64@4.45.0":
  version "4.45.0"
  resolved "https://registry-npm-next.huolala.work/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.45.0.tgz#64664ba3015deac473a5d6d6c60c068f274bf2d5"
  integrity sha1-ZGZLowFd6sRzpdbWxgwGjydL8tU=

"@rollup/rollup-linux-arm-gnueabihf@4.45.0":
  version "4.45.0"
  resolved "https://registry-npm-next.huolala.work/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.45.0.tgz#7ab16acae3bcae863e9a9bc32038cd05e794a0ff"
  integrity sha1-erFqyuO8roY+mpvDIDjNBeeUoP8=

"@rollup/rollup-linux-arm-musleabihf@4.45.0":
  version "4.45.0"
  resolved "https://registry-npm-next.huolala.work/@rollup/rollup-linux-arm-musleabihf/-/rollup-linux-arm-musleabihf-4.45.0.tgz#bef91b1e924ab57e82e767dc2655264bbde7acc6"
  integrity sha1-vvkbHpJKtX6C52fcJlUmS73nrMY=

"@rollup/rollup-linux-arm64-gnu@4.45.0":
  version "4.45.0"
  resolved "https://registry-npm-next.huolala.work/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.45.0.tgz#0a811b16da334125f6e44570d0badf543876f49e"
  integrity sha1-CoEbFtozQSX25EVw0LrfVDh29J4=

"@rollup/rollup-linux-arm64-musl@4.45.0":
  version "4.45.0"
  resolved "https://registry-npm-next.huolala.work/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.45.0.tgz#e8c166efe3cb963faaa924c7721eafbade63036f"
  integrity sha1-6MFm7+PLlj+qqSTHch6vut5jA28=

"@rollup/rollup-linux-loongarch64-gnu@4.45.0":
  version "4.45.0"
  resolved "https://registry-npm-next.huolala.work/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.45.0.tgz#239feea00fa2a1e734bdff09b8d1c90def2abbf5"
  integrity sha1-I5/uoA+ioec0vf8JuNHJDe8qu/U=

"@rollup/rollup-linux-powerpc64le-gnu@4.45.0":
  version "4.45.0"
  resolved "https://registry-npm-next.huolala.work/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.45.0.tgz#1de2f926bddbf7d689a089277c1284ea6df4b6d1"
  integrity sha1-HeL5Jr3b99aJoIknfBKE6m30ttE=

"@rollup/rollup-linux-riscv64-gnu@4.45.0":
  version "4.45.0"
  resolved "https://registry-npm-next.huolala.work/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.45.0.tgz#28dbac643244e477a7b931feb9b475aa826f84c1"
  integrity sha1-KNusZDJE5HenuTH+ubR1qoJvhME=

"@rollup/rollup-linux-riscv64-musl@4.45.0":
  version "4.45.0"
  resolved "https://registry-npm-next.huolala.work/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.45.0.tgz#5d05eeaedadec3625cd50e3ca5d35ef6f96a4bf0"
  integrity sha1-XQXurtrew2Jc1Q48pdNe9vlqS/A=

"@rollup/rollup-linux-s390x-gnu@4.45.0":
  version "4.45.0"
  resolved "https://registry-npm-next.huolala.work/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.45.0.tgz#55b0790f499fb7adc14eb074c4e46aef92915813"
  integrity sha1-VbB5D0mft63BTrB0xORq75KRWBM=

"@rollup/rollup-linux-x64-gnu@4.45.0":
  version "4.45.0"
  resolved "https://registry-npm-next.huolala.work/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.45.0.tgz#e822632fe5b324b16bdc37149149c8c760b031fd"
  integrity sha1-6CJjL+WzJLFr3DcUkUnIx2CwMf0=

"@rollup/rollup-linux-x64-musl@4.45.0":
  version "4.45.0"
  resolved "https://registry-npm-next.huolala.work/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.45.0.tgz#19a3602cb8fabd7eb3087f0a1e1e01adac31bbff"
  integrity sha1-GaNgLLj6vX6zCH8KHh4Brawxu/8=

"@rollup/rollup-win32-arm64-msvc@4.45.0":
  version "4.45.0"
  resolved "https://registry-npm-next.huolala.work/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.45.0.tgz#42e08bf3ea4fc463fc9f199c4f0310a736f03eb1"
  integrity sha1-QuCL8+pPxGP8nxmcTwMQpzbwPrE=

"@rollup/rollup-win32-ia32-msvc@4.45.0":
  version "4.45.0"
  resolved "https://registry-npm-next.huolala.work/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.45.0.tgz#043d25557f59d7e28dfe38ee1f60ddcb95a08124"
  integrity sha1-BD0lVX9Z1+KN/jjuH2Ddy5WggSQ=

"@rollup/rollup-win32-x64-msvc@4.45.0":
  version "4.45.0"
  resolved "https://registry-npm-next.huolala.work/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.45.0.tgz#0a7eecae41f463d6591c8fecd7a5c5087345ee36"
  integrity sha1-Cn7srkH0Y9ZZHI/s16XFCHNF7jY=

"@sinclair/typebox@^0.27.8":
  version "0.27.8"
  resolved "https://registry-npm-next.huolala.work/@sinclair/typebox/-/typebox-0.27.8.tgz#6667fac16c436b5434a387a34dedb013198f6e6e"
  integrity sha1-Zmf6wWxDa1Q0o4ejTe2wExmPbm4=

"@sinonjs/commons@^3.0.0":
  version "3.0.1"
  resolved "https://registry-npm-next.huolala.work/@sinonjs/commons/-/commons-3.0.1.tgz#1029357e44ca901a615585f6d27738dbc89084cd"
  integrity sha1-ECk1fkTKkBphVYX20nc428iQhM0=
  dependencies:
    type-detect "4.0.8"

"@sinonjs/fake-timers@^10.0.2":
  version "10.3.0"
  resolved "https://registry-npm-next.huolala.work/@sinonjs/fake-timers/-/fake-timers-10.3.0.tgz#55fdff1ecab9f354019129daf4df0dd4d923ea66"
  integrity sha1-Vf3/Hsq581QBkSna9N8N1Nkj6mY=
  dependencies:
    "@sinonjs/commons" "^3.0.0"

"@types/babel__core@^7.1.14", "@types/babel__core@^7.20.5":
  version "7.20.5"
  resolved "https://registry-npm-next.huolala.work/@types/babel__core/-/babel__core-7.20.5.tgz#3df15f27ba85319caa07ba08d0721889bb39c017"
  integrity sha1-PfFfJ7qFMZyqB7oI0HIYibs5wBc=
  dependencies:
    "@babel/parser" "^7.20.7"
    "@babel/types" "^7.20.7"
    "@types/babel__generator" "*"
    "@types/babel__template" "*"
    "@types/babel__traverse" "*"

"@types/babel__generator@*":
  version "7.27.0"
  resolved "https://registry-npm-next.huolala.work/@types/babel__generator/-/babel__generator-7.27.0.tgz#b5819294c51179957afaec341442f9341e4108a9"
  integrity sha1-tYGSlMUReZV6+uw0FEL5NB5BCKk=
  dependencies:
    "@babel/types" "^7.0.0"

"@types/babel__template@*":
  version "7.4.4"
  resolved "https://registry-npm-next.huolala.work/@types/babel__template/-/babel__template-7.4.4.tgz#5672513701c1b2199bc6dad636a9d7491586766f"
  integrity sha1-VnJRNwHBshmbxtrWNqnXSRWGdm8=
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"

"@types/babel__traverse@*", "@types/babel__traverse@^7.0.6":
  version "7.20.7"
  resolved "https://registry-npm-next.huolala.work/@types/babel__traverse/-/babel__traverse-7.20.7.tgz#968cdc2366ec3da159f61166428ee40f370e56c2"
  integrity sha1-lozcI2bsPaFZ9hFmQo7kDzcOVsI=
  dependencies:
    "@babel/types" "^7.20.7"

"@types/estree@*", "@types/estree@1.0.8", "@types/estree@^1.0.0", "@types/estree@^1.0.6":
  version "1.0.8"
  resolved "https://registry-npm-next.huolala.work/@types/estree/-/estree-1.0.8.tgz#958b91c991b1867ced318bedea0e215ee050726e"
  integrity sha1-lYuRyZGxhnztMYvt6g4hXuBQcm4=

"@types/graceful-fs@^4.1.3":
  version "4.1.9"
  resolved "https://registry-npm-next.huolala.work/@types/graceful-fs/-/graceful-fs-4.1.9.tgz#2a06bc0f68a20ab37b3e36aa238be6abdf49e8b4"
  integrity sha1-Kga8D2iiCrN7PjaqI4vmq99J6LQ=
  dependencies:
    "@types/node" "*"

"@types/istanbul-lib-coverage@*", "@types/istanbul-lib-coverage@^2.0.0", "@types/istanbul-lib-coverage@^2.0.1":
  version "2.0.6"
  resolved "https://registry-npm-next.huolala.work/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.6.tgz#7739c232a1fee9b4d3ce8985f314c0c6d33549d7"
  integrity sha1-dznCMqH+6bTTzomF8xTAxtM1Sdc=

"@types/istanbul-lib-report@*":
  version "3.0.3"
  resolved "https://registry-npm-next.huolala.work/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.3.tgz#53047614ae72e19fc0401d872de3ae2b4ce350bf"
  integrity sha1-UwR2FK5y4Z/AQB2HLeOuK0zjUL8=
  dependencies:
    "@types/istanbul-lib-coverage" "*"

"@types/istanbul-reports@^3.0.0":
  version "3.0.4"
  resolved "https://registry-npm-next.huolala.work/@types/istanbul-reports/-/istanbul-reports-3.0.4.tgz#0f03e3d2f670fbdac586e34b433783070cc16f54"
  integrity sha1-DwPj0vZw+9rFhuNLQzeDBwzBb1Q=
  dependencies:
    "@types/istanbul-lib-report" "*"

"@types/jest@^29.5.14":
  version "29.5.14"
  resolved "https://registry-npm-next.huolala.work/@types/jest/-/jest-29.5.14.tgz#2b910912fa1d6856cadcd0c1f95af7df1d6049e5"
  integrity sha1-K5EJEvodaFbK3NDB+Vr33x1gSeU=
  dependencies:
    expect "^29.0.0"
    pretty-format "^29.0.0"

"@types/json-schema@^7.0.15":
  version "7.0.15"
  resolved "https://registry-npm-next.huolala.work/@types/json-schema/-/json-schema-7.0.15.tgz#596a1747233694d50f6ad8a7869fcb6f56cf5841"
  integrity sha1-WWoXRyM2lNUPatinhp/Lb1bPWEE=

"@types/node@*":
  version "24.0.13"
  resolved "https://registry-npm-next.huolala.work/@types/node/-/node-24.0.13.tgz#93ed8c05c7b188a59760be0ce2ee3fa7ad0f83f6"
  integrity sha1-k+2MBcexiKWXYL4M4u4/p60Pg/Y=
  dependencies:
    undici-types "~7.8.0"

"@types/react-dom@^19.1.2":
  version "19.1.6"
  resolved "https://registry-npm-next.huolala.work/@types/react-dom/-/react-dom-19.1.6.tgz#4af629da0e9f9c0f506fc4d1caa610399c595d64"
  integrity sha1-SvYp2g6fnA9Qb8TRyqYQOZxZXWQ=

"@types/react@^19.1.2":
  version "19.1.8"
  resolved "https://registry-npm-next.huolala.work/@types/react/-/react-19.1.8.tgz#ff8395f2afb764597265ced15f8dddb0720ae1c3"
  integrity sha1-/4OV8q+3ZFlyZc7RX43dsHIK4cM=
  dependencies:
    csstype "^3.0.2"

"@types/resolve@1.20.2":
  version "1.20.2"
  resolved "https://registry-npm-next.huolala.work/@types/resolve/-/resolve-1.20.2.tgz#97d26e00cd4a0423b4af620abecf3e6f442b7975"
  integrity sha1-l9JuAM1KBCO0r2IKvs8+b0QreXU=

"@types/stack-utils@^2.0.0":
  version "2.0.3"
  resolved "https://registry-npm-next.huolala.work/@types/stack-utils/-/stack-utils-2.0.3.tgz#6209321eb2c1712a7e7466422b8cb1fc0d9dd5d8"
  integrity sha1-YgkyHrLBcSp+dGZCK4yx/A2d1dg=

"@types/yargs-parser@*":
  version "21.0.3"
  resolved "https://registry-npm-next.huolala.work/@types/yargs-parser/-/yargs-parser-21.0.3.tgz#815e30b786d2e8f0dcd85fd5bcf5e1a04d008f15"
  integrity sha1-gV4wt4bS6PDc2F/VvPXhoE0AjxU=

"@types/yargs@^17.0.8":
  version "17.0.33"
  resolved "https://registry-npm-next.huolala.work/@types/yargs/-/yargs-17.0.33.tgz#8c32303da83eec050a84b3c7ae7b9f922d13e32d"
  integrity sha1-jDIwPag+7AUKhLPHrnufki0T4y0=
  dependencies:
    "@types/yargs-parser" "*"

"@typescript-eslint/eslint-plugin@8.36.0":
  version "8.36.0"
  resolved "https://registry-npm-next.huolala.work/@typescript-eslint/eslint-plugin/-/eslint-plugin-8.36.0.tgz#880ce277f8a30ccf539ec027acac157088f131ae"
  integrity sha1-iAzid/ijDM9TnsAnrKwVcIjxMa4=
  dependencies:
    "@eslint-community/regexpp" "^4.10.0"
    "@typescript-eslint/scope-manager" "8.36.0"
    "@typescript-eslint/type-utils" "8.36.0"
    "@typescript-eslint/utils" "8.36.0"
    "@typescript-eslint/visitor-keys" "8.36.0"
    graphemer "^1.4.0"
    ignore "^7.0.0"
    natural-compare "^1.4.0"
    ts-api-utils "^2.1.0"

"@typescript-eslint/parser@8.36.0":
  version "8.36.0"
  resolved "https://registry-npm-next.huolala.work/@typescript-eslint/parser/-/parser-8.36.0.tgz#003007fe2030013936b6634b9cf52c457d36ed42"
  integrity sha1-ADAH/iAwATk2tmNLnPUsRX027UI=
  dependencies:
    "@typescript-eslint/scope-manager" "8.36.0"
    "@typescript-eslint/types" "8.36.0"
    "@typescript-eslint/typescript-estree" "8.36.0"
    "@typescript-eslint/visitor-keys" "8.36.0"
    debug "^4.3.4"

"@typescript-eslint/project-service@8.36.0":
  version "8.36.0"
  resolved "https://registry-npm-next.huolala.work/@typescript-eslint/project-service/-/project-service-8.36.0.tgz#0c4acdcbe56476a43cdabaac1f08819424a379fd"
  integrity sha1-DErNy+VkdqQ82rqsHwiBlCSjef0=
  dependencies:
    "@typescript-eslint/tsconfig-utils" "^8.36.0"
    "@typescript-eslint/types" "^8.36.0"
    debug "^4.3.4"

"@typescript-eslint/scope-manager@8.36.0":
  version "8.36.0"
  resolved "https://registry-npm-next.huolala.work/@typescript-eslint/scope-manager/-/scope-manager-8.36.0.tgz#23e4196ed07d7ea3737a584fbebc9a79c3835168"
  integrity sha1-I+QZbtB9fqNzelhPvryaecODUWg=
  dependencies:
    "@typescript-eslint/types" "8.36.0"
    "@typescript-eslint/visitor-keys" "8.36.0"

"@typescript-eslint/tsconfig-utils@8.36.0", "@typescript-eslint/tsconfig-utils@^8.36.0":
  version "8.36.0"
  resolved "https://registry-npm-next.huolala.work/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.36.0.tgz#63ef8a20ae9b5754c6ceacbe87b2fe1aab12ba13"
  integrity sha1-Y++KIK6bV1TGzqy+h7L+GqsSuhM=

"@typescript-eslint/type-utils@8.36.0":
  version "8.36.0"
  resolved "https://registry-npm-next.huolala.work/@typescript-eslint/type-utils/-/type-utils-8.36.0.tgz#16b092c2cbbb5549f6a4df1382a481586850502f"
  integrity sha1-FrCSwsu7VUn2pN8TgqSBWGhQUC8=
  dependencies:
    "@typescript-eslint/typescript-estree" "8.36.0"
    "@typescript-eslint/utils" "8.36.0"
    debug "^4.3.4"
    ts-api-utils "^2.1.0"

"@typescript-eslint/types@8.36.0", "@typescript-eslint/types@^8.36.0":
  version "8.36.0"
  resolved "https://registry-npm-next.huolala.work/@typescript-eslint/types/-/types-8.36.0.tgz#d3d184adc2899e2912c13b17c1590486ef37c7ac"
  integrity sha1-09GErcKJnikSwTsXwVkEhu83x6w=

"@typescript-eslint/typescript-estree@8.36.0":
  version "8.36.0"
  resolved "https://registry-npm-next.huolala.work/@typescript-eslint/typescript-estree/-/typescript-estree-8.36.0.tgz#344857fa79f71715369554a3cbb6b4ff8695a7bc"
  integrity sha1-NEhX+nn3FxU2lVSjy7a0/4aVp7w=
  dependencies:
    "@typescript-eslint/project-service" "8.36.0"
    "@typescript-eslint/tsconfig-utils" "8.36.0"
    "@typescript-eslint/types" "8.36.0"
    "@typescript-eslint/visitor-keys" "8.36.0"
    debug "^4.3.4"
    fast-glob "^3.3.2"
    is-glob "^4.0.3"
    minimatch "^9.0.4"
    semver "^7.6.0"
    ts-api-utils "^2.1.0"

"@typescript-eslint/utils@8.36.0":
  version "8.36.0"
  resolved "https://registry-npm-next.huolala.work/@typescript-eslint/utils/-/utils-8.36.0.tgz#2c9af5292f14e0aa4b0e9c7ac0406afafb299acf"
  integrity sha1-LJr1KS8U4KpLDpx6wEBq+vspms8=
  dependencies:
    "@eslint-community/eslint-utils" "^4.7.0"
    "@typescript-eslint/scope-manager" "8.36.0"
    "@typescript-eslint/types" "8.36.0"
    "@typescript-eslint/typescript-estree" "8.36.0"

"@typescript-eslint/visitor-keys@8.36.0":
  version "8.36.0"
  resolved "https://registry-npm-next.huolala.work/@typescript-eslint/visitor-keys/-/visitor-keys-8.36.0.tgz#7dc6ba4dd037979eb3a3bdd2093aa3604bb73674"
  integrity sha1-fca6TdA3l56zo73SCTqjYEu3NnQ=
  dependencies:
    "@typescript-eslint/types" "8.36.0"
    eslint-visitor-keys "^4.2.1"

"@vitejs/plugin-react@^4.4.1":
  version "4.6.0"
  resolved "https://registry-npm-next.huolala.work/@vitejs/plugin-react/-/plugin-react-4.6.0.tgz#2707b485f44806d42d41c63921883cff9c54dfaa"
  integrity sha1-Jwe0hfRIBtQtQcY5IYg8/5xU36o=
  dependencies:
    "@babel/core" "^7.27.4"
    "@babel/plugin-transform-react-jsx-self" "^7.27.1"
    "@babel/plugin-transform-react-jsx-source" "^7.27.1"
    "@rolldown/pluginutils" "1.0.0-beta.19"
    "@types/babel__core" "^7.20.5"
    react-refresh "^0.17.0"

acorn-jsx@^5.3.2:
  version "5.3.2"
  resolved "https://registry-npm-next.huolala.work/acorn-jsx/-/acorn-jsx-5.3.2.tgz#7ed5bb55908b3b2f1bc55c6af1653bada7f07937"
  integrity sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=

acorn@^8.14.0, acorn@^8.15.0:
  version "8.15.0"
  resolved "https://registry-npm-next.huolala.work/acorn/-/acorn-8.15.0.tgz#a360898bc415edaac46c8241f6383975b930b816"
  integrity sha1-o2CJi8QV7arEbIJB9jg5dbkwuBY=

ajv@^6.12.4:
  version "6.12.6"
  resolved "https://registry-npm-next.huolala.work/ajv/-/ajv-6.12.6.tgz#baf5a62e802b07d977034586f8c3baf5adf26df4"
  integrity sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ansi-escapes@^4.2.1:
  version "4.3.2"
  resolved "https://registry-npm-next.huolala.work/ansi-escapes/-/ansi-escapes-4.3.2.tgz#6b2291d1db7d98b6521d5f1efa42d0f3a9feb65e"
  integrity sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=
  dependencies:
    type-fest "^0.21.3"

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "https://registry-npm-next.huolala.work/ansi-regex/-/ansi-regex-5.0.1.tgz#082cb2c89c9fe8659a311a53bd6a4dc5301db304"
  integrity sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "https://registry-npm-next.huolala.work/ansi-styles/-/ansi-styles-4.3.0.tgz#edd803628ae71c04c85ae7a0906edad34b648937"
  integrity sha1-7dgDYornHATIWuegkG7a00tkiTc=
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^5.0.0:
  version "5.2.0"
  resolved "https://registry-npm-next.huolala.work/ansi-styles/-/ansi-styles-5.2.0.tgz#07449690ad45777d1924ac2abb2fc8895dba836b"
  integrity sha1-B0SWkK1Fd30ZJKwquy/IiV26g2s=

anymatch@^3.0.3, anymatch@~3.1.2:
  version "3.1.3"
  resolved "https://registry-npm-next.huolala.work/anymatch/-/anymatch-3.1.3.tgz#790c58b19ba1720a84205b57c618d5ad8524973e"
  integrity sha1-eQxYsZuhcgqEIFtXxhjVrYUklz4=
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

argparse@^1.0.7:
  version "1.0.10"
  resolved "https://registry-npm-next.huolala.work/argparse/-/argparse-1.0.10.tgz#bcd6791ea5ae09725e17e5ad988134cd40b3d911"
  integrity sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=
  dependencies:
    sprintf-js "~1.0.2"

argparse@^2.0.1:
  version "2.0.1"
  resolved "https://registry-npm-next.huolala.work/argparse/-/argparse-2.0.1.tgz#246f50f3ca78a3240f6c997e8a9bd1eac49e4b38"
  integrity sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=

array-union@^2.1.0:
  version "2.1.0"
  resolved "https://registry-npm-next.huolala.work/array-union/-/array-union-2.1.0.tgz#b798420adbeb1de828d84acd8a2e23d3efe85e8d"
  integrity sha1-t5hCCtvrHego2ErNii4j0+/oXo0=

async@^3.2.3:
  version "3.2.6"
  resolved "https://registry-npm-next.huolala.work/async/-/async-3.2.6.tgz#1b0728e14929d51b85b449b7f06e27c1145e38ce"
  integrity sha1-Gwco4Ukp1RuFtEm38G4nwRReOM4=

babel-jest@^29.7.0:
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/babel-jest/-/babel-jest-29.7.0.tgz#f4369919225b684c56085998ac63dbd05be020d5"
  integrity sha1-9DaZGSJbaExWCFmYrGPb0FvgINU=
  dependencies:
    "@jest/transform" "^29.7.0"
    "@types/babel__core" "^7.1.14"
    babel-plugin-istanbul "^6.1.1"
    babel-preset-jest "^29.6.3"
    chalk "^4.0.0"
    graceful-fs "^4.2.9"
    slash "^3.0.0"

babel-plugin-istanbul@^6.1.1:
  version "6.1.1"
  resolved "https://registry-npm-next.huolala.work/babel-plugin-istanbul/-/babel-plugin-istanbul-6.1.1.tgz#fa88ec59232fd9b4e36dbbc540a8ec9a9b47da73"
  integrity sha1-+ojsWSMv2bTjbbvFQKjsmptH2nM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@istanbuljs/load-nyc-config" "^1.0.0"
    "@istanbuljs/schema" "^0.1.2"
    istanbul-lib-instrument "^5.0.4"
    test-exclude "^6.0.0"

babel-plugin-jest-hoist@^29.6.3:
  version "29.6.3"
  resolved "https://registry-npm-next.huolala.work/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-29.6.3.tgz#aadbe943464182a8922c3c927c3067ff40d24626"
  integrity sha1-qtvpQ0ZBgqiSLDySfDBn/0DSRiY=
  dependencies:
    "@babel/template" "^7.3.3"
    "@babel/types" "^7.3.3"
    "@types/babel__core" "^7.1.14"
    "@types/babel__traverse" "^7.0.6"

babel-preset-current-node-syntax@^1.0.0:
  version "1.1.0"
  resolved "https://registry-npm-next.huolala.work/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.1.0.tgz#9a929eafece419612ef4ae4f60b1862ebad8ef30"
  integrity sha1-mpKer+zkGWEu9K5PYLGGLrrY7zA=
  dependencies:
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-bigint" "^7.8.3"
    "@babel/plugin-syntax-class-properties" "^7.12.13"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"
    "@babel/plugin-syntax-import-attributes" "^7.24.7"
    "@babel/plugin-syntax-import-meta" "^7.10.4"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"
    "@babel/plugin-syntax-top-level-await" "^7.14.5"

babel-preset-jest@^29.6.3:
  version "29.6.3"
  resolved "https://registry-npm-next.huolala.work/babel-preset-jest/-/babel-preset-jest-29.6.3.tgz#fa05fa510e7d493896d7b0dd2033601c840f171c"
  integrity sha1-+gX6UQ59STiW17DdIDNgHIQPFxw=
  dependencies:
    babel-plugin-jest-hoist "^29.6.3"
    babel-preset-current-node-syntax "^1.0.0"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://registry-npm-next.huolala.work/balanced-match/-/balanced-match-1.0.2.tgz#e83e3a7e3f300b34cb9d87f615fa0cbf357690ee"
  integrity sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=

binary-extensions@^2.0.0:
  version "2.3.0"
  resolved "https://registry-npm-next.huolala.work/binary-extensions/-/binary-extensions-2.3.0.tgz#f6e14a97858d327252200242d4ccfe522c445522"
  integrity sha1-9uFKl4WNMnJSIAJC1Mz+UixEVSI=

brace-expansion@^1.1.7:
  version "1.1.12"
  resolved "https://registry-npm-next.huolala.work/brace-expansion/-/brace-expansion-1.1.12.tgz#ab9b454466e5a8cc3a187beaad580412a9c5b843"
  integrity sha1-q5tFRGblqMw6GHvqrVgEEqnFuEM=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.2"
  resolved "https://registry-npm-next.huolala.work/brace-expansion/-/brace-expansion-2.0.2.tgz#54fc53237a613d854c7bd37463aad17df87214e7"
  integrity sha1-VPxTI3phPYVMe9N0Y6rRffhyFOc=
  dependencies:
    balanced-match "^1.0.0"

braces@^3.0.3, braces@~3.0.2:
  version "3.0.3"
  resolved "https://registry-npm-next.huolala.work/braces/-/braces-3.0.3.tgz#490332f40919452272d55a8480adc0c441358789"
  integrity sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=
  dependencies:
    fill-range "^7.1.1"

browserslist@^4.24.0:
  version "4.25.1"
  resolved "https://registry-npm-next.huolala.work/browserslist/-/browserslist-4.25.1.tgz#ba9e8e6f298a1d86f829c9b975e07948967bb111"
  integrity sha1-up6ObymKHYb4Kcm5deB5SJZ7sRE=
  dependencies:
    caniuse-lite "^1.0.30001726"
    electron-to-chromium "^1.5.173"
    node-releases "^2.0.19"
    update-browserslist-db "^1.1.3"

bs-logger@^0.2.6:
  version "0.2.6"
  resolved "https://registry-npm-next.huolala.work/bs-logger/-/bs-logger-0.2.6.tgz#eb7d365307a72cf974cc6cda76b68354ad336bd8"
  integrity sha1-6302UwenLPl0zGzadraDVK0za9g=
  dependencies:
    fast-json-stable-stringify "2.x"

bser@2.1.1:
  version "2.1.1"
  resolved "https://registry-npm-next.huolala.work/bser/-/bser-2.1.1.tgz#e6787da20ece9d07998533cfd9de6f5c38f4bc05"
  integrity sha1-5nh9og7OnQeZhTPP2d5vXDj0vAU=
  dependencies:
    node-int64 "^0.4.0"

buffer-from@^1.0.0:
  version "1.1.2"
  resolved "https://registry-npm-next.huolala.work/buffer-from/-/buffer-from-1.1.2.tgz#2b146a6fd72e80b4f55d255f35ed59a3a9a41bd5"
  integrity sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=

callsites@^3.0.0:
  version "3.1.0"
  resolved "https://registry-npm-next.huolala.work/callsites/-/callsites-3.1.0.tgz#b3630abd8943432f54b3f0519238e33cd7df2f73"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

camelcase@^5.3.1:
  version "5.3.1"
  resolved "https://registry-npm-next.huolala.work/camelcase/-/camelcase-5.3.1.tgz#e3c9b31569e106811df242f715725a1f4c494320"
  integrity sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=

camelcase@^6.2.0:
  version "6.3.0"
  resolved "https://registry-npm-next.huolala.work/camelcase/-/camelcase-6.3.0.tgz#5685b95eb209ac9c0c177467778c9c84df58ba9a"
  integrity sha1-VoW5XrIJrJwMF3Rnd4ychN9Yupo=

caniuse-lite@^1.0.30001726:
  version "1.0.30001727"
  resolved "https://registry-npm-next.huolala.work/caniuse-lite/-/caniuse-lite-1.0.30001727.tgz#22e9706422ad37aa50556af8c10e40e2d93a8b85"
  integrity sha1-IulwZCKtN6pQVWr4wQ5A4tk6i4U=

chalk@^4.0.0, chalk@^4.0.2:
  version "4.1.2"
  resolved "https://registry-npm-next.huolala.work/chalk/-/chalk-4.1.2.tgz#aac4e2b7734a740867aeb16bf02aad556a1e7a01"
  integrity sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

char-regex@^1.0.2:
  version "1.0.2"
  resolved "https://registry-npm-next.huolala.work/char-regex/-/char-regex-1.0.2.tgz#d744358226217f981ed58f479b1d6bcc29545dcf"
  integrity sha1-10Q1giYhf5ge1Y9Hmx1rzClUXc8=

chokidar@^3.5.3:
  version "3.6.0"
  resolved "https://registry-npm-next.huolala.work/chokidar/-/chokidar-3.6.0.tgz#197c6cc669ef2a8dc5e7b4d97ee4e092c3eb0d5b"
  integrity sha1-GXxsxmnvKo3F57TZfuTgksPrDVs=
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

ci-info@^3.2.0:
  version "3.9.0"
  resolved "https://registry-npm-next.huolala.work/ci-info/-/ci-info-3.9.0.tgz#4279a62028a7b1f262f3473fc9605f5e218c59b4"
  integrity sha1-QnmmICinsfJi80c/yWBfXiGMWbQ=

cjs-module-lexer@^1.0.0:
  version "1.4.3"
  resolved "https://registry-npm-next.huolala.work/cjs-module-lexer/-/cjs-module-lexer-1.4.3.tgz#0f79731eb8cfe1ec72acd4066efac9d61991b00d"
  integrity sha1-D3lzHrjP4exyrNQGbvrJ1hmRsA0=

cliui@^8.0.1:
  version "8.0.1"
  resolved "https://registry-npm-next.huolala.work/cliui/-/cliui-8.0.1.tgz#0c04b075db02cbfe60dc8e6cf2f5486b1a3608aa"
  integrity sha1-DASwddsCy/5g3I5s8vVIaxo2CKo=
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.1"
    wrap-ansi "^7.0.0"

co@^4.6.0:
  version "4.6.0"
  resolved "https://registry-npm-next.huolala.work/co/-/co-4.6.0.tgz#6ea6bdf3d853ae54ccb8e47bfa0bf3f9031fb184"
  integrity sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=

collect-v8-coverage@^1.0.0:
  version "1.0.2"
  resolved "https://registry-npm-next.huolala.work/collect-v8-coverage/-/collect-v8-coverage-1.0.2.tgz#c0b29bcd33bcd0779a1344c2136051e6afd3d9e9"
  integrity sha1-wLKbzTO80HeaE0TCE2BR5q/T2ek=

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://registry-npm-next.huolala.work/color-convert/-/color-convert-2.0.1.tgz#72d3a68d598c9bdb3af2ad1e84f21d896abd4de3"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@~1.1.4:
  version "1.1.4"
  resolved "https://registry-npm-next.huolala.work/color-name/-/color-name-1.1.4.tgz#c2a09a87acbde69543de6f63fa3995c826c536a2"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

commander@^2.20.0:
  version "2.20.3"
  resolved "https://registry-npm-next.huolala.work/commander/-/commander-2.20.3.tgz#fd485e84c03eb4881c20722ba48035e8531aeb33"
  integrity sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=

commondir@^1.0.1:
  version "1.0.1"
  resolved "https://registry-npm-next.huolala.work/commondir/-/commondir-1.0.1.tgz#ddd800da0c66127393cca5950ea968a3aaf1253b"
  integrity sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://registry-npm-next.huolala.work/concat-map/-/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

convert-source-map@^2.0.0:
  version "2.0.0"
  resolved "https://registry-npm-next.huolala.work/convert-source-map/-/convert-source-map-2.0.0.tgz#4b560f649fc4e918dd0ab75cf4961e8bc882d82a"
  integrity sha1-S1YPZJ/E6RjdCrdc9JYei8iC2Co=

cookie@^1.0.1:
  version "1.0.2"
  resolved "https://registry-npm-next.huolala.work/cookie/-/cookie-1.0.2.tgz#27360701532116bd3f1f9416929d176afe1e4610"
  integrity sha1-JzYHAVMhFr0/H5QWkp0Xav4eRhA=

create-jest@^29.7.0:
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/create-jest/-/create-jest-29.7.0.tgz#a355c5b3cb1e1af02ba177fe7afd7feee49a5320"
  integrity sha1-o1XFs8seGvAroXf+ev1/7uSaUyA=
  dependencies:
    "@jest/types" "^29.6.3"
    chalk "^4.0.0"
    exit "^0.1.2"
    graceful-fs "^4.2.9"
    jest-config "^29.7.0"
    jest-util "^29.7.0"
    prompts "^2.0.1"

cross-spawn@^7.0.3, cross-spawn@^7.0.6:
  version "7.0.6"
  resolved "https://registry-npm-next.huolala.work/cross-spawn/-/cross-spawn-7.0.6.tgz#8a58fe78f00dcd70c370451759dfbfaf03e8ee9f"
  integrity sha1-ilj+ePANzXDDcEUXWd+/rwPo7p8=
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

csstype@^3.0.2:
  version "3.1.3"
  resolved "https://registry-npm-next.huolala.work/csstype/-/csstype-3.1.3.tgz#d80ff294d114fb0e6ac500fbf85b60137d7eff81"
  integrity sha1-2A/ylNEU+w5qxQD7+FtgE31+/4E=

debug@^4.1.0, debug@^4.1.1, debug@^4.3.1, debug@^4.3.2, debug@^4.3.4:
  version "4.4.1"
  resolved "https://registry-npm-next.huolala.work/debug/-/debug-4.4.1.tgz#e5a8bc6cbc4c6cd3e64308b0693a3d4fa550189b"
  integrity sha1-5ai8bLxMbNPmQwiwaTo9T6VQGJs=
  dependencies:
    ms "^2.1.3"

dedent@^1.0.0:
  version "1.6.0"
  resolved "https://registry-npm-next.huolala.work/dedent/-/dedent-1.6.0.tgz#79d52d6389b1ffa67d2bcef59ba51847a9d503b2"
  integrity sha1-edUtY4mx/6Z9K871m6UYR6nVA7I=

deep-is@^0.1.3:
  version "0.1.4"
  resolved "https://registry-npm-next.huolala.work/deep-is/-/deep-is-0.1.4.tgz#a6f2dce612fadd2ef1f519b73551f17e85199831"
  integrity sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=

deepmerge@^4.2.2:
  version "4.3.1"
  resolved "https://registry-npm-next.huolala.work/deepmerge/-/deepmerge-4.3.1.tgz#44b5f2147cd3b00d4b56137685966f26fd25dd4a"
  integrity sha1-RLXyFHzTsA1LVhN2hZZvJv0l3Uo=

detect-newline@^3.0.0:
  version "3.1.0"
  resolved "https://registry-npm-next.huolala.work/detect-newline/-/detect-newline-3.1.0.tgz#576f5dfc63ae1a192ff192d8ad3af6308991b651"
  integrity sha1-V29d/GOuGhkv8ZLYrTr2MImRtlE=

diff-sequences@^29.6.3:
  version "29.6.3"
  resolved "https://registry-npm-next.huolala.work/diff-sequences/-/diff-sequences-29.6.3.tgz#4deaf894d11407c51efc8418012f9e70b84ea921"
  integrity sha1-Ter4lNEUB8Ue/IQYAS+ecLhOqSE=

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "https://registry-npm-next.huolala.work/dir-glob/-/dir-glob-3.0.1.tgz#56dbf73d992a4a93ba1584f4534063fd2e41717f"
  integrity sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=
  dependencies:
    path-type "^4.0.0"

ejs@^3.1.10:
  version "3.1.10"
  resolved "https://registry-npm-next.huolala.work/ejs/-/ejs-3.1.10.tgz#69ab8358b14e896f80cc39e62087b88500c3ac3b"
  integrity sha1-aauDWLFOiW+AzDnmIIe4hQDDrDs=
  dependencies:
    jake "^10.8.5"

electron-to-chromium@^1.5.173:
  version "1.5.182"
  resolved "https://registry-npm-next.huolala.work/electron-to-chromium/-/electron-to-chromium-1.5.182.tgz#4ab73104f893938acb3ab9c28d7bec170c116b3e"
  integrity sha1-SrcxBPiTk4rLOrnCjXvsFwwRaz4=

emittery@^0.13.1:
  version "0.13.1"
  resolved "https://registry-npm-next.huolala.work/emittery/-/emittery-0.13.1.tgz#c04b8c3457490e0847ae51fced3af52d338e3dad"
  integrity sha1-wEuMNFdJDghHrlH87Tr1LTOOPa0=

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://registry-npm-next.huolala.work/emoji-regex/-/emoji-regex-8.0.0.tgz#e818fd69ce5ccfcb404594f842963bf53164cc37"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

error-ex@^1.3.1:
  version "1.3.2"
  resolved "https://registry-npm-next.huolala.work/error-ex/-/error-ex-1.3.2.tgz#b4ac40648107fdcdcfae242f428bea8a14d4f1bf"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

esbuild@^0.25.0:
  version "0.25.6"
  resolved "https://registry-npm-next.huolala.work/esbuild/-/esbuild-0.25.6.tgz#9b82a3db2fa131aec069ab040fd57ed0a880cdcd"
  integrity sha1-m4Kj2y+hMa7AaasED9V+0KiAzc0=
  optionalDependencies:
    "@esbuild/aix-ppc64" "0.25.6"
    "@esbuild/android-arm" "0.25.6"
    "@esbuild/android-arm64" "0.25.6"
    "@esbuild/android-x64" "0.25.6"
    "@esbuild/darwin-arm64" "0.25.6"
    "@esbuild/darwin-x64" "0.25.6"
    "@esbuild/freebsd-arm64" "0.25.6"
    "@esbuild/freebsd-x64" "0.25.6"
    "@esbuild/linux-arm" "0.25.6"
    "@esbuild/linux-arm64" "0.25.6"
    "@esbuild/linux-ia32" "0.25.6"
    "@esbuild/linux-loong64" "0.25.6"
    "@esbuild/linux-mips64el" "0.25.6"
    "@esbuild/linux-ppc64" "0.25.6"
    "@esbuild/linux-riscv64" "0.25.6"
    "@esbuild/linux-s390x" "0.25.6"
    "@esbuild/linux-x64" "0.25.6"
    "@esbuild/netbsd-arm64" "0.25.6"
    "@esbuild/netbsd-x64" "0.25.6"
    "@esbuild/openbsd-arm64" "0.25.6"
    "@esbuild/openbsd-x64" "0.25.6"
    "@esbuild/openharmony-arm64" "0.25.6"
    "@esbuild/sunos-x64" "0.25.6"
    "@esbuild/win32-arm64" "0.25.6"
    "@esbuild/win32-ia32" "0.25.6"
    "@esbuild/win32-x64" "0.25.6"

escalade@^3.1.1, escalade@^3.2.0:
  version "3.2.0"
  resolved "https://registry-npm-next.huolala.work/escalade/-/escalade-3.2.0.tgz#011a3f69856ba189dffa7dc8fcce99d2a87903e5"
  integrity sha1-ARo/aYVroYnf+n3I/M6Z0qh5A+U=

escape-string-regexp@^2.0.0:
  version "2.0.0"
  resolved "https://registry-npm-next.huolala.work/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz#a30304e99daa32e23b2fd20f51babd07cffca344"
  integrity sha1-owME6Z2qMuI7L9IPUbq9B8/8o0Q=

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "https://registry-npm-next.huolala.work/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz#14ba83a5d373e3d311e5afca29cf5bfad965bf34"
  integrity sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=

eslint-plugin-react-hooks@^5.2.0:
  version "5.2.0"
  resolved "https://registry-npm-next.huolala.work/eslint-plugin-react-hooks/-/eslint-plugin-react-hooks-5.2.0.tgz#1be0080901e6ac31ce7971beed3d3ec0a423d9e3"
  integrity sha1-G+AICQHmrDHOeXG+7T0+wKQj2eM=

eslint-plugin-react-refresh@^0.4.19:
  version "0.4.20"
  resolved "https://registry-npm-next.huolala.work/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.4.20.tgz#3bbfb5c8637e28d19ce3443686445e502ecd18ba"
  integrity sha1-O7+1yGN+KNGc40Q2hkReUC7NGLo=

eslint-scope@^8.4.0:
  version "8.4.0"
  resolved "https://registry-npm-next.huolala.work/eslint-scope/-/eslint-scope-8.4.0.tgz#88e646a207fad61436ffa39eb505147200655c82"
  integrity sha1-iOZGogf61hQ2/6OetQUUcgBlXII=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-visitor-keys@^3.4.3:
  version "3.4.3"
  resolved "https://registry-npm-next.huolala.work/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz#0cd72fe8550e3c2eae156a96a4dddcd1c8ac5800"
  integrity sha1-DNcv6FUOPC6uFWqWpN3c0cisWAA=

eslint-visitor-keys@^4.2.1:
  version "4.2.1"
  resolved "https://registry-npm-next.huolala.work/eslint-visitor-keys/-/eslint-visitor-keys-4.2.1.tgz#4cfea60fe7dd0ad8e816e1ed026c1d5251b512c1"
  integrity sha1-TP6mD+fdCtjoFuHtAmwdUlG1EsE=

eslint@^9.25.0:
  version "9.31.0"
  resolved "https://registry-npm-next.huolala.work/eslint/-/eslint-9.31.0.tgz#9a488e6da75bbe05785cd62e43c5ea99356d21ba"
  integrity sha1-mkiObadbvgV4XNYuQ8XqmTVtIbo=
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@eslint-community/regexpp" "^4.12.1"
    "@eslint/config-array" "^0.21.0"
    "@eslint/config-helpers" "^0.3.0"
    "@eslint/core" "^0.15.0"
    "@eslint/eslintrc" "^3.3.1"
    "@eslint/js" "9.31.0"
    "@eslint/plugin-kit" "^0.3.1"
    "@humanfs/node" "^0.16.6"
    "@humanwhocodes/module-importer" "^1.0.1"
    "@humanwhocodes/retry" "^0.4.2"
    "@types/estree" "^1.0.6"
    "@types/json-schema" "^7.0.15"
    ajv "^6.12.4"
    chalk "^4.0.0"
    cross-spawn "^7.0.6"
    debug "^4.3.2"
    escape-string-regexp "^4.0.0"
    eslint-scope "^8.4.0"
    eslint-visitor-keys "^4.2.1"
    espree "^10.4.0"
    esquery "^1.5.0"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^8.0.0"
    find-up "^5.0.0"
    glob-parent "^6.0.2"
    ignore "^5.2.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    json-stable-stringify-without-jsonify "^1.0.1"
    lodash.merge "^4.6.2"
    minimatch "^3.1.2"
    natural-compare "^1.4.0"
    optionator "^0.9.3"

espree@^10.0.1, espree@^10.4.0:
  version "10.4.0"
  resolved "https://registry-npm-next.huolala.work/espree/-/espree-10.4.0.tgz#d54f4949d4629005a1fa168d937c3ff1f7e2a837"
  integrity sha1-1U9JSdRikAWh+haNk3w/8ffiqDc=
  dependencies:
    acorn "^8.15.0"
    acorn-jsx "^5.3.2"
    eslint-visitor-keys "^4.2.1"

esprima@^4.0.0:
  version "4.0.1"
  resolved "https://registry-npm-next.huolala.work/esprima/-/esprima-4.0.1.tgz#13b04cdb3e6c5d19df91ab6987a8695619b0aa71"
  integrity sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=

esquery@^1.5.0:
  version "1.6.0"
  resolved "https://registry-npm-next.huolala.work/esquery/-/esquery-1.6.0.tgz#91419234f804d852a82dceec3e16cdc22cf9dae7"
  integrity sha1-kUGSNPgE2FKoLc7sPhbNwiz52uc=
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "https://registry-npm-next.huolala.work/esrecurse/-/esrecurse-4.3.0.tgz#7ad7964d679abb28bee72cec63758b1c5d2c9921"
  integrity sha1-eteWTWeauyi+5yzsY3WLHF0smSE=
  dependencies:
    estraverse "^5.2.0"

estraverse@^5.1.0, estraverse@^5.2.0:
  version "5.3.0"
  resolved "https://registry-npm-next.huolala.work/estraverse/-/estraverse-5.3.0.tgz#2eea5290702f26ab8fe5370370ff86c965d21123"
  integrity sha1-LupSkHAvJquP5TcDcP+GyWXSESM=

estree-walker@^2.0.2:
  version "2.0.2"
  resolved "https://registry-npm-next.huolala.work/estree-walker/-/estree-walker-2.0.2.tgz#52f010178c2a4c117a7757cfe942adb7d2da4cac"
  integrity sha1-UvAQF4wqTBF6d1fP6UKtt9LaTKw=

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://registry-npm-next.huolala.work/esutils/-/esutils-2.0.3.tgz#74d2eb4de0b8da1293711910d50775b9b710ef64"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

execa@^5.0.0:
  version "5.1.1"
  resolved "https://registry-npm-next.huolala.work/execa/-/execa-5.1.1.tgz#f80ad9cbf4298f7bd1d4c9555c21e93741c411dd"
  integrity sha1-+ArZy/Qpj3vR1MlVXCHpN0HEEd0=
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.0"
    human-signals "^2.1.0"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.1"
    onetime "^5.1.2"
    signal-exit "^3.0.3"
    strip-final-newline "^2.0.0"

exit@^0.1.2:
  version "0.1.2"
  resolved "https://registry-npm-next.huolala.work/exit/-/exit-0.1.2.tgz#0632638f8d877cc82107d30a0fff1a17cba1cd0c"
  integrity sha1-BjJjj42HfMghB9MKD/8aF8uhzQw=

expect@^29.0.0, expect@^29.7.0:
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/expect/-/expect-29.7.0.tgz#578874590dcb3214514084c08115d8aee61e11bc"
  integrity sha1-V4h0WQ3LMhRRQITAgRXYruYeEbw=
  dependencies:
    "@jest/expect-utils" "^29.7.0"
    jest-get-type "^29.6.3"
    jest-matcher-utils "^29.7.0"
    jest-message-util "^29.7.0"
    jest-util "^29.7.0"

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "https://registry-npm-next.huolala.work/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz#3a7d56b559d6cbc3eb512325244e619a65c6c525"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-glob@^3.2.9, fast-glob@^3.3.2:
  version "3.3.3"
  resolved "https://registry-npm-next.huolala.work/fast-glob/-/fast-glob-3.3.3.tgz#d06d585ce8dba90a16b0505c543c3ccfb3aeb818"
  integrity sha1-0G1YXOjbqQoWsFBcVDw8z7OuuBg=
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.8"

fast-json-stable-stringify@2.x, fast-json-stable-stringify@^2.0.0, fast-json-stable-stringify@^2.1.0:
  version "2.1.0"
  resolved "https://registry-npm-next.huolala.work/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz#874bf69c6f404c2b5d99c481341399fd55892633"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fast-levenshtein@^2.0.6:
  version "2.0.6"
  resolved "https://registry-npm-next.huolala.work/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz#3d8a5c66883a16a30ca8643e851f19baa7797917"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fastq@^1.6.0:
  version "1.19.1"
  resolved "https://registry-npm-next.huolala.work/fastq/-/fastq-1.19.1.tgz#d50eaba803c8846a883c16492821ebcd2cda55f5"
  integrity sha1-1Q6rqAPIhGqIPBZJKCHrzSzaVfU=
  dependencies:
    reusify "^1.0.4"

fb-watchman@^2.0.0:
  version "2.0.2"
  resolved "https://registry-npm-next.huolala.work/fb-watchman/-/fb-watchman-2.0.2.tgz#e9524ee6b5c77e9e5001af0f85f3adbb8623255c"
  integrity sha1-6VJO5rXHfp5QAa8PhfOtu4YjJVw=
  dependencies:
    bser "2.1.1"

fdir@^6.4.4:
  version "6.4.6"
  resolved "https://registry-npm-next.huolala.work/fdir/-/fdir-6.4.6.tgz#2b268c0232697063111bbf3f64810a2a741ba281"
  integrity sha1-KyaMAjJpcGMRG78/ZIEKKnQbooE=

file-entry-cache@^8.0.0:
  version "8.0.0"
  resolved "https://registry-npm-next.huolala.work/file-entry-cache/-/file-entry-cache-8.0.0.tgz#7787bddcf1131bffb92636c69457bbc0edd6d81f"
  integrity sha1-d4e93PETG/+5JjbGlFe7wO3W2B8=
  dependencies:
    flat-cache "^4.0.0"

filelist@^1.0.4:
  version "1.0.4"
  resolved "https://registry-npm-next.huolala.work/filelist/-/filelist-1.0.4.tgz#f78978a1e944775ff9e62e744424f215e58352b5"
  integrity sha1-94l4oelEd1/55i50RCTyFeWDUrU=
  dependencies:
    minimatch "^5.0.1"

fill-range@^7.1.1:
  version "7.1.1"
  resolved "https://registry-npm-next.huolala.work/fill-range/-/fill-range-7.1.1.tgz#44265d3cac07e3ea7dc247516380643754a05292"
  integrity sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=
  dependencies:
    to-regex-range "^5.0.1"

find-up@^4.0.0, find-up@^4.1.0:
  version "4.1.0"
  resolved "https://registry-npm-next.huolala.work/find-up/-/find-up-4.1.0.tgz#97afe7d6cdc0bc5928584b7c8d7b16e8a9aa5d19"
  integrity sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

find-up@^5.0.0:
  version "5.0.0"
  resolved "https://registry-npm-next.huolala.work/find-up/-/find-up-5.0.0.tgz#4c92819ecb7083561e4f4a240a86be5198f536fc"
  integrity sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=
  dependencies:
    locate-path "^6.0.0"
    path-exists "^4.0.0"

flat-cache@^4.0.0:
  version "4.0.1"
  resolved "https://registry-npm-next.huolala.work/flat-cache/-/flat-cache-4.0.1.tgz#0ece39fcb14ee012f4b0410bd33dd9c1f011127c"
  integrity sha1-Ds45/LFO4BL0sEEL0z3ZwfAREnw=
  dependencies:
    flatted "^3.2.9"
    keyv "^4.5.4"

flatted@^3.2.9:
  version "3.3.3"
  resolved "https://registry-npm-next.huolala.work/flatted/-/flatted-3.3.3.tgz#67c8fad95454a7c7abebf74bb78ee74a44023358"
  integrity sha1-Z8j62VRUp8er6/dLt47nSkQCM1g=

fs-extra@^11.3.0:
  version "11.3.0"
  resolved "https://registry-npm-next.huolala.work/fs-extra/-/fs-extra-11.3.0.tgz#0daced136bbaf65a555a326719af931adc7a314d"
  integrity sha1-DaztE2u69lpVWjJnGa+TGtx6MU0=
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://registry-npm-next.huolala.work/fs.realpath/-/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@^2.3.2, fsevents@~2.3.2, fsevents@~2.3.3:
  version "2.3.3"
  resolved "https://registry-npm-next.huolala.work/fsevents/-/fsevents-2.3.3.tgz#cac6407785d03675a2a5e1a5305c697b347d90d6"
  integrity sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=

function-bind@^1.1.2:
  version "1.1.2"
  resolved "https://registry-npm-next.huolala.work/function-bind/-/function-bind-1.1.2.tgz#2c02d864d97f3ea6c8830c464cbd11ab6eab7a1c"
  integrity sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "https://registry-npm-next.huolala.work/gensync/-/gensync-1.0.0-beta.2.tgz#32a6ee76c3d7f52d46b2b1ae5d93fea8580a25e0"
  integrity sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=

get-caller-file@^2.0.5:
  version "2.0.5"
  resolved "https://registry-npm-next.huolala.work/get-caller-file/-/get-caller-file-2.0.5.tgz#4f94412a82db32f36e3b0b9741f8a97feb031f7e"
  integrity sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=

get-package-type@^0.1.0:
  version "0.1.0"
  resolved "https://registry-npm-next.huolala.work/get-package-type/-/get-package-type-0.1.0.tgz#8de2d803cff44df3bc6c456e6668b36c3926e11a"
  integrity sha1-jeLYA8/0TfO8bEVuZmizbDkm4Ro=

get-stream@^6.0.0:
  version "6.0.1"
  resolved "https://registry-npm-next.huolala.work/get-stream/-/get-stream-6.0.1.tgz#a262d8eef67aced57c2852ad6167526a43cbf7b7"
  integrity sha1-omLY7vZ6ztV8KFKtYWdSakPL97c=

github-markdown-css@^5.8.1:
  version "5.8.1"
  resolved "https://registry-npm-next.huolala.work/github-markdown-css/-/github-markdown-css-5.8.1.tgz#2a53cf17f0c9bde5ff9e83710a3310a02f5278a7"
  integrity sha1-KlPPF/DJveX/noNxCjMQoC9SeKc=

glob-parent@^5.1.2, glob-parent@~5.1.2:
  version "5.1.2"
  resolved "https://registry-npm-next.huolala.work/glob-parent/-/glob-parent-5.1.2.tgz#869832c58034fe68a4093c17dc15e8340d8401c4"
  integrity sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=
  dependencies:
    is-glob "^4.0.1"

glob-parent@^6.0.2:
  version "6.0.2"
  resolved "https://registry-npm-next.huolala.work/glob-parent/-/glob-parent-6.0.2.tgz#6d237d99083950c79290f24c7642a3de9a28f9e3"
  integrity sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=
  dependencies:
    is-glob "^4.0.3"

glob@^7.1.3, glob@^7.1.4:
  version "7.2.3"
  resolved "https://registry-npm-next.huolala.work/glob/-/glob-7.2.3.tgz#b8df0fb802bbfa8e89bd1d938b4e16578ed44f2b"
  integrity sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@^8.0.3:
  version "8.1.0"
  resolved "https://registry-npm-next.huolala.work/glob/-/glob-8.1.0.tgz#d388f656593ef708ee3e34640fdfb99a9fd1c33e"
  integrity sha1-04j2Vlk+9wjuPjRkD9+5mp/Rwz4=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^5.0.1"
    once "^1.3.0"

globals@^14.0.0:
  version "14.0.0"
  resolved "https://registry-npm-next.huolala.work/globals/-/globals-14.0.0.tgz#898d7413c29babcf6bafe56fcadded858ada724e"
  integrity sha1-iY10E8Kbq89rr+Vvyt3thYrack4=

globals@^16.0.0:
  version "16.3.0"
  resolved "https://registry-npm-next.huolala.work/globals/-/globals-16.3.0.tgz#66118e765ddaf9e2d880f7e17658543f93f1f667"
  integrity sha1-ZhGOdl3a+eLYgPfhdlhUP5Px9mc=

globby@^11.0.0:
  version "11.1.0"
  resolved "https://registry-npm-next.huolala.work/globby/-/globby-11.1.0.tgz#bd4be98bb042f83d796f7e3811991fbe82a0d34b"
  integrity sha1-vUvpi7BC+D15b344EZkfvoKg00s=
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.2.9"
    ignore "^5.2.0"
    merge2 "^1.4.1"
    slash "^3.0.0"

graceful-fs@^4.1.6, graceful-fs@^4.2.0, graceful-fs@^4.2.9:
  version "4.2.11"
  resolved "https://registry-npm-next.huolala.work/graceful-fs/-/graceful-fs-4.2.11.tgz#4183e4e8bf08bb6e05bbb2f7d2e0c8f712ca40e3"
  integrity sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM=

graphemer@^1.4.0:
  version "1.4.0"
  resolved "https://registry-npm-next.huolala.work/graphemer/-/graphemer-1.4.0.tgz#fb2f1d55e0e3a1849aeffc90c4fa0dd53a0e66c6"
  integrity sha1-+y8dVeDjoYSa7/yQxPoN1ToOZsY=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://registry-npm-next.huolala.work/has-flag/-/has-flag-4.0.0.tgz#944771fd9c81c81265c4d6941860da06bb59479b"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

hasown@^2.0.2:
  version "2.0.2"
  resolved "https://registry-npm-next.huolala.work/hasown/-/hasown-2.0.2.tgz#003eaf91be7adc372e84ec59dc37252cedb80003"
  integrity sha1-AD6vkb563DcuhOxZ3DclLO24AAM=
  dependencies:
    function-bind "^1.1.2"

html-escaper@^2.0.0:
  version "2.0.2"
  resolved "https://registry-npm-next.huolala.work/html-escaper/-/html-escaper-2.0.2.tgz#dfd60027da36a36dfcbe236262c00a5822681453"
  integrity sha1-39YAJ9o2o238viNiYsAKWCJoFFM=

human-signals@^2.1.0:
  version "2.1.0"
  resolved "https://registry-npm-next.huolala.work/human-signals/-/human-signals-2.1.0.tgz#dc91fcba42e4d06e4abaed33b3e7a3c02f514ea0"
  integrity sha1-3JH8ukLk0G5Kuu0zs+ejwC9RTqA=

ignore@^5.2.0:
  version "5.3.2"
  resolved "https://registry-npm-next.huolala.work/ignore/-/ignore-5.3.2.tgz#3cd40e729f3643fd87cb04e50bf0eb722bc596f5"
  integrity sha1-PNQOcp82Q/2HywTlC/DrcivFlvU=

ignore@^7.0.0:
  version "7.0.5"
  resolved "https://registry-npm-next.huolala.work/ignore/-/ignore-7.0.5.tgz#4cb5f6cd7d4c7ab0365738c7aea888baa6d7efd9"
  integrity sha1-TLX2zX1MerA2VzjHrqiIuqbX79k=

import-fresh@^3.2.1:
  version "3.3.1"
  resolved "https://registry-npm-next.huolala.work/import-fresh/-/import-fresh-3.3.1.tgz#9cecb56503c0ada1f2741dbbd6546e4b13b57ccf"
  integrity sha1-nOy1ZQPAraHydB271lRuSxO1fM8=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-local@^3.0.2:
  version "3.2.0"
  resolved "https://registry-npm-next.huolala.work/import-local/-/import-local-3.2.0.tgz#c3d5c745798c02a6f8b897726aba5100186ee260"
  integrity sha1-w9XHRXmMAqb4uJdyarpRABhu4mA=
  dependencies:
    pkg-dir "^4.2.0"
    resolve-cwd "^3.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://registry-npm-next.huolala.work/imurmurhash/-/imurmurhash-0.1.4.tgz#9218b9b2b928a238b13dc4fb6b6d576f231453ea"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://registry-npm-next.huolala.work/inflight/-/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2:
  version "2.0.4"
  resolved "https://registry-npm-next.huolala.work/inherits/-/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://registry-npm-next.huolala.work/is-arrayish/-/is-arrayish-0.2.1.tgz#77c99840527aa8ecb1a8ba697b80645a7a926a9d"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://registry-npm-next.huolala.work/is-binary-path/-/is-binary-path-2.1.0.tgz#ea1f7f3b80f064236e83470f86c09c254fb45b09"
  integrity sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=
  dependencies:
    binary-extensions "^2.0.0"

is-core-module@^2.16.0:
  version "2.16.1"
  resolved "https://registry-npm-next.huolala.work/is-core-module/-/is-core-module-2.16.1.tgz#2a98801a849f43e2add644fbb6bc6229b19a4ef4"
  integrity sha1-KpiAGoSfQ+Kt1kT7trxiKbGaTvQ=
  dependencies:
    hasown "^2.0.2"

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://registry-npm-next.huolala.work/is-extglob/-/is-extglob-2.1.1.tgz#a88c02535791f02ed37c76a1b9ea9773c833f8c2"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://registry-npm-next.huolala.work/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz#f116f8064fe90b3f7844a38997c0b75051269f1d"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-generator-fn@^2.0.0:
  version "2.1.0"
  resolved "https://registry-npm-next.huolala.work/is-generator-fn/-/is-generator-fn-2.1.0.tgz#7d140adc389aaf3011a8f2a2a4cfa6faadffb118"
  integrity sha1-fRQK3DiarzARqPKipM+m+q3/sRg=

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  resolved "https://registry-npm-next.huolala.work/is-glob/-/is-glob-4.0.3.tgz#64f61e42cbbb2eec2071a9dac0b28ba1e65d5084"
  integrity sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=
  dependencies:
    is-extglob "^2.1.1"

is-module@^1.0.0:
  version "1.0.0"
  resolved "https://registry-npm-next.huolala.work/is-module/-/is-module-1.0.0.tgz#3258fb69f78c14d5b815d664336b4cffb6441591"
  integrity sha1-Mlj7afeMFNW4FdZkM2tM/7ZEFZE=

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://registry-npm-next.huolala.work/is-number/-/is-number-7.0.0.tgz#7535345b896734d5f80c4d06c50955527a14f12b"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-reference@1.2.1:
  version "1.2.1"
  resolved "https://registry-npm-next.huolala.work/is-reference/-/is-reference-1.2.1.tgz#8b2dac0b371f4bc994fdeaba9eb542d03002d0b7"
  integrity sha1-iy2sCzcfS8mU/eq6nrVC0DAC0Lc=
  dependencies:
    "@types/estree" "*"

is-stream@^2.0.0:
  version "2.0.1"
  resolved "https://registry-npm-next.huolala.work/is-stream/-/is-stream-2.0.1.tgz#fac1e3d53b97ad5a9d0ae9cef2389f5810a5c077"
  integrity sha1-+sHj1TuXrVqdCunO8jifWBClwHc=

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://registry-npm-next.huolala.work/isexe/-/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

istanbul-lib-coverage@^3.0.0, istanbul-lib-coverage@^3.2.0:
  version "3.2.2"
  resolved "https://registry-npm-next.huolala.work/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.2.tgz#2d166c4b0644d43a39f04bf6c2edd1e585f31756"
  integrity sha1-LRZsSwZE1Do58Ev2wu3R5YXzF1Y=

istanbul-lib-instrument@^5.0.4:
  version "5.2.1"
  resolved "https://registry-npm-next.huolala.work/istanbul-lib-instrument/-/istanbul-lib-instrument-5.2.1.tgz#d10c8885c2125574e1c231cacadf955675e1ce3d"
  integrity sha1-0QyIhcISVXThwjHKyt+VVnXhzj0=
  dependencies:
    "@babel/core" "^7.12.3"
    "@babel/parser" "^7.14.7"
    "@istanbuljs/schema" "^0.1.2"
    istanbul-lib-coverage "^3.2.0"
    semver "^6.3.0"

istanbul-lib-instrument@^6.0.0:
  version "6.0.3"
  resolved "https://registry-npm-next.huolala.work/istanbul-lib-instrument/-/istanbul-lib-instrument-6.0.3.tgz#fa15401df6c15874bcb2105f773325d78c666765"
  integrity sha1-+hVAHfbBWHS8shBfdzMl14xmZ2U=
  dependencies:
    "@babel/core" "^7.23.9"
    "@babel/parser" "^7.23.9"
    "@istanbuljs/schema" "^0.1.3"
    istanbul-lib-coverage "^3.2.0"
    semver "^7.5.4"

istanbul-lib-report@^3.0.0:
  version "3.0.1"
  resolved "https://registry-npm-next.huolala.work/istanbul-lib-report/-/istanbul-lib-report-3.0.1.tgz#908305bac9a5bd175ac6a74489eafd0fc2445a7d"
  integrity sha1-kIMFusmlvRdaxqdEier9D8JEWn0=
  dependencies:
    istanbul-lib-coverage "^3.0.0"
    make-dir "^4.0.0"
    supports-color "^7.1.0"

istanbul-lib-source-maps@^4.0.0:
  version "4.0.1"
  resolved "https://registry-npm-next.huolala.work/istanbul-lib-source-maps/-/istanbul-lib-source-maps-4.0.1.tgz#895f3a709fcfba34c6de5a42939022f3e4358551"
  integrity sha1-iV86cJ/PujTG3lpCk5Ai8+Q1hVE=
  dependencies:
    debug "^4.1.1"
    istanbul-lib-coverage "^3.0.0"
    source-map "^0.6.1"

istanbul-reports@^3.1.3:
  version "3.1.7"
  resolved "https://registry-npm-next.huolala.work/istanbul-reports/-/istanbul-reports-3.1.7.tgz#daed12b9e1dca518e15c056e1e537e741280fa0b"
  integrity sha1-2u0SueHcpRjhXAVuHlN+dBKA+gs=
  dependencies:
    html-escaper "^2.0.0"
    istanbul-lib-report "^3.0.0"

jake@^10.8.5:
  version "10.9.2"
  resolved "https://registry-npm-next.huolala.work/jake/-/jake-10.9.2.tgz#6ae487e6a69afec3a5e167628996b59f35ae2b7f"
  integrity sha1-auSH5qaa/sOl4WdiiZa1nzWuK38=
  dependencies:
    async "^3.2.3"
    chalk "^4.0.2"
    filelist "^1.0.4"
    minimatch "^3.1.2"

jest-changed-files@^29.7.0:
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/jest-changed-files/-/jest-changed-files-29.7.0.tgz#1c06d07e77c78e1585d020424dedc10d6e17ac3a"
  integrity sha1-HAbQfnfHjhWF0CBCTe3BDW4XrDo=
  dependencies:
    execa "^5.0.0"
    jest-util "^29.7.0"
    p-limit "^3.1.0"

jest-circus@^29.7.0:
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/jest-circus/-/jest-circus-29.7.0.tgz#b6817a45fcc835d8b16d5962d0c026473ee3668a"
  integrity sha1-toF6RfzINdixbVli0MAmRz7jZoo=
  dependencies:
    "@jest/environment" "^29.7.0"
    "@jest/expect" "^29.7.0"
    "@jest/test-result" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    chalk "^4.0.0"
    co "^4.6.0"
    dedent "^1.0.0"
    is-generator-fn "^2.0.0"
    jest-each "^29.7.0"
    jest-matcher-utils "^29.7.0"
    jest-message-util "^29.7.0"
    jest-runtime "^29.7.0"
    jest-snapshot "^29.7.0"
    jest-util "^29.7.0"
    p-limit "^3.1.0"
    pretty-format "^29.7.0"
    pure-rand "^6.0.0"
    slash "^3.0.0"
    stack-utils "^2.0.3"

jest-cli@^29.7.0:
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/jest-cli/-/jest-cli-29.7.0.tgz#5592c940798e0cae677eec169264f2d839a37995"
  integrity sha1-VZLJQHmODK5nfuwWkmTy2DmjeZU=
  dependencies:
    "@jest/core" "^29.7.0"
    "@jest/test-result" "^29.7.0"
    "@jest/types" "^29.6.3"
    chalk "^4.0.0"
    create-jest "^29.7.0"
    exit "^0.1.2"
    import-local "^3.0.2"
    jest-config "^29.7.0"
    jest-util "^29.7.0"
    jest-validate "^29.7.0"
    yargs "^17.3.1"

jest-config@^29.7.0:
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/jest-config/-/jest-config-29.7.0.tgz#bcbda8806dbcc01b1e316a46bb74085a84b0245f"
  integrity sha1-vL2ogG28wBseMWpGu3QIWoSwJF8=
  dependencies:
    "@babel/core" "^7.11.6"
    "@jest/test-sequencer" "^29.7.0"
    "@jest/types" "^29.6.3"
    babel-jest "^29.7.0"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    deepmerge "^4.2.2"
    glob "^7.1.3"
    graceful-fs "^4.2.9"
    jest-circus "^29.7.0"
    jest-environment-node "^29.7.0"
    jest-get-type "^29.6.3"
    jest-regex-util "^29.6.3"
    jest-resolve "^29.7.0"
    jest-runner "^29.7.0"
    jest-util "^29.7.0"
    jest-validate "^29.7.0"
    micromatch "^4.0.4"
    parse-json "^5.2.0"
    pretty-format "^29.7.0"
    slash "^3.0.0"
    strip-json-comments "^3.1.1"

jest-diff@^29.7.0:
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/jest-diff/-/jest-diff-29.7.0.tgz#017934a66ebb7ecf6f205e84699be10afd70458a"
  integrity sha1-AXk0pm67fs9vIF6EaZvhCv1wRYo=
  dependencies:
    chalk "^4.0.0"
    diff-sequences "^29.6.3"
    jest-get-type "^29.6.3"
    pretty-format "^29.7.0"

jest-docblock@^29.7.0:
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/jest-docblock/-/jest-docblock-29.7.0.tgz#8fddb6adc3cdc955c93e2a87f61cfd350d5d119a"
  integrity sha1-j922rcPNyVXJPiqH9hz9NQ1dEZo=
  dependencies:
    detect-newline "^3.0.0"

jest-each@^29.7.0:
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/jest-each/-/jest-each-29.7.0.tgz#162a9b3f2328bdd991beaabffbb74745e56577d1"
  integrity sha1-FiqbPyMovdmRvqq/+7dHReVld9E=
  dependencies:
    "@jest/types" "^29.6.3"
    chalk "^4.0.0"
    jest-get-type "^29.6.3"
    jest-util "^29.7.0"
    pretty-format "^29.7.0"

jest-environment-node@^29.7.0:
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/jest-environment-node/-/jest-environment-node-29.7.0.tgz#0b93e111dda8ec120bc8300e6d1fb9576e164376"
  integrity sha1-C5PhEd2o7BILyDAObR+5V24WQ3Y=
  dependencies:
    "@jest/environment" "^29.7.0"
    "@jest/fake-timers" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    jest-mock "^29.7.0"
    jest-util "^29.7.0"

jest-get-type@^29.6.3:
  version "29.6.3"
  resolved "https://registry-npm-next.huolala.work/jest-get-type/-/jest-get-type-29.6.3.tgz#36f499fdcea197c1045a127319c0481723908fd1"
  integrity sha1-NvSZ/c6hl8EEWhJzGcBIFyOQj9E=

jest-haste-map@^29.7.0:
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/jest-haste-map/-/jest-haste-map-29.7.0.tgz#3c2396524482f5a0506376e6c858c3bbcc17b104"
  integrity sha1-PCOWUkSC9aBQY3bmyFjDu8wXsQQ=
  dependencies:
    "@jest/types" "^29.6.3"
    "@types/graceful-fs" "^4.1.3"
    "@types/node" "*"
    anymatch "^3.0.3"
    fb-watchman "^2.0.0"
    graceful-fs "^4.2.9"
    jest-regex-util "^29.6.3"
    jest-util "^29.7.0"
    jest-worker "^29.7.0"
    micromatch "^4.0.4"
    walker "^1.0.8"
  optionalDependencies:
    fsevents "^2.3.2"

jest-leak-detector@^29.7.0:
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/jest-leak-detector/-/jest-leak-detector-29.7.0.tgz#5b7ec0dadfdfec0ca383dc9aa016d36b5ea4c728"
  integrity sha1-W37A2t/f7Ayjg9yaoBbTa16kxyg=
  dependencies:
    jest-get-type "^29.6.3"
    pretty-format "^29.7.0"

jest-matcher-utils@^29.7.0:
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/jest-matcher-utils/-/jest-matcher-utils-29.7.0.tgz#ae8fec79ff249fd592ce80e3ee474e83a6c44f12"
  integrity sha1-ro/sef8kn9WSzoDj7kdOg6bETxI=
  dependencies:
    chalk "^4.0.0"
    jest-diff "^29.7.0"
    jest-get-type "^29.6.3"
    pretty-format "^29.7.0"

jest-message-util@^29.7.0:
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/jest-message-util/-/jest-message-util-29.7.0.tgz#8bc392e204e95dfe7564abbe72a404e28e51f7f3"
  integrity sha1-i8OS4gTpXf51ZKu+cqQE4o5R9/M=
  dependencies:
    "@babel/code-frame" "^7.12.13"
    "@jest/types" "^29.6.3"
    "@types/stack-utils" "^2.0.0"
    chalk "^4.0.0"
    graceful-fs "^4.2.9"
    micromatch "^4.0.4"
    pretty-format "^29.7.0"
    slash "^3.0.0"
    stack-utils "^2.0.3"

jest-mock@^29.7.0:
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/jest-mock/-/jest-mock-29.7.0.tgz#4e836cf60e99c6fcfabe9f99d017f3fdd50a6347"
  integrity sha1-ToNs9g6Zxvz6vp+Z0Bfz/dUKY0c=
  dependencies:
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    jest-util "^29.7.0"

jest-pnp-resolver@^1.2.2:
  version "1.2.3"
  resolved "https://registry-npm-next.huolala.work/jest-pnp-resolver/-/jest-pnp-resolver-1.2.3.tgz#930b1546164d4ad5937d5540e711d4d38d4cad2e"
  integrity sha1-kwsVRhZNStWTfVVA5xHU041MrS4=

jest-regex-util@^29.6.3:
  version "29.6.3"
  resolved "https://registry-npm-next.huolala.work/jest-regex-util/-/jest-regex-util-29.6.3.tgz#4a556d9c776af68e1c5f48194f4d0327d24e8a52"
  integrity sha1-SlVtnHdq9o4cX0gZT00DJ9JOilI=

jest-resolve-dependencies@^29.7.0:
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/jest-resolve-dependencies/-/jest-resolve-dependencies-29.7.0.tgz#1b04f2c095f37fc776ff40803dc92921b1e88428"
  integrity sha1-GwTywJXzf8d2/0CAPckpIbHohCg=
  dependencies:
    jest-regex-util "^29.6.3"
    jest-snapshot "^29.7.0"

jest-resolve@^29.7.0:
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/jest-resolve/-/jest-resolve-29.7.0.tgz#64d6a8992dd26f635ab0c01e5eef4399c6bcbc30"
  integrity sha1-ZNaomS3Sb2NasMAeXu9Dmca8vDA=
  dependencies:
    chalk "^4.0.0"
    graceful-fs "^4.2.9"
    jest-haste-map "^29.7.0"
    jest-pnp-resolver "^1.2.2"
    jest-util "^29.7.0"
    jest-validate "^29.7.0"
    resolve "^1.20.0"
    resolve.exports "^2.0.0"
    slash "^3.0.0"

jest-runner@^29.7.0:
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/jest-runner/-/jest-runner-29.7.0.tgz#809af072d408a53dcfd2e849a4c976d3132f718e"
  integrity sha1-gJrwctQIpT3P0uhJpMl20xMvcY4=
  dependencies:
    "@jest/console" "^29.7.0"
    "@jest/environment" "^29.7.0"
    "@jest/test-result" "^29.7.0"
    "@jest/transform" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    chalk "^4.0.0"
    emittery "^0.13.1"
    graceful-fs "^4.2.9"
    jest-docblock "^29.7.0"
    jest-environment-node "^29.7.0"
    jest-haste-map "^29.7.0"
    jest-leak-detector "^29.7.0"
    jest-message-util "^29.7.0"
    jest-resolve "^29.7.0"
    jest-runtime "^29.7.0"
    jest-util "^29.7.0"
    jest-watcher "^29.7.0"
    jest-worker "^29.7.0"
    p-limit "^3.1.0"
    source-map-support "0.5.13"

jest-runtime@^29.7.0:
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/jest-runtime/-/jest-runtime-29.7.0.tgz#efecb3141cf7d3767a3a0cc8f7c9990587d3d817"
  integrity sha1-7+yzFBz303Z6OgzI98mZBYfT2Bc=
  dependencies:
    "@jest/environment" "^29.7.0"
    "@jest/fake-timers" "^29.7.0"
    "@jest/globals" "^29.7.0"
    "@jest/source-map" "^29.6.3"
    "@jest/test-result" "^29.7.0"
    "@jest/transform" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    chalk "^4.0.0"
    cjs-module-lexer "^1.0.0"
    collect-v8-coverage "^1.0.0"
    glob "^7.1.3"
    graceful-fs "^4.2.9"
    jest-haste-map "^29.7.0"
    jest-message-util "^29.7.0"
    jest-mock "^29.7.0"
    jest-regex-util "^29.6.3"
    jest-resolve "^29.7.0"
    jest-snapshot "^29.7.0"
    jest-util "^29.7.0"
    slash "^3.0.0"
    strip-bom "^4.0.0"

jest-snapshot@^29.7.0:
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/jest-snapshot/-/jest-snapshot-29.7.0.tgz#c2c574c3f51865da1bb329036778a69bf88a6be5"
  integrity sha1-wsV0w/UYZdobsykDZ3imm/iKa+U=
  dependencies:
    "@babel/core" "^7.11.6"
    "@babel/generator" "^7.7.2"
    "@babel/plugin-syntax-jsx" "^7.7.2"
    "@babel/plugin-syntax-typescript" "^7.7.2"
    "@babel/types" "^7.3.3"
    "@jest/expect-utils" "^29.7.0"
    "@jest/transform" "^29.7.0"
    "@jest/types" "^29.6.3"
    babel-preset-current-node-syntax "^1.0.0"
    chalk "^4.0.0"
    expect "^29.7.0"
    graceful-fs "^4.2.9"
    jest-diff "^29.7.0"
    jest-get-type "^29.6.3"
    jest-matcher-utils "^29.7.0"
    jest-message-util "^29.7.0"
    jest-util "^29.7.0"
    natural-compare "^1.4.0"
    pretty-format "^29.7.0"
    semver "^7.5.3"

jest-util@^29.7.0:
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/jest-util/-/jest-util-29.7.0.tgz#23c2b62bfb22be82b44de98055802ff3710fc0bc"
  integrity sha1-I8K2K/sivoK0TemAVYAv83EPwLw=
  dependencies:
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    graceful-fs "^4.2.9"
    picomatch "^2.2.3"

jest-validate@^29.7.0:
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/jest-validate/-/jest-validate-29.7.0.tgz#7bf705511c64da591d46b15fce41400d52147d9c"
  integrity sha1-e/cFURxk2lkdRrFfzkFADVIUfZw=
  dependencies:
    "@jest/types" "^29.6.3"
    camelcase "^6.2.0"
    chalk "^4.0.0"
    jest-get-type "^29.6.3"
    leven "^3.1.0"
    pretty-format "^29.7.0"

jest-watcher@^29.7.0:
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/jest-watcher/-/jest-watcher-29.7.0.tgz#7810d30d619c3a62093223ce6bb359ca1b28a2f2"
  integrity sha1-eBDTDWGcOmIJMiPOa7NZyhsoovI=
  dependencies:
    "@jest/test-result" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    ansi-escapes "^4.2.1"
    chalk "^4.0.0"
    emittery "^0.13.1"
    jest-util "^29.7.0"
    string-length "^4.0.1"

jest-worker@^29.7.0:
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/jest-worker/-/jest-worker-29.7.0.tgz#acad073acbbaeb7262bd5389e1bcf43e10058d4a"
  integrity sha1-rK0HOsu663JivVOJ4bz0PhAFjUo=
  dependencies:
    "@types/node" "*"
    jest-util "^29.7.0"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

jest@^29.7.0:
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/jest/-/jest-29.7.0.tgz#994676fc24177f088f1c5e3737f5697204ff2613"
  integrity sha1-mUZ2/CQXfwiPHF43N/VpcgT/JhM=
  dependencies:
    "@jest/core" "^29.7.0"
    "@jest/types" "^29.6.3"
    import-local "^3.0.2"
    jest-cli "^29.7.0"

js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://registry-npm-next.huolala.work/js-tokens/-/js-tokens-4.0.0.tgz#19203fb59991df98e3a287050d4647cdeaf32499"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-yaml@^3.13.1:
  version "3.14.1"
  resolved "https://registry-npm-next.huolala.work/js-yaml/-/js-yaml-3.14.1.tgz#dae812fdb3825fa306609a8717383c50c36a0537"
  integrity sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

js-yaml@^4.1.0:
  version "4.1.0"
  resolved "https://registry-npm-next.huolala.work/js-yaml/-/js-yaml-4.1.0.tgz#c1fb65f8f5017901cdd2c951864ba18458a10602"
  integrity sha1-wftl+PUBeQHN0slRhkuhhFihBgI=
  dependencies:
    argparse "^2.0.1"

jsesc@^3.0.2:
  version "3.1.0"
  resolved "https://registry-npm-next.huolala.work/jsesc/-/jsesc-3.1.0.tgz#74d335a234f67ed19907fdadfac7ccf9d409825d"
  integrity sha1-dNM1ojT2ftGZB/2t+sfM+dQJgl0=

json-buffer@3.0.1:
  version "3.0.1"
  resolved "https://registry-npm-next.huolala.work/json-buffer/-/json-buffer-3.0.1.tgz#9338802a30d3b6605fbe0613e094008ca8c05a13"
  integrity sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=

json-parse-even-better-errors@^2.3.0:
  version "2.3.1"
  resolved "https://registry-npm-next.huolala.work/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz#7c47805a94319928e05777405dc12e1f7a4ee02d"
  integrity sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://registry-npm-next.huolala.work/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz#69f6a87d9513ab8bb8fe63bdb0979c448e684660"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "https://registry-npm-next.huolala.work/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz#9db7b59496ad3f3cfef30a75142d2d930ad72651"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

json5@^2.2.3:
  version "2.2.3"
  resolved "https://registry-npm-next.huolala.work/json5/-/json5-2.2.3.tgz#78cd6f1a19bdc12b73db5ad0c61efd66c1e29283"
  integrity sha1-eM1vGhm9wStz21rQxh79ZsHikoM=

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "https://registry-npm-next.huolala.work/jsonfile/-/jsonfile-6.1.0.tgz#bc55b2634793c679ec6403094eb13698a6ec0aae"
  integrity sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

keyv@^4.5.4:
  version "4.5.4"
  resolved "https://registry-npm-next.huolala.work/keyv/-/keyv-4.5.4.tgz#a879a99e29452f942439f2a405e3af8b31d4de93"
  integrity sha1-qHmpnilFL5QkOfKkBeOvizHU3pM=
  dependencies:
    json-buffer "3.0.1"

kleur@^3.0.3:
  version "3.0.3"
  resolved "https://registry-npm-next.huolala.work/kleur/-/kleur-3.0.3.tgz#a79c9ecc86ee1ce3fa6206d1216c501f147fc07e"
  integrity sha1-p5yezIbuHOP6YgbRIWxQHxR/wH4=

leven@^3.1.0:
  version "3.1.0"
  resolved "https://registry-npm-next.huolala.work/leven/-/leven-3.1.0.tgz#77891de834064cccba82ae7842bb6b14a13ed7f2"
  integrity sha1-d4kd6DQGTMy6gq54QrtrFKE+1/I=

levn@^0.4.1:
  version "0.4.1"
  resolved "https://registry-npm-next.huolala.work/levn/-/levn-0.4.1.tgz#ae4562c007473b932a6200d403268dd2fffc6ade"
  integrity sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "https://registry-npm-next.huolala.work/lines-and-columns/-/lines-and-columns-1.2.4.tgz#eca284f75d2965079309dc0ad9255abb2ebc1632"
  integrity sha1-7KKE910pZQeTCdwK2SVauy68FjI=

locate-path@^5.0.0:
  version "5.0.0"
  resolved "https://registry-npm-next.huolala.work/locate-path/-/locate-path-5.0.0.tgz#1afba396afd676a6d42504d0a67a3a7eb9f62aa0"
  integrity sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=
  dependencies:
    p-locate "^4.1.0"

locate-path@^6.0.0:
  version "6.0.0"
  resolved "https://registry-npm-next.huolala.work/locate-path/-/locate-path-6.0.0.tgz#55321eb309febbc59c4801d931a72452a681d286"
  integrity sha1-VTIeswn+u8WcSAHZMackUqaB0oY=
  dependencies:
    p-locate "^5.0.0"

lodash.memoize@^4.1.2:
  version "4.1.2"
  resolved "https://registry-npm-next.huolala.work/lodash.memoize/-/lodash.memoize-4.1.2.tgz#bcc6c49a42a2840ed997f323eada5ecd182e0bfe"
  integrity sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4=

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "https://registry-npm-next.huolala.work/lodash.merge/-/lodash.merge-4.6.2.tgz#558aa53b43b661e1925a0afdfa36a9a1085fe57a"
  integrity sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "https://registry-npm-next.huolala.work/lru-cache/-/lru-cache-5.1.1.tgz#1da27e6710271947695daf6848e847f01d84b920"
  integrity sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=
  dependencies:
    yallist "^3.0.2"

magic-string@^0.30.17, magic-string@^0.30.3:
  version "0.30.17"
  resolved "https://registry-npm-next.huolala.work/magic-string/-/magic-string-0.30.17.tgz#450a449673d2460e5bbcfba9a61916a1714c7453"
  integrity sha1-RQpElnPSRg5bvPupphkWoXFMdFM=
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.5.0"

make-dir@^4.0.0:
  version "4.0.0"
  resolved "https://registry-npm-next.huolala.work/make-dir/-/make-dir-4.0.0.tgz#c3c2307a771277cd9638305f915c29ae741b614e"
  integrity sha1-w8IwencSd82WODBfkVwprnQbYU4=
  dependencies:
    semver "^7.5.3"

make-error@^1.3.6:
  version "1.3.6"
  resolved "https://registry-npm-next.huolala.work/make-error/-/make-error-1.3.6.tgz#2eb2e37ea9b67c4891f684a1394799af484cf7a2"
  integrity sha1-LrLjfqm2fEiR9oShOUeZr0hM96I=

makeerror@1.0.12:
  version "1.0.12"
  resolved "https://registry-npm-next.huolala.work/makeerror/-/makeerror-1.0.12.tgz#3e5dd2079a82e812e983cc6610c4a2cb0eaa801a"
  integrity sha1-Pl3SB5qC6BLpg8xmEMSiyw6qgBo=
  dependencies:
    tmpl "1.0.5"

marked@^15.0.12:
  version "15.0.12"
  resolved "https://registry-npm-next.huolala.work/marked/-/marked-15.0.12.tgz#30722c7346e12d0a2d0207ab9b0c4f0102d86c4e"
  integrity sha1-MHIsc0bhLQotAgermwxPAQLYbE4=

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "https://registry-npm-next.huolala.work/merge-stream/-/merge-stream-2.0.0.tgz#52823629a14dd00c9770fb6ad47dc6310f2c1f60"
  integrity sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=

merge2@^1.3.0, merge2@^1.4.1:
  version "1.4.1"
  resolved "https://registry-npm-next.huolala.work/merge2/-/merge2-1.4.1.tgz#4368892f885e907455a6fd7dc55c0c9d404990ae"
  integrity sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=

micromatch@^4.0.4, micromatch@^4.0.8:
  version "4.0.8"
  resolved "https://registry-npm-next.huolala.work/micromatch/-/micromatch-4.0.8.tgz#d66fa18f3a47076789320b9b1af32bd86d9fa202"
  integrity sha1-1m+hjzpHB2eJMgubGvMr2G2fogI=
  dependencies:
    braces "^3.0.3"
    picomatch "^2.3.1"

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "https://registry-npm-next.huolala.work/mimic-fn/-/mimic-fn-2.1.0.tgz#7ed2c2ccccaf84d3ffcb7a69b57711fc2083401b"
  integrity sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=

minimatch@^3.0.4, minimatch@^3.1.1, minimatch@^3.1.2:
  version "3.1.2"
  resolved "https://registry-npm-next.huolala.work/minimatch/-/minimatch-3.1.2.tgz#19cd194bfd3e428f049a70817c038d89ab4be35b"
  integrity sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^5.0.1:
  version "5.1.6"
  resolved "https://registry-npm-next.huolala.work/minimatch/-/minimatch-5.1.6.tgz#1cfcb8cf5522ea69952cd2af95ae09477f122a96"
  integrity sha1-HPy4z1Ui6mmVLNKvla4JR38SKpY=
  dependencies:
    brace-expansion "^2.0.1"

minimatch@^9.0.4:
  version "9.0.5"
  resolved "https://registry-npm-next.huolala.work/minimatch/-/minimatch-9.0.5.tgz#d74f9dd6b57d83d8e98cfb82133b03978bc929e5"
  integrity sha1-10+d1rV9g9jpjPuCEzsDl4vJKeU=
  dependencies:
    brace-expansion "^2.0.1"

monaco-editor@^0.52.2:
  version "0.52.2"
  resolved "https://registry-npm-next.huolala.work/monaco-editor/-/monaco-editor-0.52.2.tgz#53c75a6fcc6802684e99fd1b2700299857002205"
  integrity sha1-U8dab8xoAmhOmf0bJwApmFcAIgU=

ms@^2.1.3:
  version "2.1.3"
  resolved "https://registry-npm-next.huolala.work/ms/-/ms-2.1.3.tgz#574c8138ce1d2b5861f0b44579dbadd60c6615b2"
  integrity sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=

nanoid@^3.3.11:
  version "3.3.11"
  resolved "https://registry-npm-next.huolala.work/nanoid/-/nanoid-3.3.11.tgz#4f4f112cefbe303202f2199838128936266d185b"
  integrity sha1-T08RLO++MDIC8hmYOBKJNiZtGFs=

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "https://registry-npm-next.huolala.work/natural-compare/-/natural-compare-1.4.0.tgz#4abebfeed7541f2c27acfb29bdbbd15c8d5ba4f7"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

node-int64@^0.4.0:
  version "0.4.0"
  resolved "https://registry-npm-next.huolala.work/node-int64/-/node-int64-0.4.0.tgz#87a9065cdb355d3182d8f94ce11188b825c68a3b"
  integrity sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=

node-releases@^2.0.19:
  version "2.0.19"
  resolved "https://registry-npm-next.huolala.work/node-releases/-/node-releases-2.0.19.tgz#9e445a52950951ec4d177d843af370b411caf314"
  integrity sha1-nkRaUpUJUexNF32EOvNwtBHK8xQ=

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://registry-npm-next.huolala.work/normalize-path/-/normalize-path-3.0.0.tgz#0dcd69ff23a1c9b11fd0978316644a0388216a65"
  integrity sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=

npm-run-path@^4.0.1:
  version "4.0.1"
  resolved "https://registry-npm-next.huolala.work/npm-run-path/-/npm-run-path-4.0.1.tgz#b7ecd1e5ed53da8e37a55e1c2269e0b97ed748ea"
  integrity sha1-t+zR5e1T2o43pV4cImnguX7XSOo=
  dependencies:
    path-key "^3.0.0"

once@^1.3.0:
  version "1.4.0"
  resolved "https://registry-npm-next.huolala.work/once/-/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^5.1.2:
  version "5.1.2"
  resolved "https://registry-npm-next.huolala.work/onetime/-/onetime-5.1.2.tgz#d0e96ebb56b07476df1dd9c4806e5237985ca45e"
  integrity sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=
  dependencies:
    mimic-fn "^2.1.0"

optionator@^0.9.3:
  version "0.9.4"
  resolved "https://registry-npm-next.huolala.work/optionator/-/optionator-0.9.4.tgz#7ea1c1a5d91d764fb282139c88fe11e182a3a734"
  integrity sha1-fqHBpdkddk+yghOciP4R4YKjpzQ=
  dependencies:
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"
    word-wrap "^1.2.5"

p-limit@^2.2.0:
  version "2.3.0"
  resolved "https://registry-npm-next.huolala.work/p-limit/-/p-limit-2.3.0.tgz#3dd33c647a214fdfffd835933eb086da0dc21db1"
  integrity sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=
  dependencies:
    p-try "^2.0.0"

p-limit@^3.0.2, p-limit@^3.1.0:
  version "3.1.0"
  resolved "https://registry-npm-next.huolala.work/p-limit/-/p-limit-3.1.0.tgz#e1daccbe78d0d1388ca18c64fea38e3e57e3706b"
  integrity sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "https://registry-npm-next.huolala.work/p-locate/-/p-locate-4.1.0.tgz#a3428bb7088b3a60292f66919278b7c297ad4f07"
  integrity sha1-o0KLtwiLOmApL2aRkni3wpetTwc=
  dependencies:
    p-limit "^2.2.0"

p-locate@^5.0.0:
  version "5.0.0"
  resolved "https://registry-npm-next.huolala.work/p-locate/-/p-locate-5.0.0.tgz#83c8315c6785005e3bd021839411c9e110e6d834"
  integrity sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=
  dependencies:
    p-limit "^3.0.2"

p-map@^7.0.3:
  version "7.0.3"
  resolved "https://registry-npm-next.huolala.work/p-map/-/p-map-7.0.3.tgz#7ac210a2d36f81ec28b736134810f7ba4418cdb6"
  integrity sha1-esIQotNvgewotzYTSBD3ukQYzbY=

p-try@^2.0.0:
  version "2.2.0"
  resolved "https://registry-npm-next.huolala.work/p-try/-/p-try-2.2.0.tgz#cb2868540e313d61de58fafbe35ce9004d5540e6"
  integrity sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://registry-npm-next.huolala.work/parent-module/-/parent-module-1.0.1.tgz#691d2709e78c79fae3a156622452d00762caaaa2"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

parse-json@^5.2.0:
  version "5.2.0"
  resolved "https://registry-npm-next.huolala.work/parse-json/-/parse-json-5.2.0.tgz#c76fc66dee54231c962b22bcc8a72cf2f99753cd"
  integrity sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

path-exists@^4.0.0:
  version "4.0.0"
  resolved "https://registry-npm-next.huolala.work/path-exists/-/path-exists-4.0.0.tgz#513bdbe2d3b95d7762e8c1137efa195c6c61b5b3"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://registry-npm-next.huolala.work/path-is-absolute/-/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "https://registry-npm-next.huolala.work/path-key/-/path-key-3.1.1.tgz#581f6ade658cbba65a0d3380de7753295054f375"
  integrity sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=

path-parse@^1.0.7:
  version "1.0.7"
  resolved "https://registry-npm-next.huolala.work/path-parse/-/path-parse-1.0.7.tgz#fbc114b60ca42b30d9daf5858e4bd68bbedb6735"
  integrity sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=

path-type@^4.0.0:
  version "4.0.0"
  resolved "https://registry-npm-next.huolala.work/path-type/-/path-type-4.0.0.tgz#84ed01c0a7ba380afe09d90a8c180dcd9d03043b"
  integrity sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=

picocolors@^1.1.1:
  version "1.1.1"
  resolved "https://registry-npm-next.huolala.work/picocolors/-/picocolors-1.1.1.tgz#3d321af3eab939b083c8f929a1d12cda81c26b6b"
  integrity sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.2.3, picomatch@^2.3.1:
  version "2.3.1"
  resolved "https://registry-npm-next.huolala.work/picomatch/-/picomatch-2.3.1.tgz#3ba3833733646d9d3e4995946c1365a67fb07a42"
  integrity sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=

picomatch@^4.0.2:
  version "4.0.2"
  resolved "https://registry-npm-next.huolala.work/picomatch/-/picomatch-4.0.2.tgz#77c742931e8f3b8820946c76cd0c1f13730d1dab"
  integrity sha1-d8dCkx6PO4gglGx2zQwfE3MNHas=

pirates@^4.0.4:
  version "4.0.7"
  resolved "https://registry-npm-next.huolala.work/pirates/-/pirates-4.0.7.tgz#643b4a18c4257c8a65104b73f3049ce9a0a15e22"
  integrity sha1-ZDtKGMQlfIplEEtz8wSc6aChXiI=

pkg-dir@^4.2.0:
  version "4.2.0"
  resolved "https://registry-npm-next.huolala.work/pkg-dir/-/pkg-dir-4.2.0.tgz#f099133df7ede422e81d1d8448270eeb3e4261f3"
  integrity sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=
  dependencies:
    find-up "^4.0.0"

postcss@^8.5.3:
  version "8.5.6"
  resolved "https://registry-npm-next.huolala.work/postcss/-/postcss-8.5.6.tgz#2825006615a619b4f62a9e7426cc120b349a8f3c"
  integrity sha1-KCUAZhWmGbT2Kp50JswSCzSajzw=
  dependencies:
    nanoid "^3.3.11"
    picocolors "^1.1.1"
    source-map-js "^1.2.1"

prelude-ls@^1.2.1:
  version "1.2.1"
  resolved "https://registry-npm-next.huolala.work/prelude-ls/-/prelude-ls-1.2.1.tgz#debc6489d7a6e6b0e7611888cec880337d316396"
  integrity sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=

pretty-format@^29.0.0, pretty-format@^29.7.0:
  version "29.7.0"
  resolved "https://registry-npm-next.huolala.work/pretty-format/-/pretty-format-29.7.0.tgz#ca42c758310f365bfa71a0bda0a807160b776812"
  integrity sha1-ykLHWDEPNlv6caC9oKgHFgt3aBI=
  dependencies:
    "@jest/schemas" "^29.6.3"
    ansi-styles "^5.0.0"
    react-is "^18.0.0"

prompts@^2.0.1:
  version "2.4.2"
  resolved "https://registry-npm-next.huolala.work/prompts/-/prompts-2.4.2.tgz#7b57e73b3a48029ad10ebd44f74b01722a4cb069"
  integrity sha1-e1fnOzpIAprRDr1E90sBcipMsGk=
  dependencies:
    kleur "^3.0.3"
    sisteransi "^1.0.5"

punycode@^2.1.0:
  version "2.3.1"
  resolved "https://registry-npm-next.huolala.work/punycode/-/punycode-2.3.1.tgz#027422e2faec0b25e1549c3e1bd8309b9133b6e5"
  integrity sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=

pure-rand@^6.0.0:
  version "6.1.0"
  resolved "https://registry-npm-next.huolala.work/pure-rand/-/pure-rand-6.1.0.tgz#d173cf23258231976ccbdb05247c9787957604f2"
  integrity sha1-0XPPIyWCMZdsy9sFJHyXh5V2BPI=

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "https://registry-npm-next.huolala.work/queue-microtask/-/queue-microtask-1.2.3.tgz#4929228bbc724dfac43e0efb058caf7b6cfb6243"
  integrity sha1-SSkii7xyTfrEPg77BYyve2z7YkM=

randombytes@^2.1.0:
  version "2.1.0"
  resolved "https://registry-npm-next.huolala.work/randombytes/-/randombytes-2.1.0.tgz#df6f84372f0270dc65cdf6291349ab7a473d4f2a"
  integrity sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=
  dependencies:
    safe-buffer "^5.1.0"

react-dom@^19.1.0:
  version "19.1.0"
  resolved "https://registry-npm-next.huolala.work/react-dom/-/react-dom-19.1.0.tgz#133558deca37fa1d682708df8904b25186793623"
  integrity sha1-EzVY3so3+h1oJwjfiQSyUYZ5NiM=
  dependencies:
    scheduler "^0.26.0"

react-is@^18.0.0:
  version "18.3.1"
  resolved "https://registry-npm-next.huolala.work/react-is/-/react-is-18.3.1.tgz#e83557dc12eae63a99e003a46388b1dcbb44db7e"
  integrity sha1-6DVX3BLq5jqZ4AOkY4ix3LtE234=

react-refresh@^0.17.0:
  version "0.17.0"
  resolved "https://registry-npm-next.huolala.work/react-refresh/-/react-refresh-0.17.0.tgz#b7e579c3657f23d04eccbe4ad2e58a8ed51e7e53"
  integrity sha1-t+V5w2V/I9BOzL5K0uWKjtUeflM=

react-router-dom@^7.6.2:
  version "7.6.3"
  resolved "https://registry-npm-next.huolala.work/react-router-dom/-/react-router-dom-7.6.3.tgz#45686e71bb958cf80dd93ca3abf4111feb4edd35"
  integrity sha1-RWhucbuVjPgN2Tyjq/QRH+tO3TU=
  dependencies:
    react-router "7.6.3"

react-router@7.6.3:
  version "7.6.3"
  resolved "https://registry-npm-next.huolala.work/react-router/-/react-router-7.6.3.tgz#7a4ea5b479b66d2c49a8f000812b2319b4d0a6da"
  integrity sha1-ek6ltHm2bSxJqPAAgSsjGbTQpto=
  dependencies:
    cookie "^1.0.1"
    set-cookie-parser "^2.6.0"

react@>=18.0.0, react@^19.1.0:
  version "19.1.0"
  resolved "https://registry-npm-next.huolala.work/react/-/react-19.1.0.tgz#926864b6c48da7627f004795d6cce50e90793b75"
  integrity sha1-kmhktsSNp2J/AEeV1szlDpB5O3U=

readdirp@~3.6.0:
  version "3.6.0"
  resolved "https://registry-npm-next.huolala.work/readdirp/-/readdirp-3.6.0.tgz#74a370bd857116e245b29cc97340cd431a02a6c7"
  integrity sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=
  dependencies:
    picomatch "^2.2.1"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "https://registry-npm-next.huolala.work/require-directory/-/require-directory-2.1.1.tgz#8c64ad5fd30dab1c976e2344ffe7f792a6a6df42"
  integrity sha1-jGStX9MNqxyXbiNE/+f3kqam30I=

resolve-cwd@^3.0.0:
  version "3.0.0"
  resolved "https://registry-npm-next.huolala.work/resolve-cwd/-/resolve-cwd-3.0.0.tgz#0f0075f1bb2544766cf73ba6a6e2adfebcb13f2d"
  integrity sha1-DwB18bslRHZs9zumpuKt/ryxPy0=
  dependencies:
    resolve-from "^5.0.0"

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://registry-npm-next.huolala.work/resolve-from/-/resolve-from-4.0.0.tgz#4abcd852ad32dd7baabfe9b40e00a36db5f392e6"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

resolve-from@^5.0.0:
  version "5.0.0"
  resolved "https://registry-npm-next.huolala.work/resolve-from/-/resolve-from-5.0.0.tgz#c35225843df8f776df21c57557bc087e9dfdfc69"
  integrity sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=

resolve.exports@^2.0.0:
  version "2.0.3"
  resolved "https://registry-npm-next.huolala.work/resolve.exports/-/resolve.exports-2.0.3.tgz#41955e6f1b4013b7586f873749a635dea07ebe3f"
  integrity sha1-QZVebxtAE7dYb4c3SaY13qB+vj8=

resolve@^1.20.0, resolve@^1.22.1:
  version "1.22.10"
  resolved "https://registry-npm-next.huolala.work/resolve/-/resolve-1.22.10.tgz#b663e83ffb09bbf2386944736baae803029b8b39"
  integrity sha1-tmPoP/sJu/I4aURza6roAwKbizk=
  dependencies:
    is-core-module "^2.16.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

reusify@^1.0.4:
  version "1.1.0"
  resolved "https://registry-npm-next.huolala.work/reusify/-/reusify-1.1.0.tgz#0fe13b9522e1473f51b558ee796e08f11f9b489f"
  integrity sha1-D+E7lSLhRz9RtVjueW4I8R+bSJ8=

rollup-plugin-dts@latest:
  version "6.2.1"
  resolved "https://registry-npm-next.huolala.work/rollup-plugin-dts/-/rollup-plugin-dts-6.2.1.tgz#120a40734f740115da44931d7915a370fe420701"
  integrity sha1-EgpAc090ARXaRJMdeRWjcP5CBwE=
  dependencies:
    magic-string "^0.30.17"
  optionalDependencies:
    "@babel/code-frame" "^7.26.2"

rollup@^4.34.9, rollup@^4.41.1:
  version "4.45.0"
  resolved "https://registry-npm-next.huolala.work/rollup/-/rollup-4.45.0.tgz#92d1b164eca1c6f2cb399ae7a1a8ee78967b6e33"
  integrity sha1-ktGxZOyhxvLLOZrnoajueJZ7bjM=
  dependencies:
    "@types/estree" "1.0.8"
  optionalDependencies:
    "@rollup/rollup-android-arm-eabi" "4.45.0"
    "@rollup/rollup-android-arm64" "4.45.0"
    "@rollup/rollup-darwin-arm64" "4.45.0"
    "@rollup/rollup-darwin-x64" "4.45.0"
    "@rollup/rollup-freebsd-arm64" "4.45.0"
    "@rollup/rollup-freebsd-x64" "4.45.0"
    "@rollup/rollup-linux-arm-gnueabihf" "4.45.0"
    "@rollup/rollup-linux-arm-musleabihf" "4.45.0"
    "@rollup/rollup-linux-arm64-gnu" "4.45.0"
    "@rollup/rollup-linux-arm64-musl" "4.45.0"
    "@rollup/rollup-linux-loongarch64-gnu" "4.45.0"
    "@rollup/rollup-linux-powerpc64le-gnu" "4.45.0"
    "@rollup/rollup-linux-riscv64-gnu" "4.45.0"
    "@rollup/rollup-linux-riscv64-musl" "4.45.0"
    "@rollup/rollup-linux-s390x-gnu" "4.45.0"
    "@rollup/rollup-linux-x64-gnu" "4.45.0"
    "@rollup/rollup-linux-x64-musl" "4.45.0"
    "@rollup/rollup-win32-arm64-msvc" "4.45.0"
    "@rollup/rollup-win32-ia32-msvc" "4.45.0"
    "@rollup/rollup-win32-x64-msvc" "4.45.0"
    fsevents "~2.3.2"

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "https://registry-npm-next.huolala.work/run-parallel/-/run-parallel-1.2.0.tgz#66d1368da7bdf921eb9d95bd1a9229e7f21a43ee"
  integrity sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=
  dependencies:
    queue-microtask "^1.2.2"

safe-buffer@^5.1.0:
  version "5.2.1"
  resolved "https://registry-npm-next.huolala.work/safe-buffer/-/safe-buffer-5.2.1.tgz#1eaf9fa9bdb1fdd4ec75f58f9cdb4e6b7827eec6"
  integrity sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=

scheduler@^0.26.0:
  version "0.26.0"
  resolved "https://registry-npm-next.huolala.work/scheduler/-/scheduler-0.26.0.tgz#4ce8a8c2a2095f13ea11bf9a445be50c555d6337"
  integrity sha1-TOiowqIJXxPqEb+aRFvlDFVdYzc=

semver@^6.3.0, semver@^6.3.1:
  version "6.3.1"
  resolved "https://registry-npm-next.huolala.work/semver/-/semver-6.3.1.tgz#556d2ef8689146e46dcea4bfdd095f3434dffcb4"
  integrity sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=

semver@^7.5.3, semver@^7.5.4, semver@^7.6.0, semver@^7.7.2:
  version "7.7.2"
  resolved "https://registry-npm-next.huolala.work/semver/-/semver-7.7.2.tgz#67d99fdcd35cec21e6f8b87a7fd515a33f982b58"
  integrity sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=

serialize-javascript@^6.0.1:
  version "6.0.2"
  resolved "https://registry-npm-next.huolala.work/serialize-javascript/-/serialize-javascript-6.0.2.tgz#defa1e055c83bf6d59ea805d8da862254eb6a6c2"
  integrity sha1-3voeBVyDv21Z6oBdjahiJU62psI=
  dependencies:
    randombytes "^2.1.0"

set-cookie-parser@^2.6.0:
  version "2.7.1"
  resolved "https://registry-npm-next.huolala.work/set-cookie-parser/-/set-cookie-parser-2.7.1.tgz#3016f150072202dfbe90fadee053573cc89d2943"
  integrity sha1-MBbxUAciAt++kPre4FNXPMidKUM=

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://registry-npm-next.huolala.work/shebang-command/-/shebang-command-2.0.0.tgz#ccd0af4f8835fbdc265b82461aaf0c36663f34ea"
  integrity sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry-npm-next.huolala.work/shebang-regex/-/shebang-regex-3.0.0.tgz#ae16f1644d873ecad843b0307b143362d4c42172"
  integrity sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=

signal-exit@^3.0.3, signal-exit@^3.0.7:
  version "3.0.7"
  resolved "https://registry-npm-next.huolala.work/signal-exit/-/signal-exit-3.0.7.tgz#a9a1767f8af84155114eaabd73f99273c8f59ad9"
  integrity sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=

sisteransi@^1.0.5:
  version "1.0.5"
  resolved "https://registry-npm-next.huolala.work/sisteransi/-/sisteransi-1.0.5.tgz#134d681297756437cc05ca01370d3a7a571075ed"
  integrity sha1-E01oEpd1ZDfMBcoBNw06elcQde0=

slash@^3.0.0:
  version "3.0.0"
  resolved "https://registry-npm-next.huolala.work/slash/-/slash-3.0.0.tgz#6539be870c165adbd5240220dbe361f1bc4d4634"
  integrity sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=

smob@^1.0.0:
  version "1.5.0"
  resolved "https://registry-npm-next.huolala.work/smob/-/smob-1.5.0.tgz#85d79a1403abf128d24d3ebc1cdc5e1a9548d3ab"
  integrity sha1-hdeaFAOr8SjSTT68HNxeGpVI06s=

source-map-js@^1.2.1:
  version "1.2.1"
  resolved "https://registry-npm-next.huolala.work/source-map-js/-/source-map-js-1.2.1.tgz#1ce5650fddd87abc099eda37dcff024c2667ae46"
  integrity sha1-HOVlD93YerwJnto33P8CTCZnrkY=

source-map-support@0.5.13:
  version "0.5.13"
  resolved "https://registry-npm-next.huolala.work/source-map-support/-/source-map-support-0.5.13.tgz#31b24a9c2e73c2de85066c0feb7d44767ed52932"
  integrity sha1-MbJKnC5zwt6FBmwP631Edn7VKTI=
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map-support@~0.5.20:
  version "0.5.21"
  resolved "https://registry-npm-next.huolala.work/source-map-support/-/source-map-support-0.5.21.tgz#04fe7c7f9e1ed2d662233c28cb2b35b9f63f6e4f"
  integrity sha1-BP58f54e0tZiIzwoyys1ufY/bk8=
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map@^0.6.0, source-map@^0.6.1:
  version "0.6.1"
  resolved "https://registry-npm-next.huolala.work/source-map/-/source-map-0.6.1.tgz#74722af32e9614e9c287a8d0bbde48b5e2f1a263"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://registry-npm-next.huolala.work/sprintf-js/-/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"
  integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=

stack-utils@^2.0.3:
  version "2.0.6"
  resolved "https://registry-npm-next.huolala.work/stack-utils/-/stack-utils-2.0.6.tgz#aaf0748169c02fc33c8232abccf933f54a1cc34f"
  integrity sha1-qvB0gWnAL8M8gjKrzPkz9Uocw08=
  dependencies:
    escape-string-regexp "^2.0.0"

state-local@^1.0.6:
  version "1.0.7"
  resolved "https://registry-npm-next.huolala.work/state-local/-/state-local-1.0.7.tgz#da50211d07f05748d53009bee46307a37db386d5"
  integrity sha1-2lAhHQfwV0jVMAm+5GMHo32zhtU=

string-length@^4.0.1:
  version "4.0.2"
  resolved "https://registry-npm-next.huolala.work/string-length/-/string-length-4.0.2.tgz#a8a8dc7bd5c1a82b9b3c8b87e125f66871b6e57a"
  integrity sha1-qKjce9XBqCubPIuH4SX2aHG25Xo=
  dependencies:
    char-regex "^1.0.2"
    strip-ansi "^6.0.0"

string-width@^4.1.0, string-width@^4.2.0, string-width@^4.2.3:
  version "4.2.3"
  resolved "https://registry-npm-next.huolala.work/string-width/-/string-width-4.2.3.tgz#269c7117d27b05ad2e536830a8ec895ef9c6d010"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "https://registry-npm-next.huolala.work/strip-ansi/-/strip-ansi-6.0.1.tgz#9e26c63d30f53443e9489495b2105d37b67a85d9"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-bom@^4.0.0:
  version "4.0.0"
  resolved "https://registry-npm-next.huolala.work/strip-bom/-/strip-bom-4.0.0.tgz#9c3505c1db45bcedca3d9cf7a16f5c5aa3901878"
  integrity sha1-nDUFwdtFvO3KPZz3oW9cWqOQGHg=

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "https://registry-npm-next.huolala.work/strip-final-newline/-/strip-final-newline-2.0.0.tgz#89b852fb2fcbe936f6f4b3187afb0a12c1ab58ad"
  integrity sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=

strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "https://registry-npm-next.huolala.work/strip-json-comments/-/strip-json-comments-3.1.1.tgz#31f1281b3832630434831c310c01cccda8cbe006"
  integrity sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=

supports-color@^7.1.0:
  version "7.2.0"
  resolved "https://registry-npm-next.huolala.work/supports-color/-/supports-color-7.2.0.tgz#1b7dcdcb32b8138801b3e478ba6a51caa89648da"
  integrity sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=
  dependencies:
    has-flag "^4.0.0"

supports-color@^8.0.0:
  version "8.1.1"
  resolved "https://registry-npm-next.huolala.work/supports-color/-/supports-color-8.1.1.tgz#cd6fc17e28500cff56c1b86c0a7fd4a54a73005c"
  integrity sha1-zW/BfihQDP9WwbhsCn/UpUpzAFw=
  dependencies:
    has-flag "^4.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "https://registry-npm-next.huolala.work/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz#6eda4bd344a3c94aea376d4cc31bc77311039e09"
  integrity sha1-btpL00SjyUrqN21MwxvHcxEDngk=

terser@^5.17.4:
  version "5.43.1"
  resolved "https://registry-npm-next.huolala.work/terser/-/terser-5.43.1.tgz#88387f4f9794ff1a29e7ad61fb2932e25b4fdb6d"
  integrity sha1-iDh/T5eU/xop561h+yky4ltP220=
  dependencies:
    "@jridgewell/source-map" "^0.3.3"
    acorn "^8.14.0"
    commander "^2.20.0"
    source-map-support "~0.5.20"

test-exclude@^6.0.0:
  version "6.0.0"
  resolved "https://registry-npm-next.huolala.work/test-exclude/-/test-exclude-6.0.0.tgz#04a8698661d805ea6fa293b6cb9e63ac044ef15e"
  integrity sha1-BKhphmHYBepvopO2y55jrARO8V4=
  dependencies:
    "@istanbuljs/schema" "^0.1.2"
    glob "^7.1.4"
    minimatch "^3.0.4"

tinyglobby@^0.2.13, tinyglobby@^0.2.14:
  version "0.2.14"
  resolved "https://registry-npm-next.huolala.work/tinyglobby/-/tinyglobby-0.2.14.tgz#5280b0cf3f972b050e74ae88406c0a6a58f4079d"
  integrity sha1-UoCwzz+XKwUOdK6IQGwKalj0B50=
  dependencies:
    fdir "^6.4.4"
    picomatch "^4.0.2"

tmpl@1.0.5:
  version "1.0.5"
  resolved "https://registry-npm-next.huolala.work/tmpl/-/tmpl-1.0.5.tgz#8683e0b902bb9c20c4f726e3c0b69f36518c07cc"
  integrity sha1-hoPguQK7nCDE9ybjwLafNlGMB8w=

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://registry-npm-next.huolala.work/to-regex-range/-/to-regex-range-5.0.1.tgz#1648c44aae7c8d988a326018ed72f5b4dd0392e4"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

ts-api-utils@^2.1.0:
  version "2.1.0"
  resolved "https://registry-npm-next.huolala.work/ts-api-utils/-/ts-api-utils-2.1.0.tgz#595f7094e46eed364c13fd23e75f9513d29baf91"
  integrity sha1-WV9wlORu7TZME/0j51+VE9Kbr5E=

ts-jest@^29.3.4:
  version "29.4.0"
  resolved "https://registry-npm-next.huolala.work/ts-jest/-/ts-jest-29.4.0.tgz#bef0ee98d94c83670af7462a1617bf2367a83740"
  integrity sha1-vvDumNlMg2cK90YqFhe/I2eoN0A=
  dependencies:
    bs-logger "^0.2.6"
    ejs "^3.1.10"
    fast-json-stable-stringify "^2.1.0"
    json5 "^2.2.3"
    lodash.memoize "^4.1.2"
    make-error "^1.3.6"
    semver "^7.7.2"
    type-fest "^4.41.0"
    yargs-parser "^21.1.1"

tslib@^2.6.0:
  version "2.8.1"
  resolved "https://registry-npm-next.huolala.work/tslib/-/tslib-2.8.1.tgz#612efe4ed235d567e8aba5f2a5fab70280ade83f"
  integrity sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  resolved "https://registry-npm-next.huolala.work/type-check/-/type-check-0.4.0.tgz#07b8203bfa7056c0657050e3ccd2c37730bab8f1"
  integrity sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=
  dependencies:
    prelude-ls "^1.2.1"

type-detect@4.0.8:
  version "4.0.8"
  resolved "https://registry-npm-next.huolala.work/type-detect/-/type-detect-4.0.8.tgz#7646fb5f18871cfbb7749e69bd39a6388eb7450c"
  integrity sha1-dkb7XxiHHPu3dJ5pvTmmOI63RQw=

type-fest@^0.21.3:
  version "0.21.3"
  resolved "https://registry-npm-next.huolala.work/type-fest/-/type-fest-0.21.3.tgz#d260a24b0198436e133fa26a524a6d65fa3b2e37"
  integrity sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc=

type-fest@^4.41.0:
  version "4.41.0"
  resolved "https://registry-npm-next.huolala.work/type-fest/-/type-fest-4.41.0.tgz#6ae1c8e5731273c2bf1f58ad39cbae2c91a46c58"
  integrity sha1-auHI5XMSc8K/H1itOcuuLJGkbFg=

typescript-eslint@^8.30.1:
  version "8.36.0"
  resolved "https://registry-npm-next.huolala.work/typescript-eslint/-/typescript-eslint-8.36.0.tgz#6c87d5ccf1bd45a849c159e2387bb65b6068ed90"
  integrity sha1-bIfVzPG9RahJwVniOHu2W2Bo7ZA=
  dependencies:
    "@typescript-eslint/eslint-plugin" "8.36.0"
    "@typescript-eslint/parser" "8.36.0"
    "@typescript-eslint/utils" "8.36.0"

typescript@~5.8.3:
  version "5.8.3"
  resolved "https://registry-npm-next.huolala.work/typescript/-/typescript-5.8.3.tgz#92f8a3e5e3cf497356f4178c34cd65a7f5e8440e"
  integrity sha1-kvij5ePPSXNW9BeMNM1lp/XoRA4=

u2x@^1.1.0:
  version "1.1.0"
  resolved "https://registry-npm-next.huolala.work/u2x/-/u2x-1.1.0.tgz#a3210eab20985b5c1cf807ec4147f016a51d71d7"
  integrity sha1-oyEOqyCYW1wc+AfsQUfwFqUdcdc=

undici-types@~7.8.0:
  version "7.8.0"
  resolved "https://registry-npm-next.huolala.work/undici-types/-/undici-types-7.8.0.tgz#de00b85b710c54122e44fbfd911f8d70174cd294"
  integrity sha1-3gC4W3EMVBIuRPv9kR+NcBdM0pQ=

universalify@^2.0.0:
  version "2.0.1"
  resolved "https://registry-npm-next.huolala.work/universalify/-/universalify-2.0.1.tgz#168efc2180964e6386d061e094df61afe239b18d"
  integrity sha1-Fo78IYCWTmOG0GHglN9hr+I5sY0=

update-browserslist-db@^1.1.3:
  version "1.1.3"
  resolved "https://registry-npm-next.huolala.work/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz#348377dd245216f9e7060ff50b15a1b740b75420"
  integrity sha1-NIN33SRSFvnnBg/1CxWht0C3VCA=
  dependencies:
    escalade "^3.2.0"
    picocolors "^1.1.1"

uri-js@^4.2.2:
  version "4.4.1"
  resolved "https://registry-npm-next.huolala.work/uri-js/-/uri-js-4.4.1.tgz#9b1a52595225859e55f669d928f88c6c57f2a77e"
  integrity sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=
  dependencies:
    punycode "^2.1.0"

v8-to-istanbul@^9.0.1:
  version "9.3.0"
  resolved "https://registry-npm-next.huolala.work/v8-to-istanbul/-/v8-to-istanbul-9.3.0.tgz#b9572abfa62bd556c16d75fdebc1a411d5ff3175"
  integrity sha1-uVcqv6Yr1VbBbXX968GkEdX/MXU=
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.12"
    "@types/istanbul-lib-coverage" "^2.0.1"
    convert-source-map "^2.0.0"

vite-plugin-static-copy@^3.1.0:
  version "3.1.0"
  resolved "https://registry-npm-next.huolala.work/vite-plugin-static-copy/-/vite-plugin-static-copy-3.1.0.tgz#9bf3675bc2d3ba7472217e7ffed65cfbd6c123fb"
  integrity sha1-m/NnW8LTunRyIX5//tZc+9bBI/s=
  dependencies:
    chokidar "^3.5.3"
    fs-extra "^11.3.0"
    p-map "^7.0.3"
    picocolors "^1.1.1"
    tinyglobby "^0.2.14"

vite@^6.3.5:
  version "6.3.5"
  resolved "https://registry-npm-next.huolala.work/vite/-/vite-6.3.5.tgz#fec73879013c9c0128c8d284504c6d19410d12a3"
  integrity sha1-/sc4eQE8nAEoyNKEUExtGUENEqM=
  dependencies:
    esbuild "^0.25.0"
    fdir "^6.4.4"
    picomatch "^4.0.2"
    postcss "^8.5.3"
    rollup "^4.34.9"
    tinyglobby "^0.2.13"
  optionalDependencies:
    fsevents "~2.3.3"

walker@^1.0.8:
  version "1.0.8"
  resolved "https://registry-npm-next.huolala.work/walker/-/walker-1.0.8.tgz#bd498db477afe573dc04185f011d3ab8a8d7653f"
  integrity sha1-vUmNtHev5XPcBBhfAR06uKjXZT8=
  dependencies:
    makeerror "1.0.12"

which@^2.0.1:
  version "2.0.2"
  resolved "https://registry-npm-next.huolala.work/which/-/which-2.0.2.tgz#7c6a8dd0a636a0327e10b59c9286eee93f3f51b1"
  integrity sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=
  dependencies:
    isexe "^2.0.0"

word-wrap@^1.2.5:
  version "1.2.5"
  resolved "https://registry-npm-next.huolala.work/word-wrap/-/word-wrap-1.2.5.tgz#d2c45c6dd4fbce621a66f136cbe328afd0410b34"
  integrity sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=

wrap-ansi@^7.0.0:
  version "7.0.0"
  resolved "https://registry-npm-next.huolala.work/wrap-ansi/-/wrap-ansi-7.0.0.tgz#67e145cff510a6a6984bdf1152911d69d2eb9e43"
  integrity sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrappy@1:
  version "1.0.2"
  resolved "https://registry-npm-next.huolala.work/wrappy/-/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

write-file-atomic@^4.0.2:
  version "4.0.2"
  resolved "https://registry-npm-next.huolala.work/write-file-atomic/-/write-file-atomic-4.0.2.tgz#a9df01ae5b77858a027fd2e80768ee433555fcfd"
  integrity sha1-qd8Brlt3hYoCf9LoB2juQzVV/P0=
  dependencies:
    imurmurhash "^0.1.4"
    signal-exit "^3.0.7"

y18n@^5.0.5:
  version "5.0.8"
  resolved "https://registry-npm-next.huolala.work/y18n/-/y18n-5.0.8.tgz#7f4934d0f7ca8c56f95314939ddcd2dd91ce1d55"
  integrity sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=

yallist@^3.0.2:
  version "3.1.1"
  resolved "https://registry-npm-next.huolala.work/yallist/-/yallist-3.1.1.tgz#dbb7daf9bfd8bac9ab45ebf602b8cbad0d5d08fd"
  integrity sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=

yargs-parser@^21.1.1:
  version "21.1.1"
  resolved "https://registry-npm-next.huolala.work/yargs-parser/-/yargs-parser-21.1.1.tgz#9096bceebf990d21bb31fa9516e0ede294a77d35"
  integrity sha1-kJa87r+ZDSG7MfqVFuDt4pSnfTU=

yargs@^17.3.1:
  version "17.7.2"
  resolved "https://registry-npm-next.huolala.work/yargs/-/yargs-17.7.2.tgz#991df39aca675a192b816e1e0363f9d75d2aa269"
  integrity sha1-mR3zmspnWhkrgW4eA2P5110qomk=
  dependencies:
    cliui "^8.0.1"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.3"
    y18n "^5.0.5"
    yargs-parser "^21.1.1"

yocto-queue@^0.1.0:
  version "0.1.0"
  resolved "https://registry-npm-next.huolala.work/yocto-queue/-/yocto-queue-0.1.0.tgz#0294eb3dee05028d31ee1a5fa2c556a6aaf10a1b"
  integrity sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=
