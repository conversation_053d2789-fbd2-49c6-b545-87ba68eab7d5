package cn.lalaframework.quantumbox;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.Nullable;

@Getter
@Setter
public class JsonSchemaArray<T extends JsonSchemaValue> extends JsonSchemaAbstract {
    @Nullable
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private T items;

    @Nullable
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer minItems;

    @Nullable
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer maxItems;

    public JsonSchemaArray() {
        super(JsonSchemaType.ARRAY);
    }

    public JsonSchemaArray(T items) {
        this();
        this.items = items;
    }
}
