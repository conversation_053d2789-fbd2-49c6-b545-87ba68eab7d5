package cn.lalaframework.quantumbox;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class JsonSchemaString extends JsonSchemaAbstract implements QueryParameter, PrimitiveParameter {
    @Nullable
    @JsonProperty("enum")
    @JsonInclude(value = JsonInclude.Include.NON_EMPTY)
    private List<String> allowedValues;

    @Nullable
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    private String format;

    @Nullable
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    private String pattern;

    public JsonSchemaString() {
        super(JsonSchemaType.STRING);
    }

    @NonNull
    public List<String> getAllowedValues() {
        if (allowedValues == null) allowedValues = new ArrayList<>();
        return allowedValues;
    }
}
