package cn.lalaframework.quantumbox;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

@Getter
@Setter
abstract class JsonSchemaAbstract implements JsonSchemaValue {
    @NonNull
    private JsonSchemaType type;

    @Nullable
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String description;

    protected JsonSchemaAbstract(JsonSchemaType type) {
        this.type = type;
    }
}
