package cn.lalaframework.quantumbox;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.lang.Nullable;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AgentConfigOptions {
    @Nullable
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String systemMessage;
    @Nullable
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String welcomeMessage;
    @Nullable
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<ApiTool> additionalApiTools;
}
