package cn.lalaframework.quantumbox;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.lang.Nullable;

@Getter
@Setter
@NoArgsConstructor
public class AgentConfig {
    @Nullable
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    private String name;
    @Nullable
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    private String description;
    @Nullable
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    private AgentConfigOptions options;
}
