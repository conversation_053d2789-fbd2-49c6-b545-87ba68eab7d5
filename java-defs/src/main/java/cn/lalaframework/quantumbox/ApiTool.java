package cn.lalaframework.quantumbox;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.Nullable;

import java.util.Map;

@Getter
@Setter
public class ApiTool {
    @Nullable
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String name;

    @Nullable
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String description;

    @Nullable
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private HttpMethod method;

    @Nullable
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String url;

    @Nullable
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Map<String, String> headers;

    @Nullable
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Map<String, PrimitiveParameter> pathVariables;

    @Nullable
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private JsonSchemaObject<QueryParameter> query;

    @Nullable
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private JsonSchemaObject<JsonSchemaValue> body;
}
