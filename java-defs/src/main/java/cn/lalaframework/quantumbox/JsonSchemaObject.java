package cn.lalaframework.quantumbox;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class JsonSchemaObject<T extends JsonSchemaValue> extends JsonSchemaAbstract {
    @Nullable
    @JsonInclude(value = JsonInclude.Include.NON_EMPTY)
    private Map<String, T> properties;

    /**
     * <PERSON>olean or JsonSchemaValue
     */
    @Nullable
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    private Object additionalProperties;

    @Nullable
    @JsonInclude(value = JsonInclude.Include.NON_EMPTY)
    private Map<String, T> patternProperties;

    @Nullable
    @JsonInclude(value = JsonInclude.Include.NON_EMPTY)
    private List<String> required;

    public JsonSchemaObject() {
        super(JsonSchemaType.OBJECT);
    }

    public Object getAdditionalProperties() {
        return additionalProperties;
    }

    public void setAdditionalProperties(Boolean enabled) {
        this.additionalProperties = enabled;
    }

    public void setAdditionalProperties(T additionalProperties) {
        this.additionalProperties = additionalProperties;
    }

    @NonNull
    public Map<String, T> getProperties() {
        if (properties == null) properties = new LinkedHashMap<>();
        return properties;
    }

    @NonNull
    public Map<String, T> getPatternProperties() {
        if (patternProperties == null) patternProperties = new LinkedHashMap<>();
        return patternProperties;
    }

    @NonNull
    public List<String> getRequired() {
        if (required == null) required = new ArrayList<>();
        return required;
    }
}
