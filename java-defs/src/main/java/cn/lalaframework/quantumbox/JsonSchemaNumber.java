package cn.lalaframework.quantumbox;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.Nullable;

@Getter
@Setter
public class JsonSchemaNumber extends JsonSchemaAbstract implements QueryParameter, PrimitiveParameter {
    @Nullable
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    private Double multipleOf;

    @Nullable
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    private Double minimum;

    @Nullable
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    private Double maximum;

    @Nullable
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    private Double exclusiveMinimum;

    @Nullable
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    private Double exclusiveMaximum;

    public JsonSchemaNumber() {
        super(JsonSchemaType.NUMBER);
    }
}
