package cn.lalaframework.quantumbox;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import org.springframework.lang.NonNull;

import java.util.Arrays;

enum JsonSchemaType {
    STRING, NUMBER, BOOLEAN, ARRAY, OBJECT;

    @JsonCreator
    @NonNull
    public static JsonSchemaType fromValue(String value) {
        return Arrays.stream(JsonSchemaType.values())
                .filter(type -> type.name().equalsIgnoreCase(value))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Invalid JSON schema type: " + value));
    }

    @JsonValue
    @NonNull
    public String getValue() {
        return name().toLowerCase();
    }
}
