#!/bin/bash

set -e

if [ -e .git ]; then
  echo "This script cannot be used in develop environment" >&2
  exit 1
fi

yarn install

yarn build

rm -rf /tmp/package
mkdir /tmp/package

cp packages/parser/package.json /tmp/package
cp packages/parser/README.md /tmp/package
mv packages/parser/dist /tmp/package
mv packages/agents/dist/*.json /tmp/package
mv packages/demo/dist /tmp/package/docs-dist
sed -i 's#__VAN_STATIC_BASE_PATH__#&/docs-dist#g' /tmp/package/docs-dist/*.html

cwd="$(pwd)"
mv $cwd /tmp
mv /tmp/package $cwd
