import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import vitePluginVanStatic from "@hll/vite-plugin-van";
import { viteStaticCopy } from "vite-plugin-static-copy";
import fs from 'node:fs'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react(),
    vitePluginVanStatic(),
    viteStaticCopy({
      targets: [
        {
          src: "../../node_modules/monaco-editor/min/*",
          dest: "monaco-editor",
        },
      ],
    }),
  ],
  server: {
    host: 'local.huolala.work',
    port: 5173,
    hmr: {
      protocol: 'wss',
      host: 'local.huolala.work',
      port: 5173
    },
    https: {
      key: fs.readFileSync('/Users/<USER>/local.huolala.work-key.pem'),
      cert: fs.readFileSync('/Users/<USER>/local.huolala.work.pem'),
    },
  }
});
