import React from "react";

export interface UnreadBadgeProps<T = any> {
  /** 消息数组 */
  data: T[] | undefined | null;
  /** 判断消息是否未读的方法，返回 true 表示未读 */
  isUnread: (item: T) => boolean;
  /** 是否显示具体数量，false 时只显示小红点 */
  showCount?: boolean;
  /** 没有未读消息时是否隐藏 */
  hideWhenZero?: boolean;
  /** 自定义 className 或 style */
  className?: string;
  style?: React.CSSProperties;
}

export function UnreadBadge<T = any>({
  data,
  isUnread,
  showCount = true,
  hideWhenZero = true,
  className,
  style,
}: UnreadBadgeProps<T>) {
  const unreadCount = Array.isArray(data) ? data.filter(isUnread).length : 0;

  if (hideWhenZero && unreadCount === 0) return null;

  // 只显示小红点
  if (!showCount) {
    return (
      <span
        className={className}
        style={{
          display: "inline-block",
          width: 8,
          height: 8,
          borderRadius: "50%",
          background: "red",
          verticalAlign: "middle",
          ...style,
        }}
        title={unreadCount > 0 ? `有未读消息` : undefined}
      />
    );
  }

  // 显示数量
  return (
    <span
      className={className}
      style={{
        background: "red",
        color: "white",
        borderRadius: "50%",
        fontSize: "0.8em",
        width: "1.2em",
        height: "1.2em",
        display: "inline-flex",
        alignItems: "center",
        justifyContent: "center",
        ...style,
      }}
      title={unreadCount > 0 ? `有 ${unreadCount} 条消息` : undefined}
    >
      {unreadCount}
    </span>
  );
}

export default UnreadBadge;
