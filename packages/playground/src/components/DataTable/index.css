.DataTable {
  width: 100%;
  position: relative;
}

.DataTable table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875em;
  position: relative;
}

.DataTable thead {
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  position: sticky;
  top: 0;
  z-index: 10;
}

.DataTable th {
  padding: var(--card-padding);
  text-align: left;
  font-weight: 600;
  color: #374151;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background-color: #f3f4f6;
  backdrop-filter: blur(8px);
}

.DataTable td {
  padding: var(--card-padding);
  border-bottom: 1px solid #f3f4f6;
  vertical-align: middle;
}

.DataTable tr {
  transition: background-color 0.2s ease;
  overflow: hidden;
  border-radius: 12px;
}

.DataTable tr:hover {
  background-color: #f3f4f6;
}

.DataTable table tr:last-child td {
  border-bottom: none;
}
