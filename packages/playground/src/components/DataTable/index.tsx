import React, { useMemo } from "react";
import { Tip } from "../Tip";
import "./index.css";

export interface Column<T = any> {
  key: string;
  title: string;
  width?: string;
  align?: "left" | "center" | "right";
  render?: (value: any, record: T, index: number) => React.ReactNode;
}

export interface DataTableProps<T = any> {
  data: T[];
  columns: Column<T>[];
  emptyText?: string;
  className?: string;
  rowKey?: string | ((record: T, index: number) => string);
}

export const DataTable = <T extends Record<string, any>>({
  data,
  columns,
  emptyText = "暂无数据",
  className = "",
  rowKey,
}: DataTableProps<T>) => {
  const getRowKey = (record: T, index: number): string => {
    if (typeof rowKey === "function") {
      return rowKey(record, index);
    }
    if (typeof rowKey === "string") {
      return record[rowKey];
    }
    return index.toString();
  };

  const content = useMemo(() => {
    if (data.length === 0) {
      return <Tip type="empty">{emptyText}</Tip>;
    }
    return <Table columns={columns} data={data} getRowKey={getRowKey} />;
  }, [data, columns, getRowKey, emptyText]);

  return <div className={`DataTable ${className}`}>{content}</div>;
};

const Table = <T extends Record<string, any>>({
  columns,
  data,
  getRowKey,
}: {
  columns: Column<T>[];
  data: T[];
  getRowKey: (record: T, index: number) => string;
}) => {
  return (
    <table>
      <thead>
        <tr>
          {columns.map((column) => (
            <th
              key={column.key}
              style={{
                width: column.width,
                textAlign: column.align || "left",
              }}
            >
              {column.title}
            </th>
          ))}
        </tr>
      </thead>
      <tbody>
        {data.map((record, index) => (
          <tr key={getRowKey(record, index)}>
            {columns.map((column) => (
              <td key={column.key} style={{ textAlign: column.align || "left" }}>
                {column.render ? column.render(record[column.key], record, index) : record[column.key]}
              </td>
            ))}
          </tr>
        ))}
      </tbody>
    </table>
  );
};
