import React, { useState, useRef } from "react";
import ReactDOM from "react-dom";
import "./index.css";

interface TooltipProps extends Omit<React.HTMLAttributes<HTMLSpanElement>, "content"> {
  content: React.ReactNode;
  placement?: "top" | "bottom" | "left" | "right";
}

export const Tooltip: React.FC<TooltipProps> = ({ content, children, placement = "top", ...props }) => {
  const [visible, setVisible] = useState(false);
  const timer = useRef<number | null>(null);
  const triggerRef = useRef<HTMLSpanElement>(null);
  const [coords, setCoords] = useState<{ top: number; left: number }>({ top: 0, left: 0 });

  const show = () => {
    timer.current = window.setTimeout(() => {
      if (triggerRef.current) {
        const rect = triggerRef.current.getBoundingClientRect();
        let top = 0,
          left = 0;
        if (placement === "top") {
          top = rect.top + window.scrollY;
          left = rect.left + rect.width / 2 + window.scrollX;
        } else if (placement === "left") {
          top = rect.top + rect.height / 2 + window.scrollY;
          left = rect.left + window.scrollX;
        } else if (placement === "bottom") {
          top = rect.top + rect.height + window.scrollY;
          left = rect.left + rect.width / 2 + window.scrollX;
        } else if (placement === "right") {
          top = rect.top + rect.height / 2 + window.scrollY;
          left = rect.left + rect.width + window.scrollX;
        }
        setCoords({ top, left });
      }
      setVisible(true);
    }, 200);
  };
  const hide = () => {
    if (timer.current) clearTimeout(timer.current);
    setVisible(false);
  };

  const bubble = visible ? (
    <div
      className={`Tooltip-bubble Tooltip-${placement}`}
      style={
        placement === "top"
          ? { position: "absolute", top: coords.top - 8, left: coords.left, transform: "translate(-50%, -100%)" }
          : placement === "left"
          ? { position: "absolute", top: coords.top, left: coords.left - 8, transform: "translate(-100%, -50%)" }
          : placement === "bottom"
          ? { position: "absolute", top: coords.top + 8, left: coords.left, transform: "translate(-50%, 0)" }
          : placement === "right"
          ? { position: "absolute", top: coords.top, left: coords.left + 8, transform: "translate(0, -50%)" }
          : {}
      }
    >
      {content}
    </div>
  ) : null;

  return (
    <span className="Tooltip-wrapper" ref={triggerRef} onMouseEnter={show} onMouseLeave={hide} {...props}>
      {children}
      {typeof window !== "undefined" && ReactDOM.createPortal(bubble, document.body)}
    </span>
  );
};
