.Dialog-portal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.Dialog-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.4);
  z-index: 1;
  opacity: 1;
  transition: opacity 0.25s;
}
.Dialog-backdrop--open {
  opacity: 1;
}
.Dialog-backdrop--close {
  opacity: 0;
  pointer-events: none;
}

.Dialog {
  position: relative;
  z-index: 2;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 32px rgba(0, 0, 0, 0.18);
  min-width: 320px;
  max-width: 90vw;
  max-height: 90vh;
  padding: 0 0 1.5em 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  opacity: 1;
  transform: scale(1);
  transition: opacity 0.25s, transform 0.25s;
}
.Dialog--open {
  opacity: 1;
  transform: scale(1);
}
.Dialog--close {
  opacity: 0;
  transform: scale(0.96);
  pointer-events: none;
}

.Dialog-title {
  font-size: 1.2em;
  font-weight: bold;
  padding: 1em 2.5em 0.5em 1.5em;
  border-bottom: 1px solid #eee;
}

.Dialog-close {
  position: absolute;
  top: 1em;
  right: 1em;
  background: none;
  border: none;
  font-size: 1.5em;
  color: #888;
  cursor: pointer;
  z-index: 3;
  line-height: 1;
}

.Dialog-content {
  padding: 1.5em;
  overflow: auto;
  flex: 1 1 auto;
}

.Dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 1em 1.5em 0.5em 1.5em;
  border-top: 1px solid #f0f0f0;
  background: #fff;
}

.Dialog-btn {
  min-width: 72px;
  padding: 6px 18px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  background: #fff;
  color: #222;
  font-size: 1em;
  cursor: pointer;
  transition: all 0.2s;
}
.Dialog-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
.Dialog-ok {
  background: #1677ff;
  color: #fff;
  border: 1px solid #1677ff;
}
.Dialog-ok:disabled {
  background: #a0c5fa;
  border-color: #a0c5fa;
}
.Dialog-cancel {
  background: #fff;
  color: #222;
  border: 1px solid #d9d9d9;
}

.Dialog-loading {
  display: inline-block;
  width: 1em;
  height: 1em;
  border: 2px solid #fff;
  border-top: 2px solid #1677ff;
  border-radius: 50%;
  animation: Dialog-spin 0.8s linear infinite;
  vertical-align: middle;
}

@keyframes Dialog-spin {
  100% {
    transform: rotate(360deg);
  }
}
