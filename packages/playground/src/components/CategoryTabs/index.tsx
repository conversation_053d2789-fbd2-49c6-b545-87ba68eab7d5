import { Button } from "../Button";
import "./index.css";

export interface Category {
  id: string;
  name: string;
}

export function CategoryTabs({
  categories,
  activeCategory,
  onChange,
}: {
  categories: Category[];
  activeCategory: string;
  onChange: (id: string) => void;
}) {
  return (
    <div className="CategoryTabs">
      {categories.map((cat) => (
        <Button
          key={cat.id}
          variant={activeCategory === cat.id ? "primary" : "default"}
          onClick={() => onChange(cat.id)}
        >
          {cat.name}
        </Button>
      ))}
    </div>
  );
}
