.Dropdown {
  position: relative;
  display: inline-block;
}

.Dropdown-trigger {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  padding: 0;
  border: 1px solid transparent;
  border-radius: 6px;
  background: transparent;
  color: #8c8c8c;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  font-size: 14px;
  line-height: 1.5715;
}

.Dropdown-trigger:hover:not(.disabled) {
  color: #595959;
  background-color: rgba(0, 0, 0, 0.04);
  border-color: transparent;
}

.Dropdown-trigger.active:not(.disabled) {
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  border-color: transparent;
}

.Dropdown-trigger:focus:not(.disabled) {
  outline: none;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  border-color: transparent;
}

.Dropdown-trigger.disabled {
  color: rgba(0, 0, 0, 0.25);
  cursor: not-allowed;
  background-color: rgba(0, 0, 0, 0.04);
}

.Dropdown-menu {
  position: absolute;
  min-width: 160px;
  margin: 4px 0 0 0;
  padding: 6px 0;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  z-index: 1050;
  overflow: hidden;
  max-height: 300px;
  overflow-y: auto;
}

.Dropdown-menu--bottom {
  top: 100%;
  left: 0;
}

.Dropdown-menu--top {
  bottom: 100%;
  left: 0;
  margin: 0 0 4px 0;
}

.Dropdown-menu--left {
  right: 100%;
  top: 0;
  margin: 0 4px 0 0;
}

.Dropdown-menu--right {
  left: 100%;
  top: 0;
  margin: 0 0 0 4px;
}

/* 添加边界调整样式 */
.Dropdown-menu--bottom.Dropdown-menu--adjust-right {
  left: auto;
  right: 0;
}

.Dropdown-menu--top.Dropdown-menu--adjust-right {
  left: auto;
  right: 0;
}

.Dropdown-menu--left.Dropdown-menu--adjust-bottom {
  top: auto;
  bottom: 0;
}

.Dropdown-menu--right.Dropdown-menu--adjust-bottom {
  top: auto;
  bottom: 0;
}

.Dropdown-menu-item {
  position: relative;
  display: flex;
  align-items: center;
  padding: 8px 16px;
  color: rgba(0, 0, 0, 0.88);
  font-weight: normal;
  font-size: 14px;
  line-height: 1.5714285714285714;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  border-radius: 0;
  clear: both;
  margin: 0;
  white-space: nowrap;
  list-style: none;
}

.Dropdown-menu-item:hover:not(.disabled) {
  background-color: rgba(0, 0, 0, 0.04);
}

.Dropdown-menu-item.danger {
  color: #ff4d4f;
}

.Dropdown-menu-item.danger:hover:not(.disabled) {
  background-color: #fff2f0;
  color: #ff4d4f;
}

.Dropdown-menu-item.disabled {
  color: rgba(0, 0, 0, 0.25);
  cursor: not-allowed;
  background-color: transparent;
}

.Dropdown-menu-item.disabled:hover {
  background-color: transparent;
}

.Dropdown-menu-title {
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.Dropdown-menu-item-icon {
  margin-right: 8px;
  display: flex;
  align-items: center;
}
