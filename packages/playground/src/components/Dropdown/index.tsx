import React, { useState, useEffect, useRef, type ReactNode } from "react";
import "./index.css";
import { LoadingIcon } from "../../icons/LoadingIcon";

export interface DropdownItem {
  key: string;
  label: ReactNode;
  icon?: ReactNode;
  disabled?: boolean;
  danger?: boolean;
  onClick?: () => Promise<void> | void;
}

export interface DropdownProps {
  trigger?: ReactNode;
  items: DropdownItem[];
  placement?: "top" | "bottom" | "left" | "right";
  triggerClassName?: string;
  menuClassName?: string;
  disabled?: boolean;
  onVisibleChange?: (visible: boolean) => void;
}

export const Dropdown: React.FC<DropdownProps> = ({
  trigger,
  items,
  placement = "bottom",
  triggerClassName = "",
  menuClassName = "",
  disabled = false,
  onVisibleChange,
}) => {
  const [visible, setVisible] = useState(false);
  const [actualPlacement, setActualPlacement] = useState(placement);
  const [adjustRight, setAdjustRight] = useState(false);
  const [adjustBottom, setAdjustBottom] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setVisible(false);
      }
    };

    if (visible) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [visible]);

  useEffect(() => {
    onVisibleChange?.(visible);
  }, [visible, onVisibleChange]);

  // 计算最佳位置
  useEffect(() => {
    if (visible && dropdownRef.current && menuRef.current) {
      const triggerRect = dropdownRef.current.getBoundingClientRect();
      const menuRect = menuRef.current.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      let newPlacement = placement;
      let newAdjustRight = false;
      let newAdjustBottom = false;

      // 检查各个方向的可用空间
      const spaceBelow = viewportHeight - triggerRect.bottom;
      const spaceAbove = triggerRect.top;
      const spaceRight = viewportWidth - triggerRect.left;
      const spaceLeft = triggerRect.right;

      // 根据可用空间调整位置
      if (placement === "bottom" && spaceBelow < menuRect.height && spaceAbove > menuRect.height) {
        newPlacement = "top";
      } else if (placement === "top" && spaceAbove < menuRect.height && spaceBelow > menuRect.height) {
        newPlacement = "bottom";
      } else if (placement === "right" && spaceRight < menuRect.width && spaceLeft > menuRect.width) {
        newPlacement = "left";
      } else if (placement === "left" && spaceLeft < menuRect.width && spaceRight > menuRect.width) {
        newPlacement = "right";
      }

      // 检查水平对齐
      if (newPlacement === "bottom" || newPlacement === "top") {
        if (triggerRect.left + menuRect.width > viewportWidth) {
          newAdjustRight = true;
        }
      }

      // 检查垂直对齐
      if (newPlacement === "left" || newPlacement === "right") {
        if (triggerRect.top + menuRect.height > viewportHeight) {
          newAdjustBottom = true;
        }
      }

      setActualPlacement(newPlacement);
      setAdjustRight(newAdjustRight);
      setAdjustBottom(newAdjustBottom);
    }
  }, [visible, placement]);

  const handleTriggerClick = () => {
    if (!disabled) {
      setVisible(!visible);
    }
  };

  const getPlacementClass = () => {
    let baseClass = "";
    switch (actualPlacement) {
      case "top":
        baseClass = "Dropdown-menu--top";
        break;
      case "left":
        baseClass = "Dropdown-menu--left";
        break;
      case "right":
        baseClass = "Dropdown-menu--right";
        break;
      default:
        baseClass = "Dropdown-menu--bottom";
    }

    if (adjustRight && (actualPlacement === "bottom" || actualPlacement === "top")) {
      baseClass += " Dropdown-menu--adjust-right";
    }

    if (adjustBottom && (actualPlacement === "left" || actualPlacement === "right")) {
      baseClass += " Dropdown-menu--adjust-bottom";
    }

    return baseClass;
  };

  return (
    <div className="Dropdown" ref={dropdownRef}>
      <button
        onClick={handleTriggerClick}
        className={`Dropdown-trigger ${triggerClassName} ${visible ? "active" : ""} ${disabled ? "disabled" : ""}`}
        type="button"
        disabled={disabled}
      >
        {trigger}
      </button>
      {visible && (
        <div className={`Dropdown-menu ${getPlacementClass()} ${menuClassName}`} ref={menuRef}>
          {items.map((item, index) => (
            <MenuItem key={index} item={item} setVisible={setVisible} />
          ))}
        </div>
      )}
    </div>
  );
};

const MenuItem = ({ item, setVisible }: { item: DropdownItem; setVisible: (visible: boolean) => void }) => {
  const [loading, setLoading] = useState(false);

  const handleItemClick = async () => {
    if (item.disabled) return;
    if (loading) return;
    try {
      const result = item.onClick?.();
      if (result instanceof Promise) {
        try {
          setLoading(true);
          await result;
        } finally {
          setLoading(false);
        }
      }
    } catch (error) {
      console.error("Dropdown MenuItem onClick error:", error);
    } finally {
      setVisible(false);
    }
  };

  return (
    <div
      key={item.key}
      className={`Dropdown-menu-item ${item.danger ? "danger" : ""} ${item.disabled || loading ? "disabled" : ""}`}
      onClick={handleItemClick}
    >
      {item.icon && <span className="Dropdown-menu-item-icon">{loading ? <LoadingIcon /> : item.icon}</span>}
      <span className="Dropdown-menu-title">{item.label}</span>
    </div>
  );
};
