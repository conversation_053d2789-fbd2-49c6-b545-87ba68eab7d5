import React, { useState, useEffect, useRef, type ReactNode } from "react";
import ReactDOM from "react-dom";
import "./index.css";
import { LoadingIcon } from "../../icons/LoadingIcon";

export interface DropdownItem {
  key: string;
  label: ReactNode;
  icon?: ReactNode;
  disabled?: boolean;
  danger?: boolean;
  onClick?: () => Promise<void> | void;
}

export interface DropdownProps {
  trigger?: ReactNode;
  items: DropdownItem[];
  placement?: "top" | "bottom" | "left" | "right" | "topLeft" | "topRight" | "bottomLeft" | "bottomRight";
  triggerClassName?: string;
  menuClassName?: string;
  disabled?: boolean;
  onVisibleChange?: (visible: boolean) => void;
}

export const Dropdown: React.FC<DropdownProps> = ({
  trigger,
  items,
  placement = "bottom",
  triggerClassName = "",
  menuClassName = "",
  disabled = false,
  onVisibleChange,
}) => {
  const [visible, setVisible] = useState(false);
  const [actualPlacement, setActualPlacement] = useState(placement);
  const [adjustRight, setAdjustRight] = useState(false);
  const [adjustBottom, setAdjustBottom] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const menuRef = useRef<HTMLDivElement>(null);
  const [coords, setCoords] = useState<{ top: number; left: number }>({ top: 0, left: 0 });
  const [ready, setReady] = useState(false);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setVisible(false);
      }
    };

    if (visible) {
      document.addEventListener("click", handleClickOutside);
    }

    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, [visible]);

  useEffect(() => {
    onVisibleChange?.(visible);
  }, [visible, onVisibleChange]);

  // 计算最佳位置并设置坐标
  useEffect(() => {
    if (visible && dropdownRef.current && menuRef.current) {
      const triggerRect = dropdownRef.current.getBoundingClientRect();
      const menuRect = menuRef.current.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      let newPlacement = placement;
      let newAdjustRight = false;
      let newAdjustBottom = false;
      let top = 0,
        left = 0;

      // 检查各个方向的可用空间
      const spaceBelow = viewportHeight - triggerRect.bottom;
      const spaceAbove = triggerRect.top;
      const spaceRight = viewportWidth - triggerRect.left;
      const spaceLeft = triggerRect.right;

      // 根据可用空间调整位置
      if (placement === "bottom" && spaceBelow < menuRect.height && spaceAbove > menuRect.height) {
        newPlacement = "top";
      } else if (placement === "top" && spaceAbove < menuRect.height && spaceBelow > menuRect.height) {
        newPlacement = "bottom";
      } else if (placement === "right" && spaceRight < menuRect.width && spaceLeft > menuRect.width) {
        newPlacement = "left";
      } else if (placement === "left" && spaceLeft < menuRect.width && spaceRight > menuRect.width) {
        newPlacement = "right";
      }

      // 检查水平对齐
      if (newPlacement === "bottom" || newPlacement === "top") {
        if (triggerRect.left + menuRect.width > viewportWidth) {
          newAdjustRight = true;
        }
      }

      // 检查垂直对齐
      if (newPlacement === "left" || newPlacement === "right") {
        if (triggerRect.top + menuRect.height > viewportHeight) {
          newAdjustBottom = true;
        }
      }

      // 计算浮层坐标
      if (newPlacement === "top") {
        top = triggerRect.top + window.scrollY;
        left = triggerRect.left + triggerRect.width / 2 + window.scrollX;
      } else if (newPlacement === "bottom") {
        top = triggerRect.top + triggerRect.height + window.scrollY;
        left = triggerRect.left + triggerRect.width / 2 + window.scrollX;
      } else if (newPlacement === "left") {
        top = triggerRect.top + triggerRect.height / 2 + window.scrollY;
        left = triggerRect.left + window.scrollX;
      } else if (newPlacement === "right") {
        top = triggerRect.top + triggerRect.height / 2 + window.scrollY;
        left = triggerRect.left + triggerRect.width + window.scrollX;
      } else if (newPlacement === "topLeft") {
        top = triggerRect.top + window.scrollY;
        left = triggerRect.left + window.scrollX;
      } else if (newPlacement === "topRight") {
        top = triggerRect.top + window.scrollY;
        left = triggerRect.left + triggerRect.width + window.scrollX;
      } else if (newPlacement === "bottomLeft") {
        top = triggerRect.top + triggerRect.height + window.scrollY;
        left = triggerRect.left + window.scrollX;
      } else if (newPlacement === "bottomRight") {
        top = triggerRect.top + triggerRect.height + window.scrollY;
        left = triggerRect.left + triggerRect.width + window.scrollX;
      }

      setActualPlacement(newPlacement);
      setAdjustRight(newAdjustRight);
      setAdjustBottom(newAdjustBottom);
      setCoords({ top, left });
      setReady(true);
    } else {
      setReady(false);
    }
  }, [visible, placement]);

  // 监听窗口 resize/scroll，visible 时重新计算位置
  useEffect(() => {
    if (!visible) return;
    const updatePosition = () => {
      if (dropdownRef.current && menuRef.current) {
        const triggerRect = dropdownRef.current.getBoundingClientRect();
        const menuRect = menuRef.current.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        let newPlacement = placement;
        let newAdjustRight = false;
        let newAdjustBottom = false;
        let top = 0,
          left = 0;

        const spaceBelow = viewportHeight - triggerRect.bottom;
        const spaceAbove = triggerRect.top;
        const spaceRight = viewportWidth - triggerRect.left;
        const spaceLeft = triggerRect.right;

        if (placement === "bottom" && spaceBelow < menuRect.height && spaceAbove > menuRect.height) {
          newPlacement = "top";
        } else if (placement === "top" && spaceAbove < menuRect.height && spaceBelow > menuRect.height) {
          newPlacement = "bottom";
        } else if (placement === "right" && spaceRight < menuRect.width && spaceLeft > menuRect.width) {
          newPlacement = "left";
        } else if (placement === "left" && spaceLeft < menuRect.width && spaceRight > menuRect.width) {
          newPlacement = "right";
        }

        if (newPlacement === "bottom" || newPlacement === "top") {
          if (triggerRect.left + menuRect.width > viewportWidth) {
            newAdjustRight = true;
          }
        }
        if (newPlacement === "left" || newPlacement === "right") {
          if (triggerRect.top + menuRect.height > viewportHeight) {
            newAdjustBottom = true;
          }
        }

        if (newPlacement === "top") {
          top = triggerRect.top + window.scrollY;
          left = triggerRect.left + triggerRect.width / 2 + window.scrollX;
        } else if (newPlacement === "bottom") {
          top = triggerRect.top + triggerRect.height + window.scrollY;
          left = triggerRect.left + triggerRect.width / 2 + window.scrollX;
        } else if (newPlacement === "left") {
          top = triggerRect.top + triggerRect.height / 2 + window.scrollY;
          left = triggerRect.left + window.scrollX;
        } else if (newPlacement === "right") {
          top = triggerRect.top + triggerRect.height / 2 + window.scrollY;
          left = triggerRect.left + triggerRect.width + window.scrollX;
        } else if (newPlacement === "topLeft") {
          top = triggerRect.top + window.scrollY;
          left = triggerRect.left + window.scrollX;
        } else if (newPlacement === "topRight") {
          top = triggerRect.top + window.scrollY;
          left = triggerRect.left + triggerRect.width + window.scrollX;
        } else if (newPlacement === "bottomLeft") {
          top = triggerRect.top + triggerRect.height + window.scrollY;
          left = triggerRect.left + window.scrollX;
        } else if (newPlacement === "bottomRight") {
          top = triggerRect.top + triggerRect.height + window.scrollY;
          left = triggerRect.left + triggerRect.width + window.scrollX;
        }

        setActualPlacement(newPlacement);
        setAdjustRight(newAdjustRight);
        setAdjustBottom(newAdjustBottom);
        setCoords({ top, left });
        setReady(true);
      }
    };
    window.addEventListener("resize", updatePosition);
    window.addEventListener("scroll", updatePosition, true);
    return () => {
      window.removeEventListener("resize", updatePosition);
      window.removeEventListener("scroll", updatePosition, true);
    };
  }, [visible, placement]);

  const handleTriggerClick = () => {
    if (!disabled) {
      setVisible(!visible);
    }
  };

  const getPlacementClass = () => {
    let baseClass = "";
    switch (actualPlacement) {
      case "top":
        baseClass = "Dropdown-menu--top";
        break;
      case "left":
        baseClass = "Dropdown-menu--left";
        break;
      case "right":
        baseClass = "Dropdown-menu--right";
        break;
      case "topLeft":
        baseClass = "Dropdown-menu--top-left";
        break;
      case "topRight":
        baseClass = "Dropdown-menu--top-right";
        break;
      case "bottomLeft":
        baseClass = "Dropdown-menu--bottom-left";
        break;
      case "bottomRight":
        baseClass = "Dropdown-menu--bottom-right";
        break;
      default:
        baseClass = "Dropdown-menu--bottom";
    }

    if (adjustRight && (actualPlacement === "bottom" || actualPlacement === "top")) {
      baseClass += " Dropdown-menu--adjust-right";
    }

    if (adjustBottom && (actualPlacement === "left" || actualPlacement === "right")) {
      baseClass += " Dropdown-menu--adjust-bottom";
    }

    return baseClass;
  };

  // Portal 渲染 Dropdown-menu
  const menu = visible
    ? ReactDOM.createPortal(
        <div
          className={`Dropdown-menu ${getPlacementClass()} ${menuClassName}`}
          ref={menuRef}
          style={(() => {
            let style: React.CSSProperties = { zIndex: 9999 };
            if (actualPlacement === "top") {
              style = {
                position: "absolute",
                top: coords.top,
                left: coords.left,
                transform: "translate(-50%, -100%)",
                zIndex: 9999,
              };
            } else if (actualPlacement === "bottom") {
              style = {
                position: "absolute",
                top: coords.top,
                left: coords.left,
                transform: "translate(-50%, 0)",
                zIndex: 9999,
              };
            } else if (actualPlacement === "left") {
              style = {
                position: "absolute",
                top: coords.top,
                left: coords.left,
                transform: "translate(-100%, -50%)",
                zIndex: 9999,
              };
            } else if (actualPlacement === "right") {
              style = {
                position: "absolute",
                top: coords.top,
                left: coords.left,
                transform: "translate(0, -50%)",
                zIndex: 9999,
              };
            } else if (actualPlacement === "topLeft") {
              style = {
                position: "absolute",
                top: coords.top,
                left: coords.left,
                transform: "translate(0, -100%)",
                zIndex: 9999,
              };
            } else if (actualPlacement === "topRight") {
              style = {
                position: "absolute",
                top: coords.top,
                left: coords.left,
                transform: "translate(-100%, -100%)",
                zIndex: 9999,
              };
            } else if (actualPlacement === "bottomLeft") {
              style = {
                position: "absolute",
                top: coords.top,
                left: coords.left,
                transform: "translate(0, 0)",
                zIndex: 9999,
              };
            } else if (actualPlacement === "bottomRight") {
              style = {
                position: "absolute",
                top: coords.top,
                left: coords.left,
                transform: "translate(-100%, 0)",
                zIndex: 9999,
              };
            } else {
              style = { position: "absolute", top: coords.top, left: coords.left, zIndex: 9999 };
            }
            if (!ready) {
              style.visibility = "hidden";
            }
            return style;
          })()}
        >
          {ready && items.map((item, index) => <MenuItem key={index} item={item} setVisible={setVisible} />)}
        </div>,
        document.body
      )
    : null;

  return (
    <div className="Dropdown" ref={dropdownRef} style={{ display: "inline-block" }}>
      <button
        onClick={handleTriggerClick}
        className={`Dropdown-trigger ${triggerClassName} ${visible ? "active" : ""} ${disabled ? "disabled" : ""}`}
        type="button"
        disabled={disabled}
      >
        {trigger}
      </button>
      {menu}
    </div>
  );
};

const MenuItem = ({ item, setVisible }: { item: DropdownItem; setVisible: (visible: boolean) => void }) => {
  const [loading, setLoading] = useState(false);

  const handleItemClick = async () => {
    if (item.disabled) return;
    if (loading) return;
    try {
      const result = item.onClick?.();
      if (result instanceof Promise) {
        try {
          setLoading(true);
          await result;
        } finally {
          setLoading(false);
        }
      }
    } catch (error) {
      console.error("Dropdown MenuItem onClick error:", error);
    } finally {
      setVisible(false);
    }
  };

  return (
    <div
      key={item.key}
      className={`Dropdown-menu-item ${item.danger ? "danger" : ""} ${item.disabled || loading ? "disabled" : ""}`}
      onClick={handleItemClick}
    >
      {item.icon && <span className="Dropdown-menu-item-icon">{loading ? <LoadingIcon /> : item.icon}</span>}
      <span className="Dropdown-menu-title">{item.label}</span>
    </div>
  );
};
