import React, { useState, type ReactNode } from "react";
import { Popover } from "../Popover";
import "./index.css";
import { LoadingIcon } from "../../icons/LoadingIcon";

export interface DropdownItem {
  key: string;
  label: ReactNode;
  icon?: ReactNode;
  disabled?: boolean;
  danger?: boolean;
  active?: boolean; // 新增
  onClick?: () => Promise<void> | void;
}

export interface DropdownProps {
  trigger?: ReactNode;
  items: DropdownItem[];
  placement?: "top" | "bottom" | "left" | "right" | "topLeft" | "topRight" | "bottomLeft" | "bottomRight";
  triggerClassName?: string;
  menuClassName?: string;
  disabled?: boolean;
  onVisibleChange?: (visible: boolean) => void;
}

export const Dropdown: React.FC<DropdownProps> = ({
  trigger,
  items,
  placement = "bottom",
  menuClassName = "",
  disabled = false,
  onVisibleChange,
}) => {
  // 菜单内容
  const menu = (
    <div className={`Dropdown-menu ${menuClassName}`}>
      {items.map((item) => (
        <MenuItem key={item.key} item={item} />
      ))}
    </div>
  );

  return (
    <Popover
      trigger={trigger}
      content={menu}
      placement={placement}
      triggerClassName="Dropdown-trigger"
      popoverClassName="Dropdown-popover"
      disabled={disabled}
      onVisibleChange={onVisibleChange}
    />
  );
};

const MenuItem = ({ item }: { item: DropdownItem }) => {
  const [loading, setLoading] = useState(false);

  const handleItemClick = async () => {
    if (item.disabled) return;
    if (loading) return;
    try {
      const result = item.onClick?.();
      if (result instanceof Promise) {
        try {
          setLoading(true);
          await result;
        } finally {
          setLoading(false);
        }
      }
    } catch (error) {
      console.error("Dropdown MenuItem onClick error:", error);
    }
  };

  return (
    <div
      key={item.key}
      className={`Dropdown-menu-item ${item.danger ? "danger" : ""} ${item.disabled || loading ? "disabled" : ""} ${
        item.active ? "active" : ""
      }`}
      onClick={handleItemClick}
    >
      {item.icon && <span className="Dropdown-menu-item-icon">{loading ? <LoadingIcon /> : item.icon}</span>}
      <span className="Dropdown-menu-title">{item.label}</span>
    </div>
  );
};
