import { showError, showSuccess } from "../Notification";
import { Button } from "../Button";
import { StarIcon } from "../../icons/StarIcon";
import { useNavigate } from "react-router-dom";
import { getAgentStore, favoriteAgentsStore, useTagStore, type TagResult } from "@hll/quantum-store";
import { FeishuAvatar } from "../FeishuAvatar";
import type { FinalQuantumAgentConfig } from "@hll/quantum-store/dist/AgentStore";

import "./index.css";

const handleToggleFavorite = async (agentId: string, isFavorited: boolean) => {
  try {
    if (isFavorited == null) return;
    if (isFavorited) {
      await favoriteAgentsStore.remove(agentId);
      showSuccess("已取消关注");
    } else {
      await favoriteAgentsStore.add(agentId);
      showSuccess("已添加到关注");
    }
  } catch (error) {
    showError(error, isFavorited ? "取消关注失败" : "关注失败");
  }
};

export const AgentCard = ({ agent }: { agent: TagResult<FinalQuantumAgentConfig> & { id: string } }) => {
  const navigate = useNavigate();
  const { data: myAgentsData, loading: myAgentsLoading, error: myAgentsError } = useTagStore(favoriteAgentsStore);

  const { data, id, owner, timestamp } = agent;
  const { name, description, icon } = data || {};
  const isFavorited = myAgentsLoading == false && !myAgentsError ? (myAgentsData || []).includes(id) : null;

  return (
    <div
      className="AgentCard"
      onClick={() => {
        navigate(`/chat?agent=${getAgentStore(id).base}`);
      }}
    >
      <div style={{ display: "flex", gap: "var(--gap)" }}>
        {icon && <img src={icon} alt={name || "Agent icon"} className="agent-icon" />}
        <div style={{ flex: 1 }}>
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <h3>{name || id}</h3>
            {isFavorited != null && (
              <Button
                onClick={(e) => {
                  e.stopPropagation();
                  handleToggleFavorite(id, isFavorited);
                }}
                icon={<StarIcon style={{ fill: isFavorited ? "#f59e0b" : "#ccc" }} />}
                variant="icon"
                title={isFavorited ? "取消关注" : "添加到关注"}
              />
            )}
          </div>
          <p>{description || "暂无描述"}</p>
        </div>
      </div>
      <hr />
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          fontSize: "0.875em",
          color: "#9ca3af",
        }}
      >
        <span>{timestamp ? new Date(timestamp).toLocaleString("zh-CN") : "Unknown"}</span>
        <FeishuAvatar uniqId={owner || "Unknown"} />
      </div>
    </div>
  );
};
