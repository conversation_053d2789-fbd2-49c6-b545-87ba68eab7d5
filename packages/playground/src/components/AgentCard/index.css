.AgentCard {
  background: #ffffff;
  border: 1px solid #f3f4f6;
  border-radius: 12px;
  padding: var(--card-padding);
  transition: all 0.2s ease;
  cursor: pointer;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.AgentCard:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #e5e7eb;
}

.AgentCard .agent-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  flex-shrink: 0;
}

.AgentCard h3 {
  font-size: 1.1em;
  font-weight: 600;
  color: #111827;
  margin: 0 0 8px 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.AgentCard p {
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.5;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.AgentCard hr {
  margin: var(--gap) 0;
  border: none;
  height: 1px;
  background-color: #f3f4f6;
  margin-top: 12px;
}
