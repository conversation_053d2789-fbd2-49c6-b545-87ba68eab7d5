import React, { useState, useRef, useEffect, useCallback } from "react";
import ReactDOM from "react-dom";
import { useEventListener } from "@hll/use-event-listener";
import "./index.css";

type PopoverPosition = { top: number; left: number; transform: string };

export interface PopoverProps {
  trigger: React.ReactNode;
  content: React.ReactNode;
  placement?: "top" | "bottom" | "left" | "right" | "topLeft" | "topRight" | "bottomLeft" | "bottomRight";
  triggerClassName?: string;
  popoverClassName?: string;
  disabled?: boolean;
  onVisibleChange?: (visible: boolean) => void;
  triggerType?: "hover" | "click";
}

type Anchor = [number, number]; // [x, y]，范围 0~1

const placementMap: Record<NonNullable<PopoverProps["placement"]>, { anchor: Anchor; align: Anchor }> = {
  top: { anchor: [0.5, 0], align: [0.5, 1] },
  bottom: { anchor: [0.5, 1], align: [0.5, 0] },
  left: { anchor: [0, 0.5], align: [1, 0.5] },
  right: { anchor: [1, 0.5], align: [0, 0.5] },
  topLeft: { anchor: [0, 0], align: [0, 1] },
  topRight: { anchor: [1, 0], align: [1, 1] },
  bottomLeft: { anchor: [0, 1], align: [0, 0] },
  bottomRight: { anchor: [1, 1], align: [1, 0] },
};

const calcPopoverPosition = (
  triggerRect: DOMRect,
  popoverRect: DOMRect,
  placement: NonNullable<PopoverProps["placement"]>
): PopoverPosition => {
  const { anchor, align } = placementMap[placement];
  const left = triggerRect.left + triggerRect.width * anchor[0] - popoverRect.width * align[0] + window.scrollX;
  const top = triggerRect.top + triggerRect.height * anchor[1] - popoverRect.height * align[1] + window.scrollY;
  return { top, left, transform: "none" };
};

export const Popover: React.FC<PopoverProps> = ({
  trigger,
  content,
  placement = "bottom",
  triggerClassName = "",
  popoverClassName = "",
  disabled = false,
  onVisibleChange,
  triggerType = "hover",
}) => {
  const [visible, setVisible] = useState(false);
  const [popoverPosition, setPopoverPosition] = useState<PopoverPosition>({ top: 0, left: 0, transform: "" });
  const [ready, setReady] = useState(false);
  const triggerRef = useRef<HTMLDivElement>(null);
  const popoverRef = useRef<HTMLDivElement>(null);

  // 计算 Popover 位置
  useEffect(() => {
    if (!visible) return;
    setReady(false);
    let rafId = requestAnimationFrame(() => {
      const triggerRect = triggerRef.current?.getBoundingClientRect();
      if (!triggerRect) return;
      const popoverRect = popoverRef.current?.getBoundingClientRect();
      if (!popoverRect) return;
      setPopoverPosition(calcPopoverPosition(triggerRect, popoverRect, placement));
      setReady(true);
    });
    return () => cancelAnimationFrame(rafId);
  }, [visible, placement, content]);

  // 点击外部关闭
  const handleClickOutside = useCallback(
    (event: MouseEvent) => {
      if (!visible) return;
      if (
        triggerRef.current &&
        !triggerRef.current.contains(event.target as Node) &&
        popoverRef.current &&
        !popoverRef.current.contains(event.target as Node)
      ) {
        setVisible(false);
        onVisibleChange?.(false);
      }
    },
    [onVisibleChange, visible]
  );

  useEventListener("mousedown", handleClickOutside);

  // 事件处理
  const handleTriggerClick = useCallback(() => {
    if (disabled) return;
    setVisible((v) => {
      onVisibleChange?.(!v);
      return !v;
    });
  }, [disabled, onVisibleChange]);

  const handleMouseEnter = useCallback(() => {
    if (disabled) return;
    setVisible(true);
    onVisibleChange?.(true);
  }, [disabled, onVisibleChange]);

  const handleMouseLeave = useCallback(() => {
    if (disabled) return;
    setVisible(false);
    onVisibleChange?.(false);
  }, [disabled, onVisibleChange]);

  return (
    <>
      <div
        ref={triggerRef}
        className={triggerClassName}
        onClick={triggerType === "click" ? handleTriggerClick : undefined}
        onMouseEnter={triggerType === "hover" ? handleMouseEnter : undefined}
        onMouseLeave={triggerType === "hover" ? handleMouseLeave : undefined}
      >
        {trigger}
      </div>
      {visible &&
        ReactDOM.createPortal(
          <div
            ref={popoverRef}
            className={`Popover-root${popoverClassName ? " " + popoverClassName : ""}`}
            style={{
              position: "absolute",
              top: popoverPosition.top,
              left: popoverPosition.left,
              transform: popoverPosition.transform,
              zIndex: 9999,
              visibility: ready ? "visible" : "hidden",
            }}
            onMouseEnter={triggerType === "hover" ? handleMouseEnter : undefined}
            onMouseLeave={triggerType === "hover" ? handleMouseLeave : undefined}
          >
            {content}
          </div>,
          document.body
        )}
    </>
  );
};
