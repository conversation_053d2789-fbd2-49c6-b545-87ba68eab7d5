import React, { useState } from "react";
import { ChevronIcon } from "../../icons/ChevronIcon";
import "./index.css";

export interface CollapsibleSectionProps {
  title: string;
  children: React.ReactNode;
  defaultExpanded?: boolean;
}

export const CollapsibleSection: React.FC<CollapsibleSectionProps> = ({ title, children, defaultExpanded = true }) => {
  const [expanded, setExpanded] = useState(defaultExpanded);

  const handleToggle = () => {
    setExpanded(!expanded);
  };

  return (
    <div className="CollapsibleSection">
      <h2 className="CollapsibleSection-title" onClick={handleToggle}>
        {title}
        <span className={`CollapsibleSection-icon ${expanded ? "expanded" : "collapsed"}`}>
          <ChevronIcon />
        </span>
      </h2>
      <div className={`CollapsibleSection-content ${expanded ? "expanded" : "collapsed"}`}>{children}</div>
    </div>
  );
};
