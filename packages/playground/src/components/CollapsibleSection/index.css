.CollapsibleSection {
  display: flex;
  flex-direction: column;
}

.CollapsibleSection-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 0.9rem;
  font-weight: 600;
  color: #374151;
  cursor: pointer;
  user-select: none;
  transition: color 0.2s ease;
}

.CollapsibleSection-title:hover {
  color: #1f2937;
}

.CollapsibleSection-icon {
  color: #6b7280;
  transition: transform 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.CollapsibleSection-icon.collapsed {
  transform: rotate(-90deg);
}

.CollapsibleSection-icon.expanded {
  transform: rotate(0deg);
}

.CollapsibleSection-content {
  overflow: hidden;
}

.CollapsibleSection-content.collapsed {
  max-height: 0;
}

.CollapsibleSection-content.expanded {
  max-height: auto;
}
