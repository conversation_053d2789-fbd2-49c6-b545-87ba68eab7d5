import { FeishuAvatar } from "../FeishuAvatar";
import { Tip } from "../Tip";
import { useUserInfo } from "../../services/user";
import { getErrorMessage } from "../../utils/getErrorMessage";
import { Popover } from "../Popover";

import "./index.css";
import { InboxBadge } from "./InboxBadge";
import { InboxList } from "./InboxList";
import { UserInfo } from "./UserInfo";

export const UserBar = () => {
  const { data, loading, error } = useUserInfo();
  if (loading) {
    return <Tip type="loading" />;
  }
  if (error) {
    return <Tip type="error">{getErrorMessage(error)}</Tip>;
  }
  const user = data?.uniqId;
  if (!user) return null;

  // 弹出内容自定义：用户信息+分割线+未读消息
  const popoverContent = (
    <div style={{ display: "flex", flexDirection: "column", gap: "calc(var(--gap) * 1)" }}>
      <UserInfo user={data} />
      <hr style={{ border: "none", height: 1, background: "#eee", width: "100%", margin: 0 }} />
      <InboxList />
    </div>
  );

  return (
    <div className="UserBar">
      <Popover
        trigger={
          <InboxBadge>
            <FeishuAvatar uniqId={user} noLink />
          </InboxBadge>
        }
        content={popoverContent}
        placement="bottomRight"
        popoverClassName="UserBar-popover"
        triggerClassName="UserBar-trigger"
      />
    </div>
  );
};
