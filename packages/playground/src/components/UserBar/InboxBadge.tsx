import { getInboxStore, useTagStore } from "@hll/quantum-store";
import { getErrorMessage } from "../../utils/getErrorMessage";
import { Tip } from "../Tip";
import UnreadBadge from "../UnreadBadge";

export const InboxBadge = ({ children }: { children: React.ReactNode }) => {
  const inbox = getInboxStore("-");
  const { data, loading, error } = useTagStore(inbox);
  if (loading) {
    return <Tip type="loading" />;
  }
  if (error) {
    return <Tip type="error">{getErrorMessage(error)}</Tip>;
  }
  return (
    <div style={{ position: "relative", display: "flex", alignItems: "center" }}>
      <UnreadBadge data={data} isUnread={() => true} style={{ position: "absolute", top: "-.8em", right: "-.8em" }} />
      {children}
    </div>
  );
};
