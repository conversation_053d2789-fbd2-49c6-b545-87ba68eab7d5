export const UserInfo = ({ user }: { user: any }) => {
  const avatarUrl = user.avatar72 || user.avatar?.avatar72;
  return (
    <div style={{ display: "flex", alignItems: "center", gap: "0.75em" }}>
      {avatarUrl ? (
        <img
          src={avatarUrl}
          alt={user.name || user.uniqId}
          style={{
            width: "32px",
            height: "32px",
            borderRadius: "100%",
            objectFit: "cover",
          }}
        />
      ) : null}
      <div style={{ display: "flex", flexDirection: "column", minWidth: 0 }}>
        <div
          style={{
            fontWeight: 500,
            textOverflow: "ellipsis",
            overflow: "hidden",
            whiteSpace: "nowrap",
          }}
        >
          {user.name || user.uniqId}
        </div>
        <div
          style={{
            color: "#888",
            fontSize: "0.85em",
            textOverflow: "ellipsis",
            overflow: "hidden",
            whiteSpace: "nowrap",
          }}
        >
          {user.uniqId}
        </div>
      </div>
    </div>
  );
};
