import { getInboxStore, useTagStore } from "@hll/quantum-store";
import { getErrorMessage } from "../../utils/getErrorMessage";
import { Tip } from "../Tip";
import { InboxListItem } from "./InboxListItem";

export const InboxList = () => {
  const { data, loading, error } = useTagStore(getInboxStore("-"));
  if (loading && !data) return <Tip type="loading" />;
  if (error) return <Tip type="error">{getErrorMessage(error)}</Tip>;
  if (!data || data.length === 0) return <Tip type="empty">暂无未读消息</Tip>;
  return (
    <ul className={`InboxList ${loading ? "loading" : ""}`}>
      {data.map((item) => (
        <InboxListItem key={item.commitHash} im={item} />
      ))}
    </ul>
  );
};
