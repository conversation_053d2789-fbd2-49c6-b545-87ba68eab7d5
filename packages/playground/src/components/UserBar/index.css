/* UserBar 主体 */
.UserBar {
  display: flex;
  align-items: center;
}

.UserBar-popover {
}

.InboxList {
  width: 400px;
  max-height: 180px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  margin: 0;
  padding: 0;
  list-style: none;
  gap: var(--gap);
}

.InboxList li {
  display: flex;
  flex-direction: column;
  gap: calc(var(--gap) * 0.5);
  padding: 6px 0;
  font-size: 0.875rem;
  color: #888;
  border-bottom: 1px solid #f5f5f5;
  &:last-child {
    border-bottom: none;
  }
}
