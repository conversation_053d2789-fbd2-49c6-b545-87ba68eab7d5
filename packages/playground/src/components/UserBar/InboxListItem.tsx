import React from "react";
import { FeishuAvatar } from "../FeishuAvatar";
import { Tip } from "../Tip";
import { Button } from "../Button";
import { DeleteIcon } from "../../icons/DeleteIcon";
import { SuccessIcon } from "../../icons/SuccessIcon";
import { CloseIcon } from "../../icons/CloseIcon";
import {
  getAgentStore,
  getInboxStore,
  getSiteStore,
  InboxStore,
  useAgentConfig,
  useTagStore,
} from "@hll/quantum-store";
import { useRequest } from "../../utils/useRequest";
import { getErrorMessage } from "../../utils/getErrorMessage";
import type { InboxMessage, QuantumInboxMessage } from "@hll/quantum-store";
import { showError } from "../Notification";
import { Link } from "react-router-dom";

interface IProps<T extends QuantumInboxMessage> {
  im: InboxMessage;
  msg: T;
}

const PlainTextMessage = ({ msg }: IProps<Extract<QuantumInboxMessage, { type: "plainText" }>>) => {
  return <div>{msg.message.length > 60 ? msg.message.slice(0, 60) + "…" : msg.message}</div>;
};

const PlainTextActions = ({ im }: IProps<Extract<QuantumInboxMessage, { type: "plainText" }>>) => {
  return <DeleteButton im={im} />;
};

const RequestAgentPermissionMessage = ({
  msg,
}: IProps<Extract<QuantumInboxMessage, { type: "requestAgentPermission" }>>) => {
  const { data: agent, loading } = useAgentConfig(msg.agentId);
  return (
    <div>
      申请智能体{" "}
      <Link to={`/agent/${msg.agentId}`} target="_blank" rel="noopener noreferrer">
        {msg.agentId}
        {agent ? ` (${agent.name})` : loading ? <Tip type="loading" style={{ display: "inline-block" }} /> : null}
      </Link>{" "}
      的编辑权限
    </div>
  );
};

const RequestAgentPermissionActions = ({
  im,
  msg,
}: IProps<Extract<QuantumInboxMessage, { type: "requestAgentPermission" }>>) => {
  return (
    <>
      <Button
        variant="icon"
        title="同意"
        aria-label="同意"
        onClick={async () => {
          try {
            const { agentId } = msg;
            const { user } = im;
            if (!user) throw new Error("user is required");
            const agentStore = getAgentStore(agentId);
            await agentStore.modifyAuthorizedUsers((authorizedUsers) => {
              authorizedUsers.add(user);
            });
            await getInboxStore(user).send({
              type: "plainText",
              message: `同意智能体 ${msg.agentId} 编辑权限的申请`,
            });
            const store = getInboxStore("-");
            await store.remove([im.commitHash]);
            store.getWithoutCache();
          } catch (e) {
            showError(e, "操作失败");
          }
        }}
        icon={<SuccessIcon width={16} height={16} />}
      />
      <Button
        variant="icon"
        title="拒绝"
        onClick={async () => {
          try {
            const store = getInboxStore("-");
            await getInboxStore(im.user).send({
              type: "plainText",
              message: `拒绝智能体 ${msg.agentId} 编辑权限的申请`,
            });
            await store.remove([im.commitHash]);
            store.getWithoutCache();
          } catch (e) {
            showError(e, "操作失败");
          }
        }}
        icon={<CloseIcon width={16} height={16} />}
      />
      <DeleteButton im={im} />
    </>
  );
};

const RequestSitePermissionMessage = ({
  msg,
}: IProps<Extract<QuantumInboxMessage, { type: "requestSitePermission" }>>) => {
  const { data: site, loading } = useTagStore(getSiteStore(msg.siteId));
  return (
    <div>
      申请网址导航{" "}
      <Link to={`/sites/${msg.siteId}`} target="_blank" rel="noopener noreferrer">
        {msg.siteId}
        {site ? ` (${site.title})` : loading ? <Tip type="loading" style={{ display: "inline-block" }} /> : null}
      </Link>{" "}
      的编辑权限
    </div>
  );
};

const RequestSitePermissionActions = ({
  im,
  msg,
}: IProps<Extract<QuantumInboxMessage, { type: "requestSitePermission" }>>) => {
  return (
    <>
      <Button
        variant="icon"
        title="同意"
        aria-label="同意"
        onClick={async () => {
          try {
            const { siteId } = msg;
            const { user } = im;
            if (!user) throw new Error("user is required");
            const siteStore = getSiteStore(siteId);
            await siteStore.modifyAuthorizedUsers((authorizedUsers) => {
              authorizedUsers.add(user);
            });
            await getInboxStore(user).send({
              type: "plainText",
              message: `同意网址导航 ${msg.siteId} 编辑权限的申请`,
            });
            const store = getInboxStore("-");
            await store.remove([im.commitHash]);
            store.getWithoutCache();
          } catch (e) {
            showError(e, "操作失败");
          }
        }}
        icon={<SuccessIcon width={16} height={16} />}
      />
      <Button
        variant="icon"
        title="拒绝"
        onClick={async () => {
          try {
            const store = getInboxStore("-");
            await getInboxStore(im.user).send({
              type: "plainText",
              message: `拒绝网址导航 ${msg.siteId} 编辑权限的申请`,
            });
            await store.remove([im.commitHash]);
            store.getWithoutCache();
          } catch (e) {
            showError(e, "操作失败");
          }
        }}
        icon={<CloseIcon width={16} height={16} />}
      />
      <DeleteButton im={im} />
    </>
  );
};

const DeleteButton = ({ im }: { im: InboxMessage }) => {
  return (
    <Button
      variant="icon"
      title="删除"
      aria-label="删除"
      onClick={async () => {
        try {
          const store = getInboxStore("-");
          await store.remove([im.commitHash]);
          store.getWithoutCache();
        } catch (e) {
          showError(e, "删除失败");
        }
      }}
      icon={<DeleteIcon width={16} height={16} />}
    />
  );
};

export const InboxListItem = ({ im }: { im: InboxMessage }) => {
  const { commitHash, user, timestamp } = im;
  const { data, loading, error } = useRequest(() => InboxStore.getInboxCommit(commitHash), {
    refreshDeps: [commitHash],
  });

  let content: React.ReactNode = null;
  let actions: React.ReactNode = null;
  if (loading) {
    content = <Tip type="loading" />;
  } else if (error) {
    content = <Tip type="error">{getErrorMessage(error)}</Tip>;
  } else if (!data) {
    content = <Tip type="empty">无内容</Tip>;
  } else {
    switch (data.type) {
      case "plainText":
        content = <PlainTextMessage im={im} msg={data} />;
        actions = <PlainTextActions im={im} msg={data} />;
        break;
      case "requestAgentPermission":
        content = <RequestAgentPermissionMessage im={im} msg={data} />;
        actions = <RequestAgentPermissionActions im={im} msg={data} />;
        break;
      case "requestSitePermission":
        content = <RequestSitePermissionMessage im={im} msg={data} />;
        actions = <RequestSitePermissionActions im={im} msg={data} />;
        break;
      default:
        console.error("Unknown message type", data);
        return null;
    }
  }

  return (
    <li>
      <div style={{ display: "flex", gap: "var(--gap)" }}>
        <time>{timestamp ? new Date(timestamp).toLocaleString("zh-CN") : "--"}</time>
        <FeishuAvatar uniqId={user} />
        <div style={{ flex: 1 }}></div>
        <div style={{ display: "flex", gap: 4 }}>{actions}</div>
      </div>
      <div style={{ flex: 1 }}>{content}</div>
    </li>
  );
};
