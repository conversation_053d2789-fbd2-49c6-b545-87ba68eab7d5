import { useState, useMemo } from "react";

export interface Category {
  id: string;
  name: string;
}

export interface AgentFilterManagerOptions<T> {
  categories: Category[];
  defaultCategory: string;
  getAgentData: (agentId: string) => T | undefined;
  filterFunction?: (agent: T | undefined, searchQuery: string, activeCategory: string) => boolean;
}

export function useAgentFilterManager<T = any>(
  agentIds: string[],
  options: AgentFilterManagerOptions<T>
) {
  const {
    categories,
    defaultCategory,
    getAgentData,
    filterFunction = (agent, searchQuery, activeCategory) => {
      if (!agent) return false;
      const { name = "", description = "", tags = [] } = agent as any;
      const matchSearch =
        !searchQuery ||
        name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        description.toLowerCase().includes(searchQuery.toLowerCase());
      const matchCategory =
        activeCategory === "all" || (tags && tags.includes(activeCategory));
      return matchSearch && matchCategory;
    },
  } = options;

  const [activeCategory, setActiveCategory] = useState(defaultCategory);
  const [searchQuery, setSearchQuery] = useState("");

  const filteredAgentIds = useMemo(() => {
    return agentIds.filter((agentId) => {
      const agent = getAgentData(agentId);
      return filterFunction(agent, searchQuery, activeCategory);
    });
  }, [agentIds, getAgentData, filterFunction, searchQuery, activeCategory]);

  return {
    categories,
    activeCategory,
    setActiveCategory,
    searchQuery,
    setSearchQuery,
    filteredAgentIds,
  };
} 