.FeishuAvatar {
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.5ch;
  border-radius: 10px;
  padding-right: 0.5ch;
  transition: background 0.2s ease;
  white-space: nowrap;
}

.FeishuAvatar:hover {
  background-color: #e0e9ff;
}

.FeishuAvatar > img {
  width: 1.5em;
  height: 1.5em;
  border-radius: 100%;
}

.FeishuAvatar > span {
  font-size: 0.9em;
}

.FeishuAvatar.no-link {
  cursor: default;
}

.FeishuAvatar.no-link:hover {
  background-color: transparent;
}

@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.FeishuAvatar--loading {
  pointer-events: none;
}

.FeishuAvatar__skeleton-avatar,
.FeishuAvatar__skeleton-text,
.FeishuAvatar__skeleton-id {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

.FeishuAvatar__skeleton-avatar {
  width: 1.5em;
  height: 1.5em;
  border-radius: 100%;
  flex-shrink: 0;
}

.FeishuAvatar__skeleton-text {
  height: 0.9em;
  width: 4em;
  border-radius: 4px;
}

.FeishuAvatar__skeleton-id {
  height: 0.9em;
  width: 3em;
  border-radius: 4px;
}
