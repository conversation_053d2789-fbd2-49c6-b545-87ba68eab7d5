import { useEffect, useState } from "react";
import { getFeishuUserInfoById, type OtherUserInfo } from "../../services/user";
import "./index.css";

interface FeishuAvatarProps {
  uniqId: string;
  showId?: boolean;
}

export const FeishuAvatar = ({ uniqId, showId = false }: FeishuAvatarProps) => {
  const [userInfo, setUserInfo] = useState<OtherUserInfo | null>(null);
  useEffect(() => {
    let cancelled = false;
    getFeishuUserInfoById([uniqId]).then((info) => {
      if (!cancelled) {
        setUserInfo(info[uniqId] || null);
      }
    });
    return () => {
      cancelled = true;
    };
  }, [uniqId]);

  const src = userInfo?.avatar72 || userInfo?.avatar?.avatar72;
  const name = userInfo?.name.replace(/\(.*\)$/, "") || "Unknown";
  const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();
    if (userInfo?.openId) {
      window.open(`lark://applink.feishu.cn/client/chat/open?openId=${userInfo.openId}`);
    }
  };

  return (
    <div onClick={handleClick} className="FeishuAvatar">
      {src && <img src={src} alt="avatar" />}
      <span>{name}</span>
      {showId && <span>({uniqId})</span>}
    </div>
  );
};
