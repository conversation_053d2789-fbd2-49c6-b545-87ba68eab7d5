import { useEffect, useState } from "react";
import { getFeishuUserInfoById, type OtherUserInfo } from "../../services/user";
import "./index.css";

interface FeishuAvatarProps {
  uniqId?: string;
  user?: OtherUserInfo;
  noLink?: boolean;
  showId?: boolean;
}

export const FeishuAvatar = ({ uniqId, user, noLink, showId = false }: FeishuAvatarProps) => {
  const [userInfo, setUserInfo] = useState<OtherUserInfo | null>(user || null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!uniqId) return;
    let cancelled = false;
    setIsLoading(true);
    getFeishuUserInfoById([uniqId]).then((info) => {
      if (!cancelled) {
        setUserInfo(info[uniqId] || null);
        setIsLoading(false);
      }
    });
    return () => {
      cancelled = true;
    };
  }, [uniqId]);

  const src = userInfo?.avatar72 || userInfo?.avatar?.avatar72;
  const name = userInfo?.name.replace(/\(.*\)$/, "") || uniqId;
  const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();
    if (userInfo?.openId) {
      window.open(`lark://applink.feishu.cn/client/chat/open?openId=${userInfo.openId}`);
    }
  };

  if (isLoading) {
    return (
      <div className="FeishuAvatar FeishuAvatar--loading">
        <div className="FeishuAvatar__skeleton-avatar"></div>
        <div className="FeishuAvatar__skeleton-text"></div>
        {showId && <div className="FeishuAvatar__skeleton-id"></div>}
      </div>
    );
  }

  return (
    <div onClick={noLink ? undefined : handleClick} className={`FeishuAvatar ${noLink ? "no-link" : ""}`}>
      {src && <img src={src} alt="avatar" />}
      <span>{name}</span>
      {showId && <span>({uniqId})</span>}
    </div>
  );
};
