import { useSlot } from "@hll/use-slot";
import { UserBar } from "../UserBar";

import "./index.css";

export const MainHeader = () => {
  const title = useSlot("MainHeader-title");
  const left = useSlot("MainHeader-left");
  const right = useSlot("MainHeader-right");

  return (
    <div className="MainHeader">
      <h2>{title}</h2>
      {left}
      <div style={{ flex: 1 }} />
      {right}
      {right && <div style={{ width: "1px", height: "100%", background: "#e2e8f0" }} />}
      <UserBar />
    </div>
  );
};
