.ManagerMenu .AsideContent > div {
  display: flex;
  flex-direction: column;
  gap: calc(var(--gap) * 1);
}

.ManagerMenu .AsideContent h2 {
  font-size: 1.1em;
  font-weight: 600;
  color: #1e293b;
  padding: 0;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.ManagerMenu .AsideContent ul {
  list-style: none;
  display: flex;
  flex-direction: column;
  gap: var(--gap);
  padding: 0;
  margin: 0;
  padding-left: 1em;
}

.ManagerMenu .AsideContent li a {
  display: block;
  width: 100%;
  border-radius: 6px;
  padding: 6px var(--gap);
  margin: 0px calc(var(--gap) * -1);
  transition: all 0.2s ease;
}

.ManagerMenu .AsideContent li a:hover {
  color: inherit;
  background-color: #eee;
}

.ManagerMenu .AsideContent li.active a {
  color: #fff;
  font-weight: 600;
  background-color: var(--primary);
}

.ManagerMenu .AsideContent li.active a:hover {
  color: #fff;
}
