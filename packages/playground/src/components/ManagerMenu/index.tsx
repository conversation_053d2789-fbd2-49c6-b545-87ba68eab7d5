import { Link, useLocation } from "react-router-dom";
import { Aside, AsideContent, AsideHeader } from "../Aside";
import {
  myAgentsStore,
  favoriteAgentsStore,
  publicAgentsStore,
  type ListTagStore,
  publicSitesStore,
  mySitesStore,
} from "@hll/quantum-store";
import { AgentIcon } from "../../icons/AgentIcon";
import { WebsiteIcon } from "../../icons/WebsiteIcon";
import { UserIcon } from "../../icons/UserIcon";
import { StarIcon } from "../../icons/StarIcon";
import { GlobeIcon } from "../../icons/GlobeIcon";
import { EditIcon } from "../../icons/EditIcon";
import { ListIcon } from "../../icons/ListIcon";

import "./index.css";

interface NavigationItem {
  path: string;
  icon: React.ReactNode;
  label: string;
  store?: ListTagStore<string>;
}

interface NavigationSection {
  title: React.ReactNode;
  icon: React.ReactNode;
  items: NavigationItem[];
}

export const navigationSections = {
  agents: {
    title: "智能体管理",
    icon: <AgentIcon />,
    items: [
      { path: "/agents/mine", label: "我的智能体", store: myAgentsStore, icon: <UserIcon /> },
      { path: "/agents/favorite", label: "关注的智能体", store: favoriteAgentsStore, icon: <StarIcon /> },
      { path: "/agents/public", label: "公开的智能体", store: publicAgentsStore, icon: <GlobeIcon /> },
    ],
  },
  sites: {
    title: "网址导航管理",
    icon: <WebsiteIcon />,
    items: [
      { path: "/sites/mine", label: "我创建的", store: mySitesStore, icon: <EditIcon /> },
      { path: "/sites/public", label: "全部网址导航", store: publicSitesStore, icon: <ListIcon /> },
    ],
  },
} satisfies Record<string, NavigationSection>;

const NavigationSection = ({ section, pathname }: { section: NavigationSection; pathname: string }) => (
  <div>
    <h2>
      <div style={{ display: "flex", alignItems: "center", gap: "0.5ch" }}>
        {section.icon}
        {section.title}
      </div>
    </h2>
    <ul>
      {section.items.map((item) => (
        <li key={item.path} className={pathname === item.path ? "active" : ""}>
          <Link to={item.path}>
            <div style={{ display: "flex", alignItems: "center", gap: "0.5ch" }}>
              {item.icon}
              {item.label}
            </div>
          </Link>
        </li>
      ))}
    </ul>
  </div>
);

export const ManagerMenu = () => {
  const { pathname } = useLocation();

  return (
    <div className="ManagerMenu">
      <Aside>
        <AsideHeader>Manager</AsideHeader>
        <AsideContent>
          {Object.entries(navigationSections).map(([key, section]) => (
            <NavigationSection key={key} section={section} pathname={pathname} />
          ))}
        </AsideContent>
      </Aside>
    </div>
  );
};
