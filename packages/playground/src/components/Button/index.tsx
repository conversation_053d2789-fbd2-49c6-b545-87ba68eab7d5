import React, { useState } from "react";
import { LoadingIcon } from "../../icons/LoadingIcon";
import "./index.css";

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "default" | "primary" | "danger" | "link" | "icon";
  size?: "small" | "medium" | "large";
  loading?: boolean;
  icon?: React.ReactNode;
  children?: React.ReactNode;
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void | Promise<unknown>;
}

export const Button: React.FC<ButtonProps> = ({
  variant = "default",
  size = "medium",
  loading: externalLoading,
  disabled,
  icon,
  children,
  className = "",
  onClick,
  ...props
}) => {
  const [internalLoading, setInternalLoading] = useState(false);

  const handleClick = async (event: React.MouseEvent<HTMLButtonElement>) => {
    if (!onClick) return;
    try {
      const result = onClick(event);
      if (result instanceof Promise) {
        setInternalLoading(true);
        try {
          await result;
        } finally {
          setInternalLoading(false);
        }
      }
    } catch (error) {
      console.error("Button onClick error:", error);
    }
  };

  const baseClass = "Button";
  const variantClass = variant !== "default" ? `Button--${variant}` : "";
  const sizeClass = size !== "medium" ? `Button--${size}` : "";
  const combinedClass = [baseClass, variantClass, sizeClass, className].filter(Boolean).join(" ");
  const loading = externalLoading || internalLoading;
  return (
    <button className={combinedClass} disabled={disabled || loading} onClick={handleClick} {...props}>
      {icon && <span className="Button-icon">{loading ? <LoadingIcon /> : icon}</span>}
      {children}
    </button>
  );
};
