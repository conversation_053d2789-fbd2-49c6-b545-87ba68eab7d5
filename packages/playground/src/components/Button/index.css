.But<PERSON> {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background-color: #ffffff;
  color: #374151;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5ch;
}

/* Size variants */
.Button--small {
  padding: 4px 12px;
  font-size: 12px;
  border-radius: 4px;
}

.Button--large {
  padding: 12px 20px;
  font-size: 16px;
  border-radius: 8px;
}

.Button:hover:not(:disabled) {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

.Button:active:not(:disabled) {
  background-color: #f3f4f6;
  transform: translateY(1px);
}

.Button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.Button--primary {
  background-color: var(--primary);
  border-color: var(--primary);
  color: #ffffff;
}

.Button--primary:hover:not(:disabled) {
  background-color: var(--primary-hover);
  border-color: var(--primary-hover);
}

.Button--primary:active:not(:disabled) {
  background-color: var(--primary-active);
}

.Button--danger {
  background-color: #ef4444;
  border-color: #ef4444;
  color: #ffffff;
}

.Button--danger:hover:not(:disabled) {
  background-color: #dc2626;
  border-color: #dc2626;
}

.Button--danger:active:not(:disabled) {
  background-color: #b91c1c;
}

.Button--link {
  padding: 0;
  border: none;
  background-color: transparent;
  color: #3b82f6;
  text-decoration: none;
  font-weight: normal;
}

.Button--link:hover:not(:disabled) {
  background-color: transparent;
  color: #2563eb;
  text-decoration: none;
}

.Button--link:active:not(:disabled) {
  background-color: transparent;
  transform: none;
}

.Button--icon {
  padding: 2px;
  border: none;
  background-color: transparent;
  color: #6b7280;
  border-radius: 6px;
  min-width: auto;
  min-height: auto;
  font-size: 1.1em;
}

.Button--icon:hover:not(:disabled) {
  background-color: #f3f4f6;
  color: #374151;
}

.Button--icon:active:not(:disabled) {
  background-color: #e5e7eb;
  transform: none;
}

.Button-icon {
  display: flex;
  align-items: center;
}
