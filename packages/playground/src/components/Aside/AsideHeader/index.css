.AsideHeader {
  font-weight: 600;
  color: #1e293b;
  height: var(--header-height);
  line-height: var(--header-height);
  flex-shrink: 0;
  padding: 0 calc(var(--gap) * 2);
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: var(--gap);
  position: sticky;
}

.AsideHeader .logo-link {
  display: flex;
  color: inherit;
  transition: all 0.5s ease;
}

.AsideHeader .logo-link:hover {
  color: #2563eb;
}

.AsideHeader .Dropdown {
  margin-right: calc(var(--gap) * -1);
}
