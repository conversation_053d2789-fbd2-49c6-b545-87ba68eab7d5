.AsideHeader {
  font-weight: 600;
  color: #1e293b;
  height: var(--header-height);
  line-height: var(--header-height);
  flex-shrink: 0;
  padding: 0 calc(var(--gap) * 2);
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: var(--gap);
  position: sticky;
}

.AsideHeader a {
  display: flex;
  color: inherit;
  transition: filter 0.2s ease;
}

.AsideHeader a:hover {
  filter: brightness(1.2);
}

.AsideHeader a.header-link {
  color: inherit;
  text-decoration: none;
  font-size: 0.8em;
  font-weight: normal;
  margin-left: auto;
  transition: all 0.2s ease;
}

.AsideHeader a.header-link:hover {
  color: #2563eb;
}
