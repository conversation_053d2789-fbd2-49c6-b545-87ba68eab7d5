import { Link, useLocation, useNavigate } from "react-router-dom";
import { LogoIcon } from "../../../icons/LogoIcon";
import { Dropdown } from "../../Dropdown";
import { MarketIcon } from "../../../icons/MarketIcon";
import { MenuIcon } from "../../../icons/MenuIcon";
import { useSlot } from "@hll/use-slot";
import { UserIcon } from "../../../icons/UserIcon";

import "./index.css";

const menuItems = [
  { label: "智能体市场", icon: <MarketIcon />, key: "market", href: "/chat" },
  { label: "我的智能体", icon: <UserIcon />, key: "manager", href: "/agents/mine" },
];

export const AsideHeader = () => {
  const navigate = useNavigate();
  const title = useSlot("AsideHeader-title");
  const { pathname, search } = useLocation();
  return (
    <div className="AsideHeader">
      <Link to="/" className="logo-link">
        <LogoIcon width={36} height={36} style={{ marginLeft: -6 }} />
      </Link>
      <div>{title}</div>
      <div style={{ flex: 1 }}></div>
      <Dropdown
        trigger={<MenuIcon />}
        items={menuItems.map((item) => {
          return {
            label: item.label,
            icon: item.icon,
            key: item.key,
            active: pathname + search === item.href,
            onClick: () => navigate(item.href),
          };
        })}
      />
    </div>
  );
};
