.Notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  max-width: 400px;
  min-width: 300px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-left: 4px solid;
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.3s ease;
}

.Notification.visible {
  transform: translateX(0);
  opacity: 1;
}

.Notification.hidden {
  transform: translateX(100%);
  opacity: 0;
}

.Notification.error {
  border-left-color: #ef4444;
}

.Notification.warning {
  border-left-color: #f59e0b;
}

.Notification.info {
  border-left-color: #3b82f6;
}

.Notification.success {
  border-left-color: #22c55e;
}

.Notification-content {
  display: flex;
  align-items: center;
  padding: var(--card-padding);
  gap: var(--gap);
}

.Notification-icon {
  font-size: 16px;
  flex-shrink: 0;
  margin-top: 2px;
}

.Notification-message {
  flex: 1;
  font-size: 14px;
  line-height: 1.4;
  color: #374151;
  word-break: break-word;
}

.Notification-close {
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  font-size: 16px;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.Notification-close:hover {
  background: #f3f4f6;
  color: #6b7280;
}
