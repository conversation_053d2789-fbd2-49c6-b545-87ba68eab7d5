import { useState, useEffect, type ReactNode } from "react";
import { SuccessIcon } from "../../icons/SuccessIcon";
import { ErrorIcon } from "../../icons/ErrorIcon";
import { WarningIcon } from "../../icons/WarningIcon";
import { InfoIcon } from "../../icons/InfoIcon";

export const Notification = ({ message, type = "info", duration = 5000, onClose }: NotificationProps) => {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
      setTimeout(onClose, 300);
    }, duration);

    return () => clearTimeout(timer);
  }, [duration, onClose]);

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(onClose, 300);
  };

  const renderIcon = () => {
    switch (type) {
      case "success":
        return <SuccessIcon />;
      case "error":
        return <ErrorIcon />;
      case "warning":
        return <WarningIcon />;
      case "info":
      default:
        return <InfoIcon />;
    }
  };

  return (
    <div className={`Notification ${type} ${isVisible ? "visible" : "hidden"}`}>
      <div className="Notification-content">
        <div className="Notification-icon">{renderIcon()}</div>
        <div className="Notification-message">{message}</div>
        <button className="Notification-close" onClick={handleClose}>
          ✕
        </button>
      </div>
    </div>
  );
};

export interface NotificationProps {
  message: ReactNode;
  type?: "success" | "error" | "warning" | "info";
  duration?: number;
  onClose: () => void;
}
