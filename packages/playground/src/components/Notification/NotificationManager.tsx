/* eslint-disable react-refresh/only-export-components */

import { useState, useCallback, useEffect, type ReactNode } from "react";
import { Notification } from "./Notification";

export interface NotificationItem {
  id: string;
  message: ReactNode;
  type: "error" | "warning" | "info" | "success";
  duration?: number;
}

// 全局状态
let globalNotifications: NotificationItem[] = [];

// 自定义事件类型
const NOTIFICATION_UPDATE_EVENT = "notification-update";

// 全局通知管理器
class GlobalNotificationManager {
  private static instance: GlobalNotificationManager;

  static getInstance(): GlobalNotificationManager {
    if (!GlobalNotificationManager.instance) {
      GlobalNotificationManager.instance = new GlobalNotificationManager();
    }
    return GlobalNotificationManager.instance;
  }

  showMessage(message: ReactNode, type: "error" | "warning" | "info" | "success" = "error", duration = 5000) {
    const id = Math.random().toString(36).substring(2, 15);
    const notification: NotificationItem = { id, message, type, duration };

    globalNotifications = [...globalNotifications, notification];
    this.emitUpdate();
  }

  removeMessage(id: string) {
    globalNotifications = globalNotifications.filter((notification) => notification.id !== id);
    this.emitUpdate();
  }

  getNotifications(): NotificationItem[] {
    return globalNotifications;
  }

  private emitUpdate() {
    window.dispatchEvent(
      new CustomEvent(NOTIFICATION_UPDATE_EVENT, {
        detail: globalNotifications,
      })
    );
  }
}

// 全局实例
export const notificationManager = GlobalNotificationManager.getInstance();

// React Hook for components that need to render notifications
export const useNotificationManager = () => {
  const [notifications, setNotifications] = useState<NotificationItem[]>([]);

  useEffect(() => {
    // 初始化时设置当前通知
    setNotifications(notificationManager.getNotifications());

    // 监听通知变化事件
    const handleNotificationUpdate = (event: CustomEvent<NotificationItem[]>) => {
      setNotifications(event.detail);
    };

    window.addEventListener(NOTIFICATION_UPDATE_EVENT, handleNotificationUpdate as EventListener);

    return () => {
      window.removeEventListener(NOTIFICATION_UPDATE_EVENT, handleNotificationUpdate as EventListener);
    };
  }, []);

  const showMessage = useCallback(
    (message: ReactNode, type: "error" | "warning" | "info" | "success" = "error", duration = 5000) => {
      notificationManager.showMessage(message, type, duration);
    },
    []
  );

  const removeMessage = useCallback((id: string) => {
    notificationManager.removeMessage(id);
  }, []);

  return { showMessage, removeMessage, notifications };
};

// 全局通知容器组件
export const NotificationContainer = () => {
  const { notifications, removeMessage } = useNotificationManager();

  return (
    <div className="error-notifications-container">
      {notifications.map((notification) => (
        <Notification
          key={notification.id}
          message={notification.message}
          type={notification.type}
          duration={notification.duration}
          onClose={() => removeMessage(notification.id)}
        />
      ))}
    </div>
  );
};
