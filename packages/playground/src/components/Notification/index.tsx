/* eslint-disable react-refresh/only-export-components */

import { useCallback, type ReactNode } from "react";
import { notificationManager } from "./NotificationManager";
import { getErrorMessage } from "../../utils/getErrorMessage";

import "./index.css";

// 全局暴露的方法
export const showError = (error: unknown, context?: string, type: "error" | "warning" | "info" = "error") => {
  let message: string;
  message = getErrorMessage(error);

  if (context) {
    message = `${context}: ${message}`;
  }

  notificationManager.showMessage(message, type);

  // 同时在控制台输出错误信息
  console.error(context || "Global Error:", error);
};

export const showSuccess = (message: ReactNode) => {
  notificationManager.showMessage(message, "success");
};

export const showMessage = (message: ReactNode, type: "error" | "warning" | "info" | "success" = "error") => {
  notificationManager.showMessage(message, type);
};

/**
 * @deprecated You can import `showError`, `showSuccess`, `showMessage` from `./NotificationManager` instead.
 */
export const useNotification = () => {
  const showErrorHook = useCallback(
    (error: unknown, context?: string, type: "error" | "warning" | "info" = "error") => {
      showError(error, context, type);
    },
    []
  );

  const showSuccessHook = useCallback((message: ReactNode) => {
    showSuccess(message);
  }, []);

  const showMessageHook = useCallback(
    (message: ReactNode, type: "error" | "warning" | "info" | "success" = "error") => {
      showMessage(message, type);
    },
    []
  );

  return { showError: showErrorHook, showSuccess: showSuccessHook, showMessage: showMessageHook };
};

// 导出通知容器组件
export { NotificationContainer } from "./NotificationManager";
