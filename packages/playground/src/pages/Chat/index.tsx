import { Link, useLocation } from "react-router-dom";
import { ChatMain } from "./ChatMain";
import { Aside, AsideHeader, AsideContent } from "../../components/Aside";
import { CollapsibleSection } from "../../components/CollapsibleSection";
import { ChatHistory } from "./ChatHistory";
import { ChatProvider } from "./useChatInfo";
import { Market } from "./Market";
import { MyFavorateAgents } from "./MyFavorateAgents";
import { Tooltip } from "../../components/Tooltip";
import { MarketIcon } from "../../icons/MarketIcon";

export const AgentMarket = () => {
  return <Market />;
};

import "./index.css";

export const Chat = () => {
  const { search } = useLocation();
  return (
    <div className="Chat">
      <Aside>
        <AsideHeader>
          <span>Chat</span>
          <Link to="/chat" className="header-link">
            <Tooltip content="智能体市场" placement="bottom">
              <MarketIcon />
            </Tooltip>
          </Link>
        </AsideHeader>
        <AsideContent>
          <CollapsibleSection title="我关注的智能体">
            <MyFavorateAgents />
          </CollapsibleSection>
          <CollapsibleSection title="对话历史">
            <ChatHistory />
          </CollapsibleSection>
        </AsideContent>
      </Aside>
      <div className="Chat-main">
        {search ? (
          <ChatProvider>
            <ChatMain />
          </ChatProvider>
        ) : (
          <Market />
        )}
      </div>
    </div>
  );
};
