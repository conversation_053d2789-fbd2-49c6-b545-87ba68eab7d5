import { useLocation } from "react-router-dom";
import { ChatMain } from "./ChatMain";
import { Aside, AsideHeader, AsideContent } from "../../components/Aside";
import { CollapsibleSection } from "../../components/CollapsibleSection";
import { ChatHistory } from "./ChatHistory";
import { ChatProvider } from "./useChatInfo";
import { Market } from "./Market";
import { MyFavorateAgents } from "./MyFavorateAgents";
import { useSlot } from "@hll/use-slot";

import "./index.css";

export const Chat = () => {
  const { search } = useLocation();
  useSlot("AsideHeader-title", () => "Chat", []);
  return (
    <div className="Chat">
      <Aside>
        <AsideHeader />
        <AsideContent>
          <CollapsibleSection title="我关注的智能体">
            <MyFavorateAgents />
          </CollapsibleSection>
          <CollapsibleSection title="对话历史">
            <ChatHistory />
          </CollapsibleSection>
        </AsideContent>
      </Aside>
      <div className="Chat-main">
        {search ? (
          <ChatProvider>
            <ChatMain />
          </ChatProvider>
        ) : (
          <Market />
        )}
      </div>
    </div>
  );
};
