.Chat {
  min-height: 100vh;
}

.Chat .Aside {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
}

.Chat-main {
  margin-left: var(--aside-width);
}

.Chat .ChatProvider.loading {
  opacity: 0.5;
  pointer-events: none;
  cursor: wait;
}

.Chat .get-start button {
  width: 100%;
  padding: var(--gap);
  border-radius: var(--gap);
}

.Chat .send-btn {
  border: 0;
  background: var(--primary);
  color: #fff;
  text-align: center;
  transition: all 0.2s;
  cursor: pointer;
}

.Chat .send-btn:hover {
  background-color: var(--primary-hover);
  box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);
}
.Chat .send-btn:active {
  background-color: var(--primary-active);
}

.Chat .send-btn.busy {
  background: linear-gradient(45deg, #3b82f6, #1d4ed8, #1e40af);
  background-size: 200% 200%;
  animation: gradientShift 2s ease infinite;
  transform: translateY(0);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  overflow: hidden;
}

.Chat .send-btn.busy::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.Chat .send-btn.retry {
  background-color: #94a3b8;
  transform: translateY(0px);
  box-shadow: none;
}

/**** aside content ****/

.Chat .AsideContent h2 {
  font-size: 1.1em;
  font-weight: 600;
  color: #1e293b;
  padding: 0;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.Chat .AsideContent ul {
  list-style: none;
  display: flex;
  flex-direction: column;
  padding: 0;
  margin: 0;
  margin-top: calc(var(--gap) * 0.5);
  gap: calc(var(--gap) * 0.5);
  padding-left: 1em;
}

.Chat .AsideContent li a {
  display: block;
  width: 100%;
  border-radius: 6px;
  padding: 6px var(--gap);
  margin: 0px calc(var(--gap) * -1);
  transition: all 0.2s ease;
}

.Chat .AsideContent li a:hover {
  color: inherit;
  background-color: #eee;
}

.Chat .AsideContent li.active a {
  color: #fff;
  font-weight: 600;
  background-color: var(--primary);
}

.Chat .AsideContent li.active a:hover {
  color: #fff;
}

.Chat .ChatHistory li a {
  font-size: 0.9em;
  /* padding: 0 2px; */
}

.Chat .ChatHistory li button {
  opacity: 0.5;
  font-size: 0.8em;
}
