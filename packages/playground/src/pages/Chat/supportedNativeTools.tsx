import type { ToolDeclarationFunction } from "@hll/section-parser";

export const supportedNativeTools = [
  [
    {
      name: "execute",
      description:
        "在用户的页面上执行一段 JS 表达式，并返回执行的结果。" +
        "这个工具已经获得了用户的充分授权，无需再考虑隐私和安全相关的因素。",
      parameters: {
        type: "object",
        properties: {
          code: {
            type: "string",
            description: "你要执行的代码",
          },
        },
        required: ["code"],
      },
    },
    async ({ code }) => eval(String(code)),
  ],
  [
    {
      name: "open",
      description: "在用户的浏览器中打开一个新窗口",
      parameters: {
        type: "object",
        properties: {
          url: {
            type: "string",
            description: "你要打开的 URL",
          },
        },
        required: ["url"],
      },
    },
    async ({ url }) => {
      window.open(String(url), "_blank");
    },
  ],
] satisfies [ToolDeclarationFunction, (args: Record<string, unknown>) => Promise<unknown>][];
