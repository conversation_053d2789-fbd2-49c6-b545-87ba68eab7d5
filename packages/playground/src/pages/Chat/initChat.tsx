import { WukongChat, WorkflowChat, TradeChat, HaitunChat } from "@hll/section-parser";
import { supportedNativeTools } from "./supportedNativeTools";
import { getHllIdentifier, getUserInfo } from "../../services/user";

export const initChat = async (aiType: string, chatId: string, options: object) => {
  const { uniqId: account } = await getUserInfo();
  const chat = createChat(aiType, account, options, chatId);
  if ("registerEnvironmentVariables" in chat) {
    chat.registerEnvironmentVariables({
      _hll_identifier: getHllIdentifier(),
      account,
    });
  }
  if ("registerNativeTool" in chat) {
    for (const [declaration, implement] of supportedNativeTools) {
      chat.registerNativeTool(declaration, implement);
    }
  }
  return chat;
};

const createChat = (aiType: string, account: string, options: object, chatId: string) => {
  switch (aiType) {
    case "wukong": {
      return new WukongChat(`//wgw.huolala.work/proxy/api/open/agent/v1`, {
        agent_id: "",
        user_id: account,
        business_alias: "van_build_helper",
        project_alias: "van_build_helper",
        ...options,
      });
    }
    case "workflow": {
      return new WorkflowChat(`//wgw.huolala.work/proxy/api/open/workflow/run_stream`, {
        workflow_id: "2b15960d57ba11f0840016c3cc6c1293",
        user_id: account,
        enable_heartbeat: false,
        chat_history: [],
        ...options,
      });
    }
    case "trade":
      return new TradeChat(`//trade-ai.huolala.work/api/agent/trade/streamChat`, {
        chatId,
        platform: "bailian",
        assistant: "order_assistant",
        account,
        model: "qwen-max",
        ...options,
      });
    case "haitun": {
      const model = "Qwen3-14B";
      return new HaitunChat(`//wgw-pre.huolala.work/proxy/gateway/${model.toLowerCase()}/v1/chat/completions`, {
        model,
        temperature: 0,
        tool_choice: "auto",
        parallel_tool_calls: true,
        ...options,
      });
    }
    default:
      throw new Error(`Unknown AI type: ${aiType}`);
  }
};
