import { useState, useEffect, useMemo } from "react";
import { Link, useLocation } from "react-router-dom";
import { Button } from "../../components/Button";
import { type ChatHistoryRecord, chatHistoryStore } from "../../services/chatHistoryStore";
import { debounce } from "../../utils/debounce";
import { useEventListener } from "@hll/use-event-listener";
import { CloseIcon } from "../../icons/CloseIcon";
import { u2s } from "u2x";

export const ChatHistory = () => {
  const [history, setHistory] = useState<ChatHistoryRecord[]>([]);

  const location = useLocation();
  const params = new URLSearchParams(location.search);
  const currentChatId = params.get("chatId");
  const update = useMemo(
    () =>
      debounce(() => {
        chatHistoryStore.listAllActiveChats().then((history) => {
          history.reverse();
          setHistory(history);
        });
      }),
    []
  );

  useEventListener("put", update, { target: chatHistoryStore.eventBus });
  useEventListener("delete", update, { target: chatHistoryStore.eventBus });

  useEffect(() => {
    update();
  }, [update]);

  if (history.length === 0) return null;
  return (
    <>
      <ul className="ChatHistory">
        {history.map((item) => {
          const config = item.agentConfig || {};
          const icon = u2s(config.icon);
          const name = u2s(config.name);
          return (
            <li key={item.chatId} className={`ChatHistory-item ${item.chatId === currentChatId ? " active" : ""}`}>
              <Link
                to={`/chat?chatId=${item.chatId}`}
                style={{ display: "flex", alignItems: "center", gap: "var(--gap)" }}
              >
                {icon ? <img src={icon} alt={name} style={{ width: 16, height: 16, marginRight: 4 }} /> : null}
                <time>{formatTime(item.timestamp)}</time>
                <div style={{ flex: 1 }} />
                <Button
                  onClick={(e) => {
                    e.preventDefault();
                    chatHistoryStore.delete(item.chatId);
                  }}
                  variant="icon"
                  style={{ color: "inherit" }}
                  icon={<CloseIcon />}
                />
              </Link>
            </li>
          );
        })}
      </ul>
    </>
  );
};

const formatTime = (time: Date) => {
  const hour = time.getHours().toString().padStart(2, "0");
  const minute = time.getMinutes().toString().padStart(2, "0");
  const month = (time.getMonth() + 1).toString().padStart(2, "0");
  const day = time.getDate().toString().padStart(2, "0");
  return `${month}/${day} ${hour}:${minute}`;
};
