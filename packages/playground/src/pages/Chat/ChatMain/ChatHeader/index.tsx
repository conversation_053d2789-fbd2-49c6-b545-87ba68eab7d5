import { Button } from "../../../../components/Button";
import { useNotification } from "../../../../components/Notification";
import { uploadObject } from "../../../../services/objectSharing";
import { useChatContext } from "../../useChatInfo";
import { u2s } from "u2x";
import ShareIcon from "../../../../icons/ShareIcon";

import "./index.css";

export const ChatHeader = () => {
  const { chat, chatId, aiType, agentConfig } = useChatContext();
  const { showSuccess } = useNotification();

  const share = async () => {
    const { options } = chat;

    const hash = await uploadObject({ options, aiType, agentConfig });

    const url = new URL(window.location.origin);
    url.pathname = "/chat";
    url.searchParams.delete("chatId");
    url.searchParams.set("sharing", hash);
    navigator.clipboard.writeText(url.toString()).then(() => {
      showSuccess("分享链接已复制到剪贴板");
    });
  };

  const agentName = u2s(agentConfig.name);
  const agentIcon = u2s(agentConfig.icon);

  return (
    <div className="ChatHeader">
      {agentName && (
        <h2 className="agent-name">
          {agentIcon && <img src={agentIcon} alt={agentName} />}
          <div>{agentName}</div>
        </h2>
      )}
      <div className="chat-id">Chat ID: {chatId}</div>
      <div style={{ flex: 1 }}></div>
      <Button
        className="share"
        onClick={() => share()}
        icon={<ShareIcon />}
        variant="icon"
        style={{ fontSize: "0.9em" }}
      >
        分享聊天记录
      </Button>
    </div>
  );
};
