.ChatHeader {
  position: fixed;
  display: flex;
  align-items: center;
  padding: 0 calc(var(--gap) * 2);
  gap: calc(var(--gap) * 2);
  height: var(--header-height);
  left: var(--aside-width);
  right: 0;
  top: 0;
  width: calc(100% - var(--aside-width) - var(--gap) * 4);
  margin: 0 auto;
  background: #fff;
  z-index: 1;
  white-space: nowrap;
}

.ChatHeader > .agent-name {
  font-size: 1em;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5ch;
  white-space: nowrap;
  flex-shrink: 0;
}

.<PERSON>tHeader > .agent-name > img {
  width: 1.5em;
  max-height: 1.5em;
}

.ChatHeader > .chat-id {
  margin: 0;
  padding: 6px 12px;
  font-size: 0.9em;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6b7280;
  border-radius: 8px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}
