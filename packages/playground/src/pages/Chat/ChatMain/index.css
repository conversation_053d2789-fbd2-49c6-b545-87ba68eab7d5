.ChatMain {
  min-height: 100vh;
  max-width: 1200px;
  margin: 0 auto;
  --controls-height: 120px;
  padding-top: var(--header-height);
}

.ChatMain .conversation {
  padding: var(--card-padding);
  padding-bottom: calc(var(--controls-height) + var(--gap) * 4);
  display: flex;
  flex-direction: column;
  gap: calc(var(--gap) * 2);
}

.ChatMain .controls {
  width: calc(100% - var(--gap) * 4);
  max-width: calc(1200px - var(--gap) * 4);
  height: var(--controls-height);
  display: flex;
  padding: var(--card-padding);
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

  position: fixed;
  left: var(--aside-width);
  right: 0;
  width: calc(100% - var(--aside-width) - var(--gap) * 4);
  margin: 0 auto;
  bottom: calc(var(--gap) * 2);
}

.ChatMain .controls textarea {
  flex: 1;
  height: 100%;
  padding: 12px 16px;
  padding-right: 60px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-family: inherit;
  font-size: 0.9em;
  line-height: 1.6;
  resize: none;
  outline: none;
  transition: all 0.2s ease;
  overflow-x: hidden;
  overflow-y: auto;
}

.ChatMain .controls textarea:focus {
  border-color: #2563eb;
  background: white;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.ChatMain .controls textarea::placeholder {
  color: #9ca3af;
  transition: all 0.2s ease;
}

.ChatMain .controls textarea::placeholder {
  color: #d1d5db;
}

.ChatMain .controls button {
  width: 44px;
  height: 44px;
  padding: 0;
  border: none;
  border-radius: 50%;
  font-size: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  right: 32px;
  bottom: 0;
  top: 0;
  margin: auto;
}
