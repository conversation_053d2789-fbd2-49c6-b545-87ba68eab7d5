import type { ChatMessage } from "@hll/section-parser";
import React, { useReducer, useEffect, useRef, type ReactNode } from "react";
import { MessageView } from "./MessageView";
import { chatHistoryStore } from "../../../services/chatHistoryStore";
import { Button } from "../../../components/Button";
import { PauseIcon } from "../../../icons/PauseIcon";
import { SendIcon } from "../../../icons/SendIcon";
import { ChatHeader } from "./ChatHeader";
import { useChatContext } from "../useChatInfo";
import "./index.css";

export const ChatMain = () => {
  const context = useChatContext();
  const { chat } = context;
  const [, render] = useReducer(() => ({}), null);

  useEffect(() => {
    const { chat, ...rest } = context;
    let timer: number | undefined;
    let inc = 0;
    const put = () => chatHistoryStore.put({ options: chat.options, ...rest, timestamp: new Date(), state: 1 });
    const update = () => {
      if (timer === undefined) {
        put();
        inc = 0;
        timer = window.setTimeout(() => {
          if (inc) put();
          inc = 0;
          timer = undefined;
        }, 1000);
      } else {
        inc++;
      }
      render();
    };
    chat.watch(update);
    render();
    return () => {
      chat.unwatch(update);
      clearTimeout(timer);
    };
  }, [context]);

  const send = () => {
    if (chat.busy) {
      chat.abort();
      return;
    }

    const input = textareaRef.current?.value;
    textareaRef.current!.value = "";
    if (!input) return;
    try {
      chat.next(input);
    } catch (error) {
      console.error(error);
    }
  };

  const keyDownHandler = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (e.key === "Enter" && !e.metaKey && !e.shiftKey && !e.nativeEvent.isComposing) {
      if (chat.busy) return;
      send();
      e.preventDefault();
    }
  };

  const textareaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    const { hash } = location;
    if (!hash) return;
    if (!textareaRef.current) return;
    if (chat.messages.length > 0) return;
    try {
      textareaRef.current.value = decodeURIComponent(hash.slice(1));
      history.replaceState(null, "", location.pathname + location.search);
    } catch (error) {
      console.error(error);
    }
  }, [chat]);

  const recoverMessageTextIntoTextarea = (message: ChatMessage) => {
    if (message.role === "user") {
      // Recover the message.sections into the textarea.
      const { current: textarea } = textareaRef;
      if (!textarea) throw new Error("The textarea is not found");
      textarea.value = message.sections.buffer.flatMap((s) => (s.type === "content" ? s.value.buffer : [])).join("");
      textarea.focus();
    }
  };

  const rewindHandler = (message: ChatMessage) => {
    try {
      chat.rewindToBefore(message);
      recoverMessageTextIntoTextarea(message);
    } catch (error) {
      if (error instanceof Error) {
        alert(error.message);
      }
      console.error(error);
    }
  };

  let button: ReactNode;
  if (chat.busy) {
    button = (
      <Button onClick={() => chat.abort()} className="send-btn busy">
        <PauseIcon />
      </Button>
    );
  } else {
    button = (
      <Button className="send-btn" onClick={send}>
        <SendIcon />
      </Button>
    );
  }

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [chat]);

  return (
    <div className="ChatMain">
      <style>{`.Chat .Aside { box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05); }`}</style>
      <ChatHeader />
      <ChatConversation rewindHandler={rewindHandler} />
      <div className="controls" onKeyDown={keyDownHandler}>
        <textarea placeholder="输入你要说的话（Enter 发送，Shift+Enter 换行）" ref={textareaRef} />
        {button}
      </div>
    </div>
  );
};

const ChatConversation = ({ rewindHandler }: { rewindHandler: (message: ChatMessage) => void }) => {
  const { chat } = useChatContext();
  return (
    <div className="conversation">
      {chat.messages.map((message, index) => (
        <MessageView message={message} key={index} onRewind={() => rewindHandler(message)} />
      ))}
    </div>
  );
};
