import { type IterableBuffer } from "@hll/section-parser";
import { useEffect } from "react";
import { LoadingIcon } from "../../../icons/LoadingIcon";
import { useIterableBuffer } from "../../../utils/useIterableBuffer";
import { marked } from "marked";
import "github-markdown-css/github-markdown-light.css";

const renderer = new marked.Renderer();
renderer.link = ({ href, title, text }) => {
  const titleAttr = title ? ` title="${title}"` : "";
  return `<a href="${href}"${titleAttr} target="_blank" rel="noopener noreferrer">${text}</a>`;
};

marked.setOptions({ renderer });

export const IterableBufferView = ({
  value,
  autoLoading,
  markdown,
}: {
  value: IterableBuffer<string>;
  autoLoading?: boolean;
  markdown?: boolean;
}) => {
  const { data, loading, error } = useIterableBuffer(value);

  useEffect(() => {
    document.documentElement.scrollTo({
      top: document.documentElement.scrollHeight,
      behavior: "instant",
    });
  }, [data]);

  if (autoLoading) {
    if (data === "" && loading) return <LoadingIcon style={{ animation: "spin 1s linear infinite" }} />;
  }

  if (error) return <div>{error instanceof Error ? error.message : String(error)}</div>;

  // Remove the leading spaces and newlines.
  const text = data.replace(/^(\s*?\n)*/g, "");

  if (text === "") return null;

  if (markdown) {
    return <div className="markdown-body" dangerouslySetInnerHTML={{ __html: marked(text) }} />;
  }
  return <span>{text}</span>;
};
