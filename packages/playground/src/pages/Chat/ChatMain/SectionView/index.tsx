import { type Section, type SectionWithString } from "@hll/section-parser";
import { IterableBufferView } from "../IterableBufferView";
import { SectionViewForToolCall } from "./SectionViewForToolCall";
import { SectionViewForThink } from "./SectionViewForThink";

import "./index.css";

const SectionViewForContent = ({ section }: { section: SectionWithString }) => {
  return (
    <div className="SectionViewContent">
      <IterableBufferView value={section.value} autoLoading markdown />
    </div>
  );
};

export const SectionView = ({ section }: { section: Section }) => {
  switch (section.type) {
    case "content":
      return <SectionViewForContent section={section} />;
    case "think":
      return <SectionViewForThink section={section} />;
    case "call":
      return <SectionViewForToolCall section={section} />;
    default:
      return null;
  }
};
