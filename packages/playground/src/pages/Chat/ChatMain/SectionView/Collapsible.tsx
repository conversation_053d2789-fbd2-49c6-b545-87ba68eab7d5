import { type IterableBuffer, BufferState } from "@hll/section-parser";
import { useRef, useState, useEffect } from "react";
import { debounce } from "../../../../utils/debounce";

export const Collapsible = ({ value, children }: { value: IterableBuffer<unknown>; children: React.ReactNode }) => {
  const ref = useRef<HTMLDivElement>(null);
  const [expanded, setExpanded] = useState(false);
  const [scrollHeight, setScrollHeight] = useState(0);
  const classes = ["Collapsible"];
  if (expanded) classes.push("expanded");

  useEffect(() => {
    if (expanded) return;
    if (value.state !== BufferState.Open) return;
    const update = debounce(() => {
      const { current } = ref;
      if (!current) return;
      current.scrollTop = current.scrollHeight;
      setScrollHeight(current.offsetHeight);
    });
    const controller = { enqueue: update, close: update, error: update };
    value.tee(controller);
    return () => value.untee(controller);
  }, [expanded, value]);

  if (value.state === BufferState.Open) classes.push("generating");
  if (ref.current?.offsetHeight ?? 0 > scrollHeight) classes.push("overflow");
  return (
    <div
      className={classes.join(" ")}
      ref={ref}
      onClick={() => {
        setExpanded(!expanded);
      }}
    >
      {children}
    </div>
  );
};
