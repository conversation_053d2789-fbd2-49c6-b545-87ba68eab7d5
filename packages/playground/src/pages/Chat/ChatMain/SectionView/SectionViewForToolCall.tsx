import { B<PERSON>erState, LiveBuffer, type IterableBuffer, type SectionWithCall } from "@hll/section-parser";
import { useMemo } from "react";
import { IterableBufferView } from "../IterableBufferView";
import { Collapsible } from "./Collapsible";

export const SectionViewForToolCall = ({ section }: { section: SectionWithCall }) => {
  const { value } = section;

  const allValue: IterableBuffer<unknown> = useMemo(() => {
    const buffer = new LiveBuffer();
    const controller = {
      enqueue(chunk: unknown) {
        if (buffer.state !== BufferState.Open) return;
        buffer.enqueue(chunk);
      },
      close() {
        if (buffer.state !== BufferState.Open) return;
        buffer.close();
      },
      error(error: unknown) {
        if (buffer.state !== BufferState.Open) return;
        buffer.error(error);
      },
    };
    value.name.tee(controller);
    value.arguments.tee(controller);
    value.result.tee(controller);
    return buffer;
  }, [value]);

  return (
    <div className="SectionViewForToolCall">
      <Collapsible value={allValue}>
        <IterableBufferView value={value.name} />(
        <IterableBufferView value={value.arguments} />)
        {value.arguments.state === BufferState.Closed ? (
          <>
            {" => "}
            <IterableBufferView value={value.result} autoLoading />
          </>
        ) : null}
      </Collapsible>
    </div>
  );
};
