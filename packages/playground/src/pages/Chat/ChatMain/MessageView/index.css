.MessageView {
  padding: 0 calc(var(--gap) * 2);
}

.MessageView > .sections {
  background: white;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  gap: calc(var(--gap) * 1);
}

.MessageView.user {
  align-self: flex-end;
}

.MessageView.user > .sections {
  background: #eee;
  padding: calc(var(--gap) * 1) calc(var(--gap) * 1.5);
}

.MessageView h1 {
  font-size: 1.2em;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
  padding: 0;
}

.MessageView > .operations {
  display: flex;
  align-items: center;
  gap: calc(var(--gap) * 2);
  margin-top: calc(var(--gap) * 1);
  visibility: hidden;
  opacity: 0;
  transition: all 0.2s ease;
}

.MessageView.user > .operations {
  justify-content: flex-end;
}

.MessageView:hover > .operations {
  visibility: visible;
  opacity: 1;
}

.MessageView.generating > .operations {
  visibility: hidden;
}

.MessageView > .operations button {
  border: none;
  background: none;
  border-radius: 4px;
  padding: 0;
  margin: 0;
  line-height: 1.5;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #94a3b8;
}

.MessageView > .operations button:hover {
  color: #2563eb;
}

.MessageView .markdown-body {
  background: none;
}

@keyframes rainbow-border {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
