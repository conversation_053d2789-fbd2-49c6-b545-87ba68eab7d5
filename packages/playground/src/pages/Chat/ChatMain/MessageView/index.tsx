import { BufferState, type ChatMessage, type Section } from "@hll/section-parser";
import { useState, useEffect } from "react";
import { SectionView } from "../SectionView";
import { Button } from "../../../../components/Button";

import "./index.css";
import { getErrorMessage } from "../../../../utils/getErrorMessage";
import { Tip } from "../../../../components/Tip";

interface MessageViewProps {
  message: ChatMessage;
  onRewind: () => void;
}

export const MessageView = ({ message, onRewind }: MessageViewProps) => {
  const [, render] = useState<Section>();
  const [error, setError] = useState<unknown>();

  useEffect(() => {
    // setError(undefined);
    const controller = {
      enqueue: render,
      error: (error: unknown) => {
        setError(error);
        console.error(error);
      },
    };
    message.sections.tee(controller);
    return () => message.sections.untee(controller);
  }, [message]);

  const { sections } = message;

  const classes = ["MessageView"];
  if (sections.state === BufferState.Open) classes.push("generating");
  classes.push(message.role);

  return (
    <div key={message.index} className={classes.join(" ")}>
      <div className="sections">
        {sections.state === BufferState.Open && sections.buffer.length === 0 ? <Tip type="loading"></Tip> : null}
        {sections.buffer.map((section, index) => (
          <SectionView section={section} key={index} />
        ))}
      </div>
      {error ? <Tip type="error">{getErrorMessage(error)}</Tip> : null}
      <div className="operations">
        {message.role === "assistant" ? (
          <Button onClick={() => onRewind()}>重试</Button>
        ) : (
          <Button onClick={() => onRewind()}>编辑</Button>
        )}
      </div>
    </div>
  );
};
