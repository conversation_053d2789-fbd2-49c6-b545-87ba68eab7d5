import { BufferState, type ChatMessage, type Section } from "@hll/section-parser";
import { useState, useEffect } from "react";
import { SectionView } from "../SectionView";
import { Button } from "../../../../components/Button";

import "./index.css";

interface MessageViewProps {
  message: ChatMessage;
  onRewind: () => void;
}

export const MessageView = ({ message, onRewind }: MessageViewProps) => {
  const [, render] = useState<Section>();
  // const [error, setError] = useState<unknown>();

  useEffect(() => {
    // setError(undefined);
    const controller = {
      enqueue: render,
      error: (error: unknown) => {
        console.error(error);
      },
    };
    message.sections.tee(controller);
    return () => message.sections.untee(controller);
  }, [message]);

  const classes = ["MessageView"];
  if (message.sections.state === BufferState.Open) classes.push("generating");
  classes.push(message.role);

  return (
    <div key={message.index} className={classes.join(" ")}>
      <div className="sections">
        {message.sections.buffer.map((section, index) => (
          <SectionView section={section} key={index} />
        ))}
      </div>
      {/* {error ? <div className="message-error">{error instanceof Error ? error.message : String(error)}</div> : null} */}
      <div className="operations">
        {message.role === "assistant" ? (
          <Button onClick={() => onRewind()}>重试</Button>
        ) : (
          <Button onClick={() => onRewind()}>编辑</Button>
        )}
      </div>
    </div>
  );
};
