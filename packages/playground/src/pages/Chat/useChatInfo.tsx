import { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { u2o, u2s } from "u2x";
import { chatHistoryStore, type UncheckedAgentConfig } from "../../services/chatHistoryStore";
import { downloadObject } from "../../services/objectSharing";
import { rejectNon200 } from "../../utils/rejectNon200";
import { initChat } from "./initChat";
import type { Chat } from "@hll/section-parser";
import React from "react";
import { ErrorDisplay } from "../../components/ErrorDisplay";

export const randomId = () => Math.random().toString(36).substring(2, 15);

export class ChatContextImpl {
  readonly chat: Chat;
  readonly chatId: string;
  readonly aiType: string;
  readonly agentConfig: UncheckedAgentConfig;
  constructor(chat: Chat, chatId: string, aiType: string, agentConfig: UncheckedAgentConfig) {
    this.chat = chat;
    this.chatId = chatId;
    this.aiType = aiType;
    this.agentConfig = agentConfig;
  }
}

export const ChatContext = React.createContext<ChatContextImpl | null>(null);

export const useChatContext = () => {
  const chatHolder = React.useContext(ChatContext);
  if (!chatHolder) throw new Error("useChatContext must be used within a ChatProvider");
  return chatHolder;
};

export const ChatProvider = ({ children }: { children: React.ReactNode }) => {
  const { value: chatHolder, error, loading } = useChatHolder();

  if (error) {
    return <ErrorDisplay error={error} />;
  }

  if (!chatHolder) return null;

  return (
    <div className={`ChatProvider ${loading ? "loading" : ""}`}>
      <ChatContext.Provider value={chatHolder}>{children}</ChatContext.Provider>
    </div>
  );
};

const useChatHolder = () => {
  const navigate = useNavigate();
  const { search } = useLocation();
  const [value, setValue] = useState<ChatContextImpl | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    console.log("useChatHolder", search);
    setLoading(true);
    const usp = new URLSearchParams(search);

    const fail: ((reason: any) => void | PromiseLike<void>) | null | undefined = (error) => {
      setError(error);
      setLoading(false);
    };

    const chatId = usp.get("chatId");
    if (chatId) {
      chatHistoryStore.get(chatId).then(async (record) => {
        // await new Promise((resolve) => setTimeout(resolve, 3000));
        if (!record) return setError(new Error(`Chat '${chatId}' not found`));
        const { aiType, options = {}, agentConfig = {} } = record;
        initChat(aiType, chatId, options).then((chat) => {
          setError(null);
          setValue(new ChatContextImpl(chat, chatId, aiType, agentConfig));
          setLoading(false);
        }, fail);
      });
      return;
    }

    const putOptions = (
      chatId: string,
      options: object,
      aiType: string | undefined,
      agentConfig: UncheckedAgentConfig
    ) => {
      return chatHistoryStore.put({
        aiType: aiType ?? "haitun",
        chatId,
        options,
        agentConfig,
        timestamp: new Date(),
        state: 0,
      });
    };

    const sharing = usp.get("sharing");
    if (sharing) {
      downloadObject(sharing)
        .then(async (obj) => {
          const { options, aiType, agentConfig } = obj;
          const chatId = randomId();
          await putOptions(chatId, u2o(options), u2s(aiType), u2o(agentConfig));
          navigate(`/chat?chatId=${chatId}${location.hash}`);
        })
        .catch(fail);
      return;
    }

    const agent = usp.get("agent");
    if (agent) {
      fetch(agent, { method: "GET", credentials: "include" })
        .then(rejectNon200)
        .then((res) => res.json())
        .then(async (obj) => {
          let agentConfig = u2o(obj);
          if (typeof agentConfig.href === "string") {
            agentConfig = await fetch(agentConfig.href, { credentials: "include" })
              .then(rejectNon200)
              .then((res) => res.json())
              .then((obj) => u2o(obj));
          }
          const { options, aiType } = agentConfig as UncheckedAgentConfig;
          const chatId = randomId();
          await putOptions(chatId, u2o(options), u2s(aiType), agentConfig);
          navigate(`/chat?chatId=${chatId}${location.hash}`);
        })
        .catch(fail);
      return;
    }

    setLoading(false);
  }, [search, navigate]);

  return { value, error, loading };
};
