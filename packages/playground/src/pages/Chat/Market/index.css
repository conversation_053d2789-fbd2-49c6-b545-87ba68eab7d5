.Market {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.Market-body {
  flex: 1;
  padding: calc(var(--gap) * 2);
  display: flex;
  flex-direction: column;
  gap: calc(var(--gap) * 2);
  overflow-y: auto;
}

.Market-body > .Tip {
  margin: 60px auto;
}

.Market-body.loading {
  opacity: 0.7;
  pointer-events: none;
}

.Market-body.loading .AgentCard {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.Market .filter {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.Market .agents {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
}
