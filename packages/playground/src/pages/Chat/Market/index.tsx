import { Button } from "../../../components/Button";
import { Link } from "react-router-dom";
import { useMarketFilterManager } from "./MarketFilterManager";
import { SearchInput } from "../../../components/SearchInput";
import { AgentCard } from "../../../components/AgentCard";
import { publicAgentsStore, useAllAgentConfigs } from "@hll/quantum-store";
import { CategoryTabs } from "../../../components/CategoryTabs";
import { MainHeader } from "../../../components/MainHeader";
import { useSlot } from "@hll/use-slot";
import { Tip } from "../../../components/Tip";
import { useMemo } from "react";
import { getErrorMessage } from "../../../utils/getErrorMessage";

import "./index.css";

export const Market = () => {
  const { data, loading, error } = useAllAgentConfigs(publicAgentsStore);

  const fm = useMarketFilterManager();

  useSlot(
    "MainHeader-title",
    () => (
      <div style={{ display: "flex", alignItems: "center", gap: "var(--gap)" }}>
        <span>智能体市场</span>
      </div>
    ),
    []
  );

  useSlot(
    "MainHeader-right",
    () => (
      <Link to="/agents/mine">
        <Button>我的智能体</Button>
      </Link>
    ),
    []
  );

  const content = useMemo(() => {
    if (!data && loading) {
      return <Tip type="loading">加载中...</Tip>;
    }
    if (error) {
      return <Tip type="error">{getErrorMessage(error)}</Tip>;
    }
    if (!data || data.length == 0) {
      return <Tip type="empty">暂无可用智能体</Tip>;
    }
    const filteredData = (data || []).filter(fm.getFilterFunction());
    if (filteredData.length == 0) {
      return <Tip type="empty">暂无匹配智能体</Tip>;
    }
    return (
      <div className="agents">
        {filteredData.map((agent) => (
          <AgentCard key={agent.id} agent={agent} />
        ))}
      </div>
    );
  }, [data, loading, error, fm, fm.activeCategory, fm.searchQuery]);

  return (
    <div className={`Market`}>
      <MainHeader />
      <div className={`Market-body ${loading ? "loading" : ""}`}>
        <div className="filter">
          <CategoryTabs
            categories={fm.categories}
            activeCategory={fm.activeCategory}
            onChange={(v) => fm.setActiveCategory(v)}
          />
          <SearchInput
            value={fm.searchQuery}
            onInput={(e) => fm.setSearchQuery(e.currentTarget.value)}
            placeholder="搜索智能体"
          />
        </div>
        {content}
      </div>
    </div>
  );
};
