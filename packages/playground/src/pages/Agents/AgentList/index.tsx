import { useSlot } from "@hll/use-slot";
import { getAgentStore, myAgentsStore, useAllAgentConfigs } from "@hll/quantum-store";
import { useMemo } from "react";
import { Tip } from "../../../components/Tip";
import { getErrorMessage } from "../../../utils/getErrorMessage";
import { navigationSections } from "../../../components/ManagerMenu";
import { useLocation, useNavigate } from "react-router-dom";
import { useUserInfo } from "../../../services/user";
import { useTagStore, publicAgentsStore } from "@hll/quantum-store";
import { DataTable, type Column } from "../../../components/DataTable";

import { PlusIcon } from "../../../icons/PlusIcon";
import { Button } from "../../../components/Button";
import { nextId } from "../../../services/agentIdManager";
import { quantumAgentConfigSchemaUrl } from "../../../constants";
import { showError } from "../../../components/Notification";
import { ChatIcon } from "../../../icons/ChatIcon";
import { EditIcon } from "../../../icons/EditIcon";
import { Link } from "react-router-dom";
import { Label } from "../../../components/Label";
import { Tooltip } from "../../../components/Tooltip";
import { FeishuAvatar } from "../../../components/FeishuAvatar";

import "./index.css";
import { PersonIcon } from "../../../icons/PersonIcon";

const createAgent = async () => {
  const agentId = await nextId();
  const store = getAgentStore(agentId);
  await store.upload(
    {
      $schema: quantumAgentConfigSchemaUrl,
      name: "Agent Demo " + agentId,
      description: "这是一个智能体范例，你可以定制你自己的智能体",
      aiType: "haitun",
      options: {
        system_message: "你是一个神奇的智能体，请根据用户的问题给出回答。",
        welcome_message: "你好，我是神奇的智能体，你可以问我任何问题。",
        additional_api_tools: [
          {
            name: "whoAmI",
            description: "获取当前用户信息",
            method: "GET",
            url: "https://gateway-office-public.huolala.work/fe-van-api-svc/api/user",
            query: {
              properties: {
                info: {
                  type: "string",
                  const: "me",
                  description: "固定填 me",
                },
              },
              required: ["info"],
            },
          },
        ],
      },
    },
    "null"
  );
  await myAgentsStore.add(agentId);
  return agentId;
};

export const AgentList = () => {
  const { pathname } = useLocation();
  const navigate = useNavigate();
  const { store, label } = navigationSections.agents.items.find((item) => item.path === pathname) || {};

  const { data: userInfo } = useUserInfo();
  const { data: publicAgentIds } = useTagStore(publicAgentsStore);

  useSlot("MainHeader-title", () => label, [label]);

  useSlot(
    "MainHeader-right",
    () => {
      return (
        <Button
          variant="primary"
          icon={<PlusIcon />}
          onClick={async () => {
            try {
              const agentId = await createAgent();
              navigate(`/agents/${agentId}`);
            } catch (error) {
              showError(error);
            }
          }}
        >
          创建 AI 智能体
        </Button>
      );
    },
    []
  );

  const { data: agentConfigs = [], loading, error } = useAllAgentConfigs(store);

  const columns: Column<(typeof agentConfigs)[0]>[] = useMemo(
    () => [
      {
        key: "agent-info",
        title: "智能体",
        render: (_, agent) => {
          const { data, id } = agent;
          const { name, icon } = data;
          return (
            <div className="agent-info-content">
              {icon && <img className="agent-icon" src={icon} alt={name || id} />}
              <div className="agent-details">
                <div className="agent-name">{name || id}</div>
                <div className="agent-id">ID: {id}</div>
              </div>
            </div>
          );
        },
      },
      {
        key: "description",
        title: "描述",
        render: (_, agent) => {
          const { data } = agent;
          return <span className="agent-description">{data.description || "暂无描述"}</span>;
        },
      },
      {
        key: "status",
        title: "状态",
        align: "center",
        render: (_, agent) => {
          const isPublic = publicAgentIds?.includes(agent.id);
          return <Label color={isPublic ? "primary" : "default"}>{isPublic ? "公开" : "私有"}</Label>;
        },
      },
      {
        key: "owner",
        title: "所有者",
        render: (_, agent) => {
          return <FeishuAvatar uniqId={agent.owner} />;
        },
      },
      {
        key: "timestamp",
        title: "创建时间",
        render: (_, agent) => {
          return <span className="agent-time">{agent.timestamp ? agent.timestamp.toLocaleString("zh-CN") : "-"}</span>;
        },
      },
      {
        key: "actions",
        title: "操作",
        align: "right",
        render: (_, agent) => {
          const isOwner = userInfo ? userInfo.uniqId === agent.owner : null;
          return (
            <div className="action-buttons">
              <Tooltip content="对话">
                <Link className="action-link" to={`/chat?agent=${getAgentStore(agent.id).base}`} target="_blank">
                  <Button icon={<ChatIcon />} size="small" />
                </Link>
              </Tooltip>
              {isOwner === true && (
                <Tooltip content="编辑">
                  <Link className="action-link" to={`/agents/${agent.id}`}>
                    <Button icon={<EditIcon />} variant="primary" size="small" />
                  </Link>
                </Tooltip>
              )}
              {isOwner === true && <Tooltip content="成员">
                <Link className="action-link" to={`/members/${agent.id}`}>
                  <Button icon={<PersonIcon />} size="small"></Button>
                </Link>
              </Tooltip>}
            </div>
          );
        },
      },
    ],
    [userInfo, publicAgentIds]
  );

  const content = useMemo(() => {
    if (loading) return <Tip type="loading">加载中...</Tip>;
    if (error) return <Tip type="error">{getErrorMessage(error)}</Tip>;
    return <DataTable data={agentConfigs} columns={columns} emptyText="暂无智能体" className="agents-table" />;
  }, [agentConfigs, columns, loading, error]);

  return <div className={`AgentList ${loading ? "loading" : ""}`}>{content}</div>;
};
