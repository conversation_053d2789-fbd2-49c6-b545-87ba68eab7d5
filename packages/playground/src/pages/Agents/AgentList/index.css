.AgentList {
  display: flex;
  flex-direction: column;
  padding: var(--card-padding);
  background: #fafafa;
  height: 100%;
  overflow-y: auto;
}

.AgentList.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* Agent Info Column */
.AgentList .agent-info-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.AgentList .agent-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
}

.AgentList .agent-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.AgentList .agent-name {
  font-weight: 600;
  color: #111827;
}

.AgentList .agent-id {
  font-size: 0.875em;
  color: #6b7280;
  font-weight: 500;
}

/* Agent Description Column */
.AgentList .agent-description {
  color: #6b7280;
  max-width: 300px;
  line-height: 1.4;
}

/* Agent Owner Column */
.AgentList .agent-owner {
  color: #6b7280;
  font-weight: 500;
}

/* Agent Time Column */
.AgentList .agent-time {
  color: #9ca3af;
  white-space: nowrap;
}

/* Agent Actions Column */
.AgentList .action-buttons {
  display: inline-flex;
  gap: 1ch;
}

.AgentList .action-link {
  display: flex;
  align-items: center;
  text-decoration: none;
}
