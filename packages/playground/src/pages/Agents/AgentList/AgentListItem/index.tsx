import { getAgentStore, publicAgentsStore, useTagStore, type TagResult } from "@hll/quantum-store";
import { Tooltip } from "../../../../components/Tooltip";
import type { FinalQuantumAgentConfig } from "@hll/quantum-store/dist/AgentStore";
import { useUserInfo } from "../../../../services/user";
import { ChatIcon } from "../../../../icons/ChatIcon";
import { EditIcon } from "../../../../icons/EditIcon";
import { Link } from "react-router-dom";
import { Label } from "../../../../components/Label";

import "./index.css";
import { FeishuAvatar } from "../../../../components/FeishuAvatar";

export const AgentListItem = ({
  agent,
}: {
  agent: TagResult<FinalQuantumAgentConfig> & { agentId: string; data: FinalQuantumAgentConfig };
}) => {
  const { data, timestamp, owner, agentId } = agent;
  const { name, description, icon } = data;
  const { data: userInfo } = useUserInfo();
  const { data: publicAgentIds } = useTagStore(publicAgentsStore);
  const isOwner = userInfo ? userInfo.uniqId === owner : null;
  const isPublic = publicAgentIds?.includes(agent.agentId);
  return (
    <div className="AgentListItem-card">
      {/* 样式已移至 index.css */}
      <div className="AgentListItem-header">
        {icon && <img className="AgentListItem-icon" src={icon} alt={name || agentId} />}
        <div style={{ flex: 1 }}>
          <div style={{ display: "flex", gap: "var(--gap)", alignItems: "center", justifyContent: "space-between" }}>
            <div className="AgentListItem-title">{name || agentId}</div>
            <div style={{ display: "flex", gap: "var(--gap)", alignItems: "center" }}>
              <Label color={isPublic ? "primary" : "default"}>{isPublic ? "公开" : "私有"}</Label>
              <span className="AgentListItem-id">ID: {agentId}</span>
            </div>
          </div>
          <div className="AgentListItem-desc">{description || "暂无描述"}</div>
        </div>
      </div>
      <div className="AgentListItem-footer">
        <div className="AgentListItem-actions">
          <Tooltip content="对话">
            <Link
              className="AgentListItem-tooltip-link"
              to={`/chat?agent=${getAgentStore(agentId).base}`}
              target="_blank"
            >
              <ChatIcon />
            </Link>
          </Tooltip>
          {isOwner === true && (
            <Tooltip content="编辑">
              <Link className="AgentListItem-tooltip-link" to={`/agents/${agentId}`}>
                <EditIcon />
              </Link>
            </Tooltip>
          )}
        </div>
        <div style={{ textAlign: "right" }}>
          <div className="AgentListItem-owner">
            <FeishuAvatar uniqId={owner} />
          </div>
          {timestamp ? <time className="AgentListItem-time">{timestamp.toLocaleString("zh-CN")}</time> : null}
        </div>
      </div>
    </div>
  );
};
