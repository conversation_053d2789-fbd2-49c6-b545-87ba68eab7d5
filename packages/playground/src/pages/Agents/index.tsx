import { ManagerMenu } from "../../components/ManagerMenu";
import { MainHeader } from "../../components/MainHeader";
import { Outlet } from "react-router-dom";

import "./index.css";

export { AgentList } from "./AgentList";
export { AgentEditor } from "./AgentEditor";

export const Agents = () => {
  return (
    <div className="Agents">
      <ManagerMenu />
      <div style={{ flex: 1, display: "flex", flexDirection: "column" }}>
        <MainHeader />
        <Outlet />
      </div>
    </div>
  );
};
