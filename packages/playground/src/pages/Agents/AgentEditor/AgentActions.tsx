import { useEffect, useMemo } from "react";
import { Button } from "../../../components/Button";
import { Dropdown } from "../../../components/Dropdown";
import { Tip } from "../../../components/Tip";
import { MoreIcon } from "../../../icons/MoreIcon";
import { DeleteIcon } from "../../../icons/DeleteIcon";
import { VisibilityIcon } from "../../../icons/VisibilityIcon";
import { VisibilityOffIcon } from "../../../icons/VisibilityOffIcon";
import { SaveIcon } from "../../../icons/SaveIcon";
import { getAgentStore, getInboxStore, myAgentsStore, publicAgentsStore, useTagStore } from "@hll/quantum-store";
import { useNavigate, type NavigateFunction } from "react-router-dom";
import { showSuccess, showError } from "../../../components/Notification";
import { PlusIcon } from "../../../icons/PlusIcon";
import { useEditable } from "../../../utils/useEditable";
import { PersonIcon } from "../../../icons/PersonIcon";

interface AgentActionsProps {
  agentId: string;
  jsonValue: string;
}

const togglePrivateOrPublic = async (agentId: string, isPublic: boolean) => {
  if (isPublic == null) return;
  try {
    if (isPublic) {
      await publicAgentsStore.remove(agentId);
    } else {
      await publicAgentsStore.add(agentId);
    }
    showSuccess(
      <>
        设置成功，当前状态为 <strong>{!isPublic ? "公开" : "私有"}</strong>
      </>
    );
  } catch (e) {
    showError(e, "设置失败");
  }
};

const handleSave = async (agentId: string, jsonValue: string) => {
  if (!agentId) return;
  const store = getAgentStore(agentId);
  try {
    await store.upload(JSON.parse(jsonValue), store.head);
    // Update in background, no need to await.
    store.getWithoutCache();
    showSuccess("保存成功");
  } catch (e) {
    showError(e, "保存失败");
  }
};

const handleDelete = async (agentId: string, navigate: NavigateFunction) => {
  if (!agentId || !confirm("确定要删除这个智能体吗？")) return;
  const store = getAgentStore(agentId);
  try {
    await store.delete(store.lastResult?.head || "null");
    await Promise.all([myAgentsStore.remove(agentId), publicAgentsStore.remove(agentId)]);
    showSuccess("删除成功");
    navigate("/agents");
  } catch (e) {
    showError(e, "删除失败");
  }
};

const AgentActions = ({ agentId, jsonValue }: AgentActionsProps) => {
  const navigate = useNavigate();
  const pa = useTagStore(publicAgentsStore);
  const store = getAgentStore(agentId);
  const { data: agentData, loading: agentDataLoading, error: agentDataError, owner, head } = useTagStore(store);
  const { data: editable, loading: editableLoading, error: editableError } = useEditable(owner, head);

  const loading = agentDataLoading || editableLoading;
  const error = agentDataError || editableError;
  const isPublic = pa.data?.includes(agentId) ?? null;

  const hasChanges = useMemo(() => {
    if (!agentData) return false;
    try {
      return JSON.stringify(agentData, null, 2) !== jsonValue;
    } catch {
      return false;
    }
  }, [agentData, jsonValue]);

  useEffect(() => {
    if (error) {
      showError(error);
    }
  }, [error]);

  useEffect(() => {
    if (!hasChanges) return;
    const handler = (e: BeforeUnloadEvent) => e.preventDefault();
    window.addEventListener("beforeunload", handler);
    return () => window.removeEventListener("beforeunload", handler);
  }, [hasChanges]);

  if (loading) {
    return <Tip type="loading" />;
  }

  if (editable) {
    return (
      <>
        {hasChanges ? null : <Tip type="weak">当前无变更</Tip>}
        <Button variant="primary" onClick={() => handleSave(agentId, jsonValue)} icon={<SaveIcon />}>
          保存
        </Button>
        <Dropdown
          trigger={<MoreIcon />}
          items={[
            isPublic != null && {
              key: "togglePrivateOrPublic",
              label: <>{isPublic ? "设为私有" : "设为公开"}</>,
              icon: isPublic ? <VisibilityOffIcon /> : <VisibilityIcon />,
              onClick: () => togglePrivateOrPublic(agentId, isPublic),
            },
            {
              key: "memberSettings",
              label: "权限管理",
              icon: <PersonIcon />,
              onClick: () => navigate(`/${agentId}/members`),
            },
            {
              key: "delete",
              label: "删除",
              icon: <DeleteIcon />,
              danger: true,
              onClick: () => handleDelete(agentId, navigate),
            },
          ].filter((i) => i !== false)}
          placement="bottomRight"
        />
      </>
    );
  } else {
    return (
      <>
        {owner && (
          <Button
            icon={<PlusIcon />}
            onClick={async () => {
              const inbox = getInboxStore(owner);
              try {
                await inbox.send({ type: "requestAgentPermission", agentId });
                showSuccess("申请已发送成功");
              } catch (e) {
                showError(e, "申请失败");
              }
            }}
          >
            申请权限
          </Button>
        )}
      </>
    );
  }
};

export { AgentActions };
