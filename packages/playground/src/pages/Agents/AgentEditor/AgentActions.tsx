import { useEffect, useMemo } from "react";
import { Button } from "../../../components/Button";
import { Dropdown } from "../../../components/Dropdown";
import { Tip } from "../../../components/Tip";
import { MoreIcon } from "../../../icons/MoreIcon";
import { DeleteIcon } from "../../../icons/DeleteIcon";
import { VisibilityIcon } from "../../../icons/VisibilityIcon";
import { VisibilityOffIcon } from "../../../icons/VisibilityOffIcon";
import { CopyIcon } from "../../../icons/CopyIcon";
import { SaveIcon } from "../../../icons/SaveIcon";
import { getAgentStore, myAgentsStore, publicAgentsStore, useTagStore } from "@hll/quantum-store";
import { useNavigate, type NavigateFunction } from "react-router-dom";
import { showSuccess, showError } from "../../../components/Notification";
import { useUserInfo } from "../../../services/user";

interface AgentActionsProps {
  agentId: string;
  jsonValue: string;
}

const togglePrivateOrPublic = async (agentId: string, isPublic: boolean) => {
  if (isPublic == null) return;
  try {
    if (isPublic) {
      await publicAgentsStore.remove(agentId);
    } else {
      await publicAgentsStore.add(agentId);
    }
    showSuccess(
      <>
        设置成功，当前状态为 <strong>{!isPublic ? "公开" : "私有"}</strong>
      </>
    );
  } catch (e) {
    showError(e, "设置失败");
  }
};

const handleSave = async (agentId: string, jsonValue: string) => {
  if (!agentId) return;
  const store = getAgentStore(agentId);
  try {
    await store.upload(JSON.parse(jsonValue), store.head);
    // Update in background, no need to await.
    store.get();
    showSuccess("保存成功");
  } catch (e) {
    showError(e, "保存失败");
  }
};

const handleDelete = async (agentId: string, navigate: NavigateFunction) => {
  if (!agentId || !confirm("确定要删除这个智能体吗？")) return;
  const store = getAgentStore(agentId);
  try {
    await store.delete(store.lastResult?.head || "null");
    await Promise.all([myAgentsStore.remove(agentId), publicAgentsStore.remove(agentId)]);
    showSuccess("删除成功");
    navigate("/agents");
  } catch (e) {
    showError(e, "删除失败");
  }
};

const handleCopyConfigLink = async (agentId: string) => {
  const store = getAgentStore(agentId);
  const configLink = `${store.base}`;
  try {
    await navigator.clipboard.writeText(configLink);
    showSuccess("配置链接已复制到剪贴板");
  } catch (e) {
    showError(e, "复制失败");
  }
};

const AgentActions = ({ agentId, jsonValue }: AgentActionsProps) => {
  const { data: userInfo, loading: userInfoLoading, error: userInfoError } = useUserInfo();
  const pa = useTagStore(publicAgentsStore);
  const navigate = useNavigate();
  const store = getAgentStore(agentId);
  const { data: agentData, loading: agentDataLoading, error: agentDataError, owner } = useTagStore(store);

  const loading = userInfoLoading || agentDataLoading;
  const error = userInfoError || agentDataError;
  const isPublic = pa.data?.includes(agentId) ?? null;
  const isOwner = userInfo ? owner === userInfo.uniqId : null;

  const hasChanges = useMemo(() => {
    if (!agentData) return false;
    try {
      return JSON.stringify(agentData, null, 2) !== jsonValue;
    } catch {
      return false;
    }
  }, [agentData, jsonValue]);

  useEffect(() => {
    if (error) {
      showError(error);
    }
  }, [error]);

  useEffect(() => {
    if (!hasChanges) return;
    const handler = (e: BeforeUnloadEvent) => e.preventDefault();
    window.addEventListener("beforeunload", handler);
    return () => window.removeEventListener("beforeunload", handler);
  }, [hasChanges]);

  const dropdownItems = [
    {
      key: "copyConfigLink",
      label: "复制配置链接",
      icon: <CopyIcon />,
      onClick: () => handleCopyConfigLink(agentId),
    },
    isPublic != null && {
      key: "togglePrivateOrPublic",
      label: <>{isPublic ? "设为私有" : "设为公开"}</>,
      icon: isPublic ? <VisibilityOffIcon /> : <VisibilityIcon />,
      onClick: () => togglePrivateOrPublic(agentId, isPublic),
    },
    {
      key: "delete",
      label: "删除",
      icon: <DeleteIcon />,
      danger: true,
      onClick: () => handleDelete(agentId, navigate),
    },
  ].filter((i) => i !== false);

  if (loading) {
    return <Tip type="loading" />;
  }

  if (isOwner !== true) {
    return null;
  }

  return (
    <>
      {hasChanges ? null : <Tip type="weak">当前无变更</Tip>}
      <Button variant="primary" onClick={() => handleSave(agentId, jsonValue)} icon={<SaveIcon />}>
        保存
      </Button>
      <Dropdown trigger={<MoreIcon />} items={dropdownItems} placement="bottom" />
    </>
  );
};

export default AgentActions;
