.SitesList {
  display: flex;
  flex-direction: column;
  padding: var(--card-padding);
  background: #fafafa;
  height: 100%;
  overflow-y: auto;
}

.SitesList.loading {
  opacity: 0.7;
  pointer-events: none;
}

.SitesList > .filter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: calc(var(--gap) * 2);
}

.SitesList > .sites {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
}

.SitesList > .empty {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.SitesList > .empty p {
  font-size: 1em;
  margin: 0;
}

.site-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
  padding: var(--card-padding);
  cursor: pointer;
  transition: box-shadow 0.2s;
  display: flex;
  flex-direction: column;
  gap: var(--gap);
}
.site-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}
.site-title {
  font-size: 1.1em;
  font-weight: 700;
  color: #111827;
}
.site-desc {
  color: #64748b;
  font-size: 0.95em;
}
.site-url {
  color: #2563eb;
  font-size: 0.95em;
  word-break: break-all;
}

/* Site Info Column */
.SitesList .site-info-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.SitesList .site-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
}

.SitesList .site-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.SitesList .site-name {
  font-weight: 600;
  color: #111827;
}

.SitesList .site-id {
  font-size: 0.875em;
  color: #6b7280;
  font-weight: 500;
}

/* Site Description Column */
.SitesList .site-description {
  color: #6b7280;
  max-width: 300px;
  line-height: 1.4;
}

/* Site URL Column */
.SitesList .site-url {
  color: #2563eb;
  text-decoration: none;
  word-break: break-all;
}

.SitesList .site-url:hover {
  text-decoration: underline;
}

/* Site Owner Column */
.SitesList .site-owner {
  color: #6b7280;
  font-weight: 500;
}

/* Site Time Column */
.SitesList .site-time {
  color: #9ca3af;
  white-space: nowrap;
}

/* Site Actions Column */
.SitesList .action-buttons {
  display: inline-flex;
  gap: 1ch;
}

.SitesList .action-link {
  display: flex;
  align-items: center;
  text-decoration: none;
}
