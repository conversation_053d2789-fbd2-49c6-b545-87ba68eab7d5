import { useLocation, useNavigate } from "react-router-dom";
import { useSlot } from "@hll/use-slot";
import { useEffect, useMemo } from "react";
import { mySitesStore, getSiteStore, useAllSiteConfigs, useTagStore, favoriteSitesStore } from "@hll/quantum-store";
import { Tip } from "../../../components/Tip";
import { Button } from "../../../components/Button";
import { PlusIcon } from "../../../icons/PlusIcon";
import { EditIcon } from "../../../icons/EditIcon";
import { showError } from "../../../components/Notification";
import { navigationSections } from "../../../components/ManagerMenu";
import { nextId } from "../../../services/agentIdManager";
import { DataTable, type Column } from "../../../components/DataTable";
import { Link } from "react-router-dom";
import { Tooltip } from "../../../components/Tooltip";
import { getErrorMessage } from "../../../utils/getErrorMessage";
import { FeishuAvatar } from "../../../components/FeishuAvatar";
import { useEditable } from "../../../utils/useEditable";
import { StarIcon } from "../../../icons/StarIcon";
import { ViewIcon } from "../../../icons/ViewIcon";

import "./index.css";

const createSite = async () => {
  const siteId = await nextId();
  const store = getSiteStore(siteId);
  await store.upload(
    {
      url: "https://example.com",
      title: "新建网址导航",
      description: "这是一个网址导航范例，你可以定制你自己的网址导航",
      icon: "",
    },
    "null"
  );
  await mySitesStore.add(siteId);
  return siteId;
};

export default function SitesList() {
  const { pathname } = useLocation();
  const navigate = useNavigate();
  const { label } = (navigationSections.sites.items.find((item: { path: string }) => item.path === pathname) || {}) as {
    label?: string;
  };

  useSlot("MainHeader-title", () => label, [label]);
  useSlot(
    "MainHeader-right",
    () => (
      <Button
        variant="primary"
        icon={<PlusIcon />}
        onClick={async () => {
          try {
            const siteId = await createSite();
            navigate(`/sites/${siteId}`);
          } catch (error) {
            showError(error);
          }
        }}
      >
        创建网址导航
      </Button>
    ),
    []
  );

  const { store } = navigationSections.sites.items.find((item: { path: string }) => item.path === pathname) || {};

  const { data: siteConfigs = [], loading, error } = useAllSiteConfigs(store);

  const columns: Column<(typeof siteConfigs)[0]>[] = useMemo(
    () => [
      {
        key: "site-info",
        title: "网址导航",
        render: (_, site) => {
          const { data, id } = site;
          const { title, icon } = data;
          return (
            <div className="site-info-content">
              {icon && <img className="site-icon" src={icon} alt={title || id} />}
              <div className="site-details">
                <div className="site-name">{title || id}</div>
                <div className="site-id">ID: {id}</div>
              </div>
            </div>
          );
        },
      },
      {
        key: "description",
        title: "描述",
        render: (_, site) => {
          const { data } = site;
          return <span className="site-description">{data.description || "暂无描述"}</span>;
        },
      },
      {
        key: "url",
        title: "网址",
        render: (_, site) => {
          const { data } = site;
          return (
            <a href={data.url} target="_blank" rel="noopener noreferrer" className="site-url">
              {data.url}
            </a>
          );
        },
      },
      {
        key: "owner",
        title: "所有者",
        render: (_, site) => {
          return <FeishuAvatar uniqId={site.owner} />;
        },
      },
      {
        key: "timestamp",
        title: "创建时间",
        render: (_, site) => {
          return <span className="site-time">{site.timestamp ? site.timestamp.toLocaleString("zh-CN") : "-"}</span>;
        },
      },
      {
        key: "actions",
        title: "操作",
        align: "right",
        render: (_, site) => {
          return <Actions site={site} />;
        },
      },
    ],
    [store]
  );

  const content = useMemo(() => {
    if (loading) return <Tip type="loading">加载中...</Tip>;
    if (error) return <Tip type="error">{getErrorMessage(error)}</Tip>;
    return <DataTable data={siteConfigs} columns={columns} emptyText="暂无网址导航" className="sites-table" />;
  }, [siteConfigs, columns, loading, error]);

  return <div className={`SitesList ${loading ? "loading" : ""}`}>{content}</div>;
}

const Actions = ({ site }: { site: { id: string; owner: string; head: string } }) => {
  const { data: editable, loading: editableLoading, error: editableError } = useEditable(site.owner, site.head);
  const {
    data: favoriteSites,
    loading: favoriteSitesLoading,
    error: favoriteSitesError,
  } = useTagStore(favoriteSitesStore);
  const error = editableError || favoriteSitesError;

  useEffect(() => {
    if (error) {
      console.error(error);
    }
  }, [error]);

  const isFavorate = favoriteSites?.includes(site.id);
  return (
    <div className="action-buttons">
      {favoriteSitesLoading ? (
        <Tip type="loading" />
      ) : (
        <Button
          title={isFavorate ? "取消关注" : "添加关注"}
          icon={<StarIcon style={{ fill: isFavorate ? "#f59e0b" : "#ccc" }} />}
          onClick={async () => {
            if (isFavorate) {
              await favoriteSitesStore.remove(site.id);
            } else {
              await favoriteSitesStore.add(site.id);
            }
            favoriteSitesStore.getWithoutCache();
          }}
          size="small"
        />
      )}
      {editable !== null ? (
        <Tooltip content={editable ? "编辑" : "查看"}>
          <Link className="action-link" to={`/sites/${site.id}`}>
            <Button
              icon={editable ? <EditIcon /> : <ViewIcon />}
              variant={editable ? "primary" : "default"}
              size="small"
            />
          </Link>
        </Tooltip>
      ) : editableLoading ? (
        <Tip type="loading" />
      ) : null}
    </div>
  );
};
