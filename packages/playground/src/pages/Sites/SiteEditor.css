.SiteEditor {
  background: white;
  border-radius: 0;
  box-shadow: none;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.SiteEditor .EditorContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  padding: 0;
  transition: opacity 0.2s ease;
}

.SiteEditor .EditorContainer.loading {
  opacity: 0.2;
  pointer-events: none;
}

.SiteEditor .config-content {
  color: #475569;
  line-height: 1.6;
}

.SiteEditor .config-content p {
  margin: 0;
  font-size: 0.9rem;
  color: #64748b;
}

.SiteEditor .select-site-tip {
  margin: 40px auto;
  padding: 24px 32px;
  font-size: 1.1em;
  text-align: center;
  max-width: 320px;
}

.SiteEditor .editor-actions {
  margin-bottom: 16px;
  display: flex;
  gap: 8px;
}

.SiteEditor textarea {
  width: 100%;
  min-height: 300px;
  font-family: monospace;
  font-size: 14px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 12px;
  box-sizing: border-box;
  background: #f8fafc;
  resize: vertical;
}
