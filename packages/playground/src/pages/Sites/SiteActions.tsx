import { useEffect, useMemo } from "react";
import { Button } from "../../components/Button";
import { Dropdown } from "../../components/Dropdown";
import { Tip } from "../../components/Tip";
import { MoreIcon } from "../../icons/MoreIcon";
import { DeleteIcon } from "../../icons/DeleteIcon";
import { VisibilityIcon } from "../../icons/VisibilityIcon";
import { VisibilityOffIcon } from "../../icons/VisibilityOffIcon";
import { CopyIcon } from "../../icons/CopyIcon";
import { SaveIcon } from "../../icons/SaveIcon";
import { getSiteStore, mySitesStore, publicSitesStore, useTagStore } from "@hll/quantum-store";
import { useNavigate } from "react-router-dom";
import { showSuccess, showError } from "../../components/Notification";
import { useUserInfo } from "../../services/user";

interface SiteActionsProps {
  siteId: string;
  jsonValue: string;
}

const togglePrivateOrPublic = async (siteId: string, isPublic: boolean) => {
  if (isPublic == null) return;
  try {
    if (isPublic) {
      await publicSitesStore.remove(siteId);
    } else {
      await publicSitesStore.add(siteId);
    }
    showSuccess(
      <>
        设置成功，当前状态为 <strong>{!isPublic ? "公开" : "私有"}</strong>
      </>
    );
  } catch (e) {
    showError(e, "设置失败");
  }
};

const handleSave = async (siteId: string, jsonValue: string) => {
  if (!siteId) return;
  const store = getSiteStore(siteId);
  try {
    await store.upload(JSON.parse(jsonValue), store.head);
    store.get();
    showSuccess("保存成功");
  } catch (e) {
    showError(e, "保存失败");
  }
};

const handleDelete = async (siteId: string, navigate: ReturnType<typeof useNavigate>) => {
  if (!siteId || !window.confirm("确定要删除这个网址导航吗？")) return;
  const store = getSiteStore(siteId);
  try {
    await store.delete(store.lastResult?.head || "null");
    await Promise.all([mySitesStore.remove(siteId), publicSitesStore.remove(siteId)]);
    showSuccess("删除成功");
    navigate("/sites/mine");
  } catch (e) {
    showError(e, "删除失败");
  }
};

const handleCopyConfigLink = async (siteId: string) => {
  const store = getSiteStore(siteId);
  const configLink = `${store.base}`;
  try {
    await navigator.clipboard.writeText(configLink);
    showSuccess("配置链接已复制到剪贴板");
  } catch (e) {
    showError(e, "复制失败");
  }
};

const SiteActions = ({ siteId, jsonValue }: SiteActionsProps) => {
  const { data: userInfo, loading: userInfoLoading, error: userInfoError } = useUserInfo();
  const pa = useTagStore(publicSitesStore);
  const navigate = useNavigate();
  const store = getSiteStore(siteId);
  const { data: siteData, loading: siteDataLoading, error: siteDataError, owner } = useTagStore(store);

  const loading = userInfoLoading || siteDataLoading;
  const error = userInfoError || siteDataError;
  const isPublic = pa.loading || pa.error ? null : (pa.data || []).includes(siteId);
  const isOwner = userInfo ? owner === userInfo.uniqId : null;

  const hasChanges = useMemo(() => {
    if (!siteData) return false;
    try {
      return JSON.stringify(siteData, null, 2) !== jsonValue;
    } catch {
      return false;
    }
  }, [siteData, jsonValue]);

  useEffect(() => {
    if (error) {
      showError(error);
    }
  }, [error]);

  useEffect(() => {
    if (!hasChanges) return;
    const handler = (e: BeforeUnloadEvent) => e.preventDefault();
    window.addEventListener("beforeunload", handler);
    return () => window.removeEventListener("beforeunload", handler);
  }, [hasChanges]);

  const dropdownItems = [
    {
      key: "copyConfigLink",
      label: "复制配置链接",
      icon: <CopyIcon />,
      onClick: () => handleCopyConfigLink(siteId),
    },
    isPublic != null && {
      key: "togglePrivateOrPublic",
      label: <>{isPublic ? "设为私有" : "设为公开"}</>,
      icon: isPublic ? <VisibilityOffIcon /> : <VisibilityIcon />,
      onClick: () => togglePrivateOrPublic(siteId, isPublic),
    },
    {
      key: "delete",
      label: "删除",
      icon: <DeleteIcon />,
      danger: true,
      onClick: () => handleDelete(siteId, navigate),
    },
  ].filter((i) => i !== false);

  if (loading) {
    return <Tip type="loading" />;
  }

  if (isOwner !== true) {
    return null;
  }

  return (
    <>
      {hasChanges ? null : <Tip type="weak">当前无变更</Tip>}
      <Button variant="primary" onClick={() => handleSave(siteId, jsonValue)} icon={<SaveIcon />}>
        保存
      </Button>
      <Dropdown trigger={<MoreIcon />} items={dropdownItems} placement="bottom" />
    </>
  );
};

export default SiteActions;
