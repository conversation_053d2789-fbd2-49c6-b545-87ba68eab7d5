import { ManagerMenu } from "../../components/ManagerMenu";
import { Outlet } from "react-router-dom";
import { MainHeader } from "../../components/MainHeader";
import "./index.css";

export default function Sites() {
  return (
    <div className="SitesPage" style={{ display: "flex" }}>
      <ManagerMenu />
      <div style={{ flex: 1, display: "flex", flexDirection: "column" }}>
        <MainHeader />
        <Outlet />
      </div>
    </div>
  );
} 