import { use<PERSON>arams } from "react-router-dom";
import { MainHeader } from "../../components/MainHeader";
import { ManagerMenu } from "../../components/ManagerMenu";
import { getAgentStore } from "@hll/quantum-store";
import { useCallback, useEffect, useState } from "react";
import "./index.css";
import { Button } from "../../components/Button";
import { PersonIcon } from "../../icons/PersonIcon";
import { MemberGroupCard } from "./MemberGroupCard";
import { getUserInfo, searchUser } from "../../services/user";
import { Dialog } from "../../components/Dialog";
import Select from "../../components/Select";
import { standardDebounce } from "../../utils/debounce";
import { showError, showSuccess } from "../../components/Notification";
import { useSlot } from "@hll/use-slot";

export default function Members() {
  const [memberList, setMemberList] = useState<string[]>([]);
  const [ownerList, setOwnerList] = useState<string[]>([]);
  const [searchUserList, setSearchUserList] = useState<{ label: string; value: string }[]>([]);
  const [open, setOpen] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const [selectLoading, setSelectLoading] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [currentUser, setCurrentUser] = useState('');
  const { agentId = "" } = useParams();

  const agent = getAgentStore(agentId);

  useSlot(
      "MainHeader-title",
      () => (
        <div style={{ display: "flex", alignItems: "center", gap: "var(--gap)" }}>
          <span>成员管理</span>
        </div>
      ),
      []
    );

  useEffect(() => {
    getUserInfo().then(res => {
      setCurrentUser(res.uniqId)
    })
  }, [])

  useEffect(() => {
    agent.getAuthorizedUsers().then((userList) => {
      setMemberList(userList);
    });
    agent.getCommit().then((user) => {
      setOwnerList([user.owner]);
    });
  }, [agent]);

  const updateMembers = () => {
    if (!searchValue) {
      showError("请选择要添加的成员");
      return;
    }
    if (!ownerList.includes(currentUser)) {
      return showError('当前无权限操作')
    }
    setSearchValue("");
    setSearchUserList([])
    setSubmitLoading(true);
    agent.modifyAuthorizedUsers([...memberList, searchValue]).then(() => {
      agent.getAuthorizedUsers().then((userList) => {
        setMemberList(userList);
        setOpen(false);
        setSubmitLoading(false);
        showSuccess('添加成功')
      });
    });
  };

  const handleDeleteMember = (memberId: string) => {
    if (!ownerList.includes(currentUser)) {
      return showError('当前无权限操作')
    }
    const updatedMembers = memberList.filter(id => id !== memberId);
    agent.updateAuthorizedUsers(updatedMembers).then(() => {
      setMemberList(updatedMembers);
      showSuccess('删除成功')
    });
  };

  const searchUserApi = (keyword: string) => {
    searchUser(keyword).then((userList) => {
      const list = userList.map((u) => ({
        label: `${u.userName}(${u.userUniqId})`,
        value: u.userUniqId,
      }));
      setSearchUserList(list);
      setSelectLoading(false);
    });
  };
  const handleAddPerson = () => {
    setOpen(true);
  };

  const debouncedSearch = useCallback(
    standardDebounce((keyword: string) => {
      searchUserApi(keyword);
    }, 500),
    []
  )

  return (
    <div className="MembersPage" style={{ display: "flex" }}>
      <ManagerMenu />
      <div style={{ flex: 1, display: "flex", flexDirection: "column" }}>
        <MainHeader />
        <div className="MembersPage-content">
          
          <div>
            <div className="header">成员设置</div>
            <Button icon={<PersonIcon />} variant="icon" onClick={handleAddPerson}></Button>
            <Dialog
              width={450}
              mask={false}
              open={open}
              onClose={() => setOpen(false)}
              title="添加成员"
              onOk={updateMembers}
              onCancel={() => setOpen(false)}
              okText="确定"
              cancelText="取消"
              confirmLoading={submitLoading}
            >
              <div style={{ display: "flex", alignItems: "center", gap: 8, marginLeft: 10 }}>
                <label style={{ minWidth: "40px", fontSize: "14px" }}>用户：</label>
                <Select
                  placeholder="搜索并选择一个用户"
                  showSearch
                  onSearch={(keyword) => {
                    if (keyword.trim()) {
                      setSelectLoading(true);
                      debouncedSearch(keyword);
                    }
                  }}
                  options={searchUserList}
                  onChange={(value) => {
                    setSearchValue(value as string);
                  }}
                  value={searchValue}
                  loading={selectLoading}
                />
              </div>
            </Dialog>
          </div>
          <div className="Members">
            <MemberGroupCard title="Owner" members={ownerList} />
            <MemberGroupCard
              title="成员"
              members={memberList}
              deletable
              onDelete={handleDeleteMember}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
