import React, { useState } from "react";
import { FeishuAvatar } from "../../../components/FeishuAvatar";
import { Dialog } from "../../../components/Dialog";
import "./index.css";
import { Button } from "../../../components/Button";
import { DeleteIcon } from "../../../icons/DeleteIcon";

export interface MemberGroupCardProps {
  title: string;
  members: string[];
  deletable?: boolean;
  onDelete?: (memberId: string) => void;
}

export const MemberGroupCard: React.FC<MemberGroupCardProps> = ({
  title,
  members,
  deletable = false,
  onDelete,
}) => {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deleteId, setDeleteId] = useState('');
  const openDialog = (id: string) => {
    setShowDeleteDialog(true);
    setDeleteId(id)
  };

  const handleConfirmDelete = () => {
    onDelete?.(deleteId);
    setShowDeleteDialog(false);
  };

  const handleCancelDelete = () => {
    setShowDeleteDialog(false);
  };

  return (
    <>
      <section className="MemberGroupCard">
        <header>
          <span className="GroupTitle">{title}</span>
          <span className="GroupCount">{members.length}</span>
        </header>
        <ul>
          {members.map((m) => (
            <li className="MemberCard" key={m}>
              {deletable && (
                <Button className="DeleteButton" icon={<DeleteIcon />} variant="icon" size="small" onClick={() => openDialog(m)} />
              )}
              <FeishuAvatar uniqId={m} showId />
            </li>
          ))}
        </ul>
      </section>

      <Dialog
        open={showDeleteDialog}
        onClose={handleCancelDelete}
        title="确认删除"
        onOk={handleConfirmDelete}
        onCancel={handleCancelDelete}
        okText="删除"
        cancelText="取消"
        width={400}
      >
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: 10
        }}><span>确定要删除</span> <FeishuAvatar uniqId={deleteId} showId /> <span>吗？</span></div>
      </Dialog>
    </>
  );
};
