import { <PERSON>rowser<PERSON><PERSON>er, Navigate, Route, Routes } from "react-router-dom";
import { NotificationContainer } from "./components/Notification";

import { Chat } from "./pages/Chat";
import { AgentE<PERSON>or, AgentList, Agents } from "./pages/Agents";
import { PageNotFound } from "./pages/PageNotFound";
import { navigationSections } from "./components/ManagerMenu";
import Sites from "./pages/Sites";
import SitesList from "./pages/Sites/SitesList";
import SiteEditor from "./pages/Sites/SiteEditor";
import Members from "./pages/Members";

// import { TagStore } from "@hll/quantum-store";
// TagStore.base = "https://8080.huolala.work";

function App() {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/">
          <Route path="" element={<Navigate to="/chat" />} />
          <Route path="chat" element={<Chat />} />
          <Route path="agents" element={<Agents />}>
            {navigationSections.agents.items.map((item) => (
              <Route key={item.path} path={item.path} element={<AgentList />} />
            ))}
            <Route path="" element={<Navigate to="/agents/mine" />} />
            <Route path=":agentId" element={<AgentEditor />} />
          </Route>
          <Route path="sites" element={<Sites />}>
            {navigationSections.sites.items.map((item) => (
              <Route key={item.path} path={item.path.replace("/sites/", "")} element={<SitesList />} />
            ))}
            <Route path="" element={<Navigate to="/sites/mine" />} />
            <Route path=":siteId" element={<SiteEditor />} />
          </Route>
          <Route path=":agentId/members" element={<Members />} />
          <Route path="*" element={<PageNotFound />} />
        </Route>
      </Routes>
      <NotificationContainer />
    </BrowserRouter>
  );
}

export default App;
