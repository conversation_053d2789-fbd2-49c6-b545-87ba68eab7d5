import type { QuantumAgentConfig } from "@hll/quantum-store";

const toPromise = <T>(req: IDBRequest<T>) => {
  return new Promise<T>((resolve, reject) => {
    req.addEventListener("success", () => {
      resolve(req.result);
    });
    req.addEventListener("error", () => {
      reject(req.error);
    });
  });
};

const initIdb = () => {
  const req = indexedDB.open("section-parser", 3);
  req.addEventListener("upgradeneeded", () => {
    const { result } = req;
    for (const name of result.objectStoreNames) {
      result.deleteObjectStore(name);
    }
    const os = result.createObjectStore("chat-history", { keyPath: "chatId" });
    os.createIndex("timestamp", "timestamp", { unique: false });
    os.createIndex("state-timestamp", ["state", "timestamp"], { unique: false });
  });
  return toPromise(req);
};

const db = initIdb();

const getStore = async (mode: "readwrite" | "readonly") => {
  const tx = (await db).transaction("chat-history", mode);
  return tx.objectStore("chat-history");
};

type GetKeys<T> = T extends object ? keyof T : never;

export type UncheckedAgentConfig = Partial<Record<GetKeys<QuantumAgentConfig>, unknown>>;

export interface ChatHistoryRecord {
  chatId: string;
  aiType: string;
  options: object;
  agentConfig: UncheckedAgentConfig;
  timestamp: Date;
  state: number;
}

export const chatHistoryStore = {
  async get(chatId: string): Promise<ChatHistoryRecord | null> {
    const store = await getStore("readonly");
    return toPromise(store.get(chatId));
  },

  async put(record: ChatHistoryRecord) {
    const store = await getStore("readwrite");
    await toPromise(store.put(record));
    this.eventBus.dispatchEvent(new CustomEvent("put"));
  },

  async delete(chatId: string) {
    const store = await getStore("readwrite");
    await toPromise(store.delete(chatId));
    this.eventBus.dispatchEvent(new CustomEvent("delete"));
  },

  async list() {
    const store = await getStore("readonly");
    const index = store.index("timestamp");
    return toPromise(index.getAll());
  },

  async listAllActiveChats() {
    const store = await getStore("readonly");
    const index = store.index("state-timestamp");
    return toPromise(index.getAll(IDBKeyRange.bound([1, new Date(0)], [1, new Date()])));
  },

  async removeAllInactiveChatsLeft24Hours() {
    const store = await getStore("readwrite");
    const index = store.index("state-timestamp");
    const records = await toPromise(
      index.getAll(IDBKeyRange.bound([0, new Date(0)], [0, new Date(Date.now() - 864e5)]))
    );
    await Promise.all(records.map((record) => toPromise(store.delete(record.chatId))));
  },

  eventBus: new EventTarget(),
};

chatHistoryStore.removeAllInactiveChatsLeft24Hours();
