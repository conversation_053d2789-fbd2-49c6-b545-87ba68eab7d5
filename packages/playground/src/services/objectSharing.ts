import { TagStore } from "@hll/quantum-store";
import { rejectNon200 } from "../utils/rejectNon200";
import type { UncheckedAgentConfig } from "./chatHistoryStore";

const iv = new TextEncoder().encode("@hll/section-parser");

const api = `${TagStore.base}/objects`;

const flip = (buf: Uint8Array<ArrayBufferLike>) => {
  for (let i = 0; i < buf.length; i++) buf[i] = buf[i] ^ iv[i % iv.length];
};

export interface SharingObject {
  aiType: string;
  options: object;
  agentConfig: UncheckedAgentConfig;
}

export const uploadObject = async (data: SharingObject) => {
  const buf = new TextEncoder().encode(JSON.stringify(data));
  flip(buf);
  const hash = await fetch(api, {
    body: buf,
    method: "POST",
    headers: { "Content-Type": "application/octet-stream" },
    credentials: "include",
  })
    .then(rejectNon200)
    .then((res) => res.text());
  return hash;
};

export const downloadObject = async (hash: string) => {
  const buffer = await fetch(`${api}/${hash}`, {
    method: "GET",
    credentials: "include",
  })
    .then(rejectNon200)
    .then((res) => res.arrayBuffer());
  const bytes = new Uint8Array(buffer);
  flip(bytes);
  const obj = JSON.parse(new TextDecoder().decode(bytes));
  return Object(obj) as Partial<SharingObject>;
};
