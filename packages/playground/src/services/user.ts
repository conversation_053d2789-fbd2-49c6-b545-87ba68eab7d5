import { useEffect, useState } from "react";
import { rejectNon200 } from "../utils/rejectNon200";

const url = "https://gateway-office-public.huolala.work/fe-van-api-svc/api/user?info=me";
const feishuUserInfoUrl = "https://gateway-office-public.huolala.work/fe-van-api-svc/api/feishu?info=user";
const searchUserUrl = "https://gateway-office-public.huolala.work/fe-van-api-svc/api/user?info=searchUser"


export const getHllIdentifier = () => {
  const raw = document.cookie.match(/(?:^|;\s*)_hll_identifier=(.*?)(?=;|$)/)?.[1];
  if (raw) return decodeURIComponent(raw);
  return "";
};

export interface UserInfo {
  createdAt: string;
  email: string;
  uniqId: string;
  updatedAt: string;
  name: string;
  id: number;
}

export interface OtherUserInfo {
  openId: string;
  uniqId: string;
  name: string;
  avatar72: string;
  avatar: {
    avatar72: string;
  };
}

export interface OtherUserInfo {
  openId: string;
  uniqId: string;
  name: string;
  avatar72: string;
  avatar: {
    avatar72: string;
  };
}

let userInfo: UserInfo | Promise<UserInfo> | null = null;

export const getUserInfo = async (): Promise<UserInfo> => {
  if (userInfo) return userInfo;
  userInfo = fetch(url, { credentials: "include" })
    .then(rejectNon200)
    .then((res) => res.json());
  userInfo.then(
    (data) => {
      userInfo = data;
    },
    () => {
      userInfo = null;
    }
  );
  return userInfo;
};

export const useUserInfo = () => {
  const cachedData = userInfo && !(userInfo instanceof Promise) ? userInfo : null;
  const [led, updateLed] = useState({ data: cachedData, loading: !cachedData, error: null });

  useEffect(() => {
    if (cachedData) return;
    updateLed({ data: null, loading: true, error: null });
    getUserInfo().then(
      (data) => {
        updateLed({ data, loading: false, error: null });
      },
      (error) => {
        updateLed({ data: null, loading: false, error });
      }
    );
  }, [cachedData, updateLed]);

  return led;
};

const userInfoPromiseCache: Record<string, Promise<OtherUserInfo | null>> = Object.create(null);

export const getFeishuUserInfoById = async <T extends readonly string[]>(uniqIds: T) => {
  const result: Record<string, OtherUserInfo | null> = {};
  const uncachedIds = uniqIds.filter((id) => !userInfoPromiseCache[id]);
  if (uncachedIds.length > 0) {
    const body = new FormData();
    uncachedIds.forEach((id) => body.append("uniqIds", id));
    const fetchPromise = fetch(feishuUserInfoUrl, { method: "POST", body, credentials: "include" })
      .then(rejectNon200)
      .then((res) => res.json() as Promise<{ [key: string]: OtherUserInfo }>);
    uncachedIds.forEach((id) => {
      userInfoPromiseCache[id] = fetchPromise
        .then((info) => info?.[id] || null)
        .catch(() => {
          delete userInfoPromiseCache[id];
          return null;
        });
    });
  }
  const allPromises = uniqIds.map(async (id) => {
    try {
      const user = await userInfoPromiseCache[id];
      result[id] = user || null;
    } catch {
      result[id] = null;
    }
  });
  await Promise.all(allPromises);
  return result as { [K in T[number]]: OtherUserInfo | null };
};

export const searchUser = (keyword: string) => {
  return fetch(`${searchUserUrl}&keyword=${keyword}`, { credentials: "include" })
    .then(rejectNon200)
    .then((res) => res.json() as Promise<{ userName: string, userUniqId: string }[]>);
}