import { useMemo } from "react";
import { useUserInfo } from "../services/user";
import { getAuthorizedUsersFromCommit, TagStore } from "@hll/quantum-store";
import { useRequest } from "./useRequest";

export const useEditable = (owner: string | undefined, head: string | undefined | null) => {
  const { data: userInfo, loading: userInfoLoading, error: userInfoError } = useUserInfo();

  const {
    data: authorizedUsers,
    loading: authorizedUsersLoading,
    error: authorizedUsersError,
  } = useRequest(
    async () => {
      if (!head) return null;
      const commit = await TagStore.getCommitRaw(head);
      return getAuthorizedUsersFromCommit(commit);
    },
    { refreshDeps: [head] }
  );

  const data = useMemo(() => {
    if (!userInfo) return null;
    const user = userInfo.uniqId;
    if (owner === user) return true;
    if (!authorizedUsers) return null;
    return authorizedUsers.includes(user);
  }, [userInfo, owner, authorizedUsers]);

  return {
    data,
    loading: userInfoLoading || authorizedUsersLoading,
    error: userInfoError || authorizedUsersError,
  };
};
