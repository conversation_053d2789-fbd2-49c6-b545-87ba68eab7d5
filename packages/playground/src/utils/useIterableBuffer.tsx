import { type IterableBuffer, BufferState } from "@hll/section-parser";
import { useState, useEffect } from "react";
import { debounce } from "./debounce";

export const useIterableBuffer = (value: IterableBuffer<string>) => {
  const [data, updateData] = useState("");

  useEffect(() => {
    const controller = {
      enqueue: debounce(() => {
        updateData(value.buffer.join(""));
      }),
    };
    value.tee(controller);
    return () => value.untee(controller);
  }, [value]);

  return {
    data,
    loading: value.state === BufferState.Open,
    error: value.state === BufferState.Error ? value.reason : null,
  };
};
