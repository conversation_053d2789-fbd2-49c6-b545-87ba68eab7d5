import { useState, useEffect, useRef, useCallback } from "react";

export const useRequest = <T extends unknown>(
  factory: () => Promise<T>,
  { refreshDeps = [] }: { refreshDeps?: unknown[] } = {}
) => {
  const [data, setData] = useState<T | undefined>(undefined);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<unknown>(undefined);

  const factoryRef = useRef(factory);
  factoryRef.current = factory;

  const refresh = useCallback(() => {
    setLoading(true);
    factoryRef
      .current()
      .then(setData)
      .catch(setError)
      .finally(() => setLoading(false));
  }, []);

  useEffect(() => {
    refresh();
  }, [...refreshDeps]);

  return { data, loading, error, refresh };
};
