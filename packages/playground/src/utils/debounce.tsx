export const debounce = (fn: () => void) => {
  let timer: number | null = null;
  let count = 0;
  return () => {
    if (timer === null) {
      timer = window.setTimeout(() => {
        if (count > 0) fn();
        timer = null;
      });
      count = 0;
      fn();
    } else {
      count++;
    }
  };
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const standardDebounce = <T extends (...args: any[]) => void>(fn: T, delay: number = 0) => {
  let timer: number | null = null;
  return (...args: Parameters<T>) => {
    if (timer !== null) {
      clearTimeout(timer);
    }
    timer = window.setTimeout(() => {
      fn(...args);
      timer = null;
    }, delay);
  };
};
