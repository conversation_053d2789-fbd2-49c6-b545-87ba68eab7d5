export class Bumper {
  private static readonly MIN_LENGTH: number = 4;
  private readonly keys: bigint[] | BigUint64Array;

  constructor(keys: bigint[] | BigUint64Array) {
    this.keys = keys;
  }

  private static getBits(value: bigint): number {
    if (value === 0n) return 0;
    let bits = 0n;
    while (value > 0n) {
      value >>= 1n;
      bits++;
    }
    return Math.ceil(Number(bits) / 4);
  }

  public generateChecksum(value: bigint): number {
    const mask32 = (1n << 32n) - 1n;
    value = ((value & mask32) ^ (value >> 32n)) & mask32;
    return Number(((value * 181n + 239n) % 240n) + 16n);
  }

  public feistelRound(right: bigint, key: bigint): bigint {
    let temp = right ^ key;
    temp = temp ^ (temp >> 3n) ^ (temp << 5n);
    return temp;
  }

  public feistel(value: bigint, length: number, reversed: boolean): bigint {
    const halfLength = Math.floor(length / 2);
    const mask = (1n << BigInt(halfLength)) - 1n;
    let left = value >> BigInt(halfLength);
    let right = value & mask;

    for (let i = 0; i < this.keys.length; i++) {
      const keyIndex = reversed ? this.keys.length - 1 - i : i;
      const key = this.keys[keyIndex] & mask;
      const temp = left;
      left = right;
      right = temp ^ (this.feistelRound(right, key) & mask);
    }

    return (right << BigInt(halfLength)) | left;
  }

  public encrypt(value: bigint): string {
    const length = Math.max(Bumper.getBits(value) * 4, Bumper.MIN_LENGTH);
    const encrypted = this.feistel(value, length, false);
    const checksum = this.generateChecksum(value);
    const hex = checksum.toString(16).padStart(2, "0");
    const encryptedHex = encrypted.toString(16).padStart(Math.ceil(length / 4), "0");
    return hex + encryptedHex;
  }

  public decrypt(hexValue: string): bigint {
    const length = hexValue.length * 4 - 8;
    if (length < Bumper.MIN_LENGTH || length > 64) {
      throw new Error(`Invalid value '${hexValue}'`);
    }

    const checksum = parseInt(hexValue.substring(0, 2), 16);
    const encrypted = BigInt("0x" + hexValue.substring(2));
    const value = this.feistel(encrypted, length, true);

    if (this.generateChecksum(value) !== checksum) {
      throw new Error(`Invalid value '${hexValue}'`);
    }

    return value;
  }
}
