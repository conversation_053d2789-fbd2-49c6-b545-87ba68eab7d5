import { SvgIcon, type SvgIconProps } from "./SvgIcon";

export const LoadingIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon viewBox="0 0 24 24" {...props}>
      <circle
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="2"
        fill="none"
        strokeDasharray="62.832" // 10 * 2 * π
        strokeDashoffset="15.708" // 10 * 2 * π * 0.25
        strokeLinecap="round"
      >
        <animateTransform
          attributeName="transform"
          type="rotate"
          from="0 12 12"
          to="360 12 12"
          dur="0.8s"
          repeatCount="indefinite"
        />
      </circle>
    </SvgIcon>
  );
};
