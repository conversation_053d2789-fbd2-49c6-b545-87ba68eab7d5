:root {
  font: 16px/1.5 system-ui, Avenir, Helvetica, Arial, sans-serif;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  --gap: 10px;

  --primary: #3b82f6;
  --primary-hover: #2563eb;
  --primary-active: #1d4ed8;

  --header-height: 64px;
  --aside-width: 240px;

  --card-padding: calc(var(--gap) * 1.6) calc(var(--gap) * 2);
}

body {
  margin: 0;
}

a {
  color: inherit;
  text-decoration: none;
  transition: all 0.2s ease;
}

a:hover {
  color: #2563eb;
}

* {
  box-sizing: border-box;
}

.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
