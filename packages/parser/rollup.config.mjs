import typescript from "@rollup/plugin-typescript";
import commonjs from "@rollup/plugin-commonjs";
import { nodeResolve } from "@rollup/plugin-node-resolve";
import terser from "@rollup/plugin-terser";

const commonPlugins = [
  nodeResolve({
    browser: true,
    preferBuiltins: false,
  }),
  commonjs(),
  typescript({
    tsconfig: "./tsconfig.json",
    sourceMap: true,
    inlineSources: true,
    exclude: ["**/__tests__/**", "**/*.test.ts"],
  }),
];

const productionPlugins = [
  terser({
    format: {
      comments: false,
    },
    compress: {
      drop_console: true,
      drop_debugger: true,
    },
  }),
];

const isProduction = process.env.NODE_ENV === "production";

const createConfig = (input, output, additionalPlugins = []) => ({
  input,
  output: {
    ...output,
    sourcemap: true,
    exports: "named",
  },
  plugins: [...commonPlugins, ...additionalPlugins, ...(isProduction ? productionPlugins : [])],
  external: [],
});

export default [
  // Main package build
  createConfig("src/index.ts", {
    file: "dist/index.js",
    format: "cjs",
  }),
  createConfig("src/index.ts", {
    file: "dist/index.esm.js",
    format: "es",
  }),
];
