# @hll/section-parser

A TypeScript library for parsing text streams into different types of sections.
This library provides real-time stream parsing capabilities for various AI chat APIs.

## Installation

Using npm:

```bash
npm install @hll/section-parser
```

Or using yarn:

```bash
yarn add @hll/section-parser
```

## Usage

### Main Interfaces

```typescript
export interface ChatMessage {
  readonly index: number;
  readonly role: ChatMessageRole;
  readonly sections: IterableBuffer<Section>;
}

export enum ChatMessageRole {
  User = "user",
  Assistant = "assistant",
}

export interface Chat<T extends Record<string, unknown>> {
  readonly url: string;
  readonly options: T;
  readonly messages: readonly ChatMessage[];
  readonly lastMessage: ChatMessage | null;
  readonly busy: boolean;
  readonly error: { reason: unknown } | null;

  next(content: string): void;
  appendContentMessage(role: ChatMessageRole, content: string): void;
  rewindToBefore(message: ChatMessage): void;
  abort(reason?: Error): void;
  watch(update: () => void): void;
  unwatch(update: () => void): void;
}

export type Section = SectionWithCall | SectionWithString;

interface SectionWithCall {
  type: "call";
  value: {
    name: IterableBuffer<string>;
    arguments: IterableBuffer<string>;
    result: IterableBuffer<string>;
  };
}

interface SectionWithString {
  type: "think" | "content";
  value: IterableBuffer<string>;
}

export interface ToolDeclaration<T extends string = string> {
  type: "function";
  function: {
    name: string;
    description: string;
    parameters: ToolDeclarationParameters<T>;
  };
}
```

### HaitunChat Example

Interact with the Tongyi Haitun model:

```typescript
import { HaitunChat } from "@hll/section-parser";

// Create a chat instance
const chat = new HaitunChat("https://api.example.com/v1/chat/completions", {
  model: "Qwen3-14B",
  temperature: 0,
  max_tokens: 20480,
  stream: true,
  messages: [],
});

// Register a tool
chat.registerTool(
  "execute",
  async (args) => {
    const { code } = args;
    return eval(code);
  },
  {
    description: "Execute JavaScript code",
    parameters: {
      type: "object",
      properties: {
        code: { type: "string", description: "Code to execute" },
      },
      required: ["code"],
    },
  }
);

// Send a message
chat.next("Hello, please help me calculate 1+1");

// Listen to message stream
for (const message of chat.messages) {
  console.log(`Role: ${message.role}, Index: ${message.index}`);
  for await (const section of message.sections) {
    if (section.type === "content") {
      console.log("Content:", await section.value.then((v) => v.join("")));
    } else if (section.type === "call") {
      console.log("Tool call:", {
        name: await section.value.name.then((v) => v.join("")),
        arguments: await section.value.arguments.then((v) => v.join("")),
        result: await section.value.result.then((v) => v.join("")),
      });
    } else if (section.type === "think") {
      console.log("Thinking:", await section.value.then((v) => v.join("")));
    }
  }
}

// Rewind or abort
if (chat.messages.length > 1) {
  chat.rewindToBefore(chat.messages[1]);
}
chat.abort();
```

### WukongChat Example

Interact with the Wukong model:

```typescript
import { WukongChat } from "@hll/section-parser";

const chat = new WukongChat("https://api.example.com/proxy/api/open/agent/v1", {
  agent_id: "your-agent-id",
  user_id: "your-user-id",
  business_alias: "your-business",
  project_alias: "your-project",
});

// Watch for updates
chat.watch(() => {
  console.log("Chat updated, messages count:", chat.messages.length);
});

// Send a message
chat.next("Please help me analyze this code");

// Listen to message stream
for (const message of chat.messages) {
  console.log(`Role: ${message.role}, Index: ${message.index}`);
  for await (const section of message.sections) {
    if (section.type === "content") {
      console.log("Content:", await section.value.then((v) => v.join("")));
    } else if (section.type === "think") {
      console.log("Thinking:", await section.value.then((v) => v.join("")));
    }
  }
}

// Rewind, abort, or stop watching
if (chat.messages.length > 1) {
  chat.rewindToBefore(chat.messages[1]);
}
chat.abort();
chat.unwatch(updateFunction);
```

### TradeChat Example

Interact with trading-related AI models:

```typescript
import { TradeChat } from "@hll/section-parser";

const chat = new TradeChat("https://api.example.com/trade/chat", {
  // Add your trading-specific options here
  // additional_messages: [] // Optional: pre-existing conversation history
});

// Watch for updates
chat.watch(() => {
  console.log("Trade chat updated, messages count:", chat.messages.length);
});

// Send a message
chat.next("What's the current market trend for tech stocks?");

// Listen to message stream
for (const message of chat.messages) {
  console.log(`Role: ${message.role}, Index: ${message.index}`);
  for await (const section of message.sections) {
    if (section.type === "content") {
      console.log("Content:", await section.value.then((v) => v.join("")));
    }
  }
}

// Check chat status
console.log("Chat busy:", chat.busy);
console.log("Chat error:", chat.error);

// Rewind or abort
if (chat.messages.length > 1) {
  chat.rewindToBefore(chat.messages[1]);
}
chat.abort();
```

### Message Section Types

- **content**: Regular assistant text content
- **call**: Tool calls (with name, arguments, result) - available in HaitunChat
- **think**: Reasoning or thought process - available in HaitunChat and WukongChat

### Chat Properties

All chat implementations provide these properties:

- `messages`: Array of chat messages
- `lastMessage`: The most recent message (or null)
- `busy`: Whether the chat is currently processing a request
- `error`: Any error that occurred during processing (or null)

### Error Handling

```typescript
try {
  chat.next("User input");
} catch (error) {
  console.error("Chat error:", error);
}

// Check if chat is in error state
if (chat.error) {
  console.error("Chat has error:", chat.error.reason);
}

// Abort with custom reason
chat.abort(new Error("User cancelled the request"));
```

### Working with Sections

```typescript
for await (const section of message.sections) {
  switch (section.type) {
    case "content":
      const content = await section.value.then((chunks) => chunks.join(""));
      console.log("Content:", content);
      break;
    case "call":
      const toolName = await section.value.name.then((chunks) => chunks.join(""));
      const toolArgs = await section.value.arguments.then((chunks) => chunks.join(""));
      const toolResult = await section.value.result.then((chunks) => chunks.join(""));
      console.log("Tool call:", { toolName, toolArgs, toolResult });
      break;
    case "think":
      const thinking = await section.value.then((chunks) => chunks.join(""));
      console.log("Thinking:", thinking);
      break;
  }
}
```

### State Management

```typescript
// Watch for state changes
const updateHandler = () => {
  console.log("Chat state changed:", {
    messageCount: chat.messages.length,
    isBusy: chat.busy,
    hasError: !!chat.error
  });
};

chat.watch(updateHandler);

// Remove watcher when done
chat.unwatch(updateHandler);
```

## Development

```bash
# Install dependencies
npm install

# Development mode with watch
npm run dev

# Build for production
npm run build

# Type checking
npm run type-check

# Run tests
npm test

# Run tests in watch mode
npm run test:watch

# Clean build artifacts
npm run clean
```

## Version

Current version: 1.2.5
