import { AssistantOutputItem } from "./AssistantOutputItem";
import { ReadingController, LiveBuffer } from "./IterableBuffer";
import { Choice } from "./Haitun/HaitunChunkExtractor";

export class ChoiceContentProcessor implements ReadingController<Choice> {
  private readonly receivler;
  private contentHasSent;
  private contentChunks: LiveBuffer<string> | null;
  private lastArgsChunks: LiveBuffer<string> | null;
  constructor(receivler: LiveBuffer<AssistantOutputItem>) {
    this.receivler = receivler;
    this.contentHasSent = false;
    this.contentChunks = null;
    this.lastArgsChunks = null;
  }
  enqueueContent(text: string) {
    if (!this.contentChunks) {
      this.contentChunks = new LiveBuffer<string>();
      this.contentHasSent = false;
      // If the message is before any call, send it immediately.
      if (!this.lastArgsChunks) {
        this.receivler.enqueue({ type: "content", value: this.contentChunks });
        this.contentHasSent = true;
      }
    }
    this.contentChunks.enqueue(text);
    if (this.contentHasSent === false && this.contentChunks.buffer.join("").trim() != "") {
      this.receivler.enqueue({ type: "content", value: this.contentChunks });
      this.contentHasSent = true;
    }
  }
  enqueueToolCallChunk(calls: unknown[]) {
    for (const fc of calls) {
      this.contentChunks?.close();
      this.contentChunks = null;
      const { id, function: funcInfo, index } = Object(fc) as Record<string, unknown>;
      if (!funcInfo) return;
      const { name, arguments: argsChunk } = (funcInfo || {}) as Record<string, unknown>;
      if (typeof name === "string" && name) {
        this.lastArgsChunks?.close();
        this.lastArgsChunks = new LiveBuffer<string>();
        const ltc = {
          id: typeof id === "string" ? id : undefined,
          index: typeof index === "number" ? index : undefined,
          type: "function" as const,
          function: { name: LiveBuffer.closure([name]), arguments: this.lastArgsChunks },
        };
        this.receivler.enqueue({ type: "call", value: ltc });
      }
      if (typeof argsChunk === "string" && argsChunk) {
        this.lastArgsChunks?.enqueue(argsChunk);
      }
    }
  }
  close() {
    this.contentChunks?.close();
    this.lastArgsChunks?.close();
    // Don't close the receiver, because the ChoiceContentProcessor is just one of a writers for receiver, it's not the owner of the receiver.
    // this.receivler.close();
  }
  error(reason: unknown) {
    this.contentChunks?.error(reason);
    this.lastArgsChunks?.error(reason);
    this.receivler.error(reason);
  }
}
