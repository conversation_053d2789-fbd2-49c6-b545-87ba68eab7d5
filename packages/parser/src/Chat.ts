import { IterableBuffer } from "./IterableBuffer";
import { ToolCallManagerOptions } from "./ToolCallManager";
import { DeepFreeze } from "./utils";

export interface Chat<T = object> {
  readonly url: string;
  readonly options: DeepFreeze<T>;

  /**
   * The messages of the chat.
   * NOTE: The messages are not low-level chatting history, just be used for UI display.
   *       If you want to get the low-level chatting history, you can use the `options` instead.
   */
  readonly messages: readonly ChatMessage[];

  /**
   * A shortcut to `messages[messages.length - 1] ?? null`.
   */
  readonly lastMessage: ChatMessage | null;

  /**
   * Whether the chat is busy.
   * This property is used for UI display, you can use it to disable the input or button.
   * NOTE: If the chat is busy, you can't send a new message.
   */
  readonly busy: boolean;

  /**
   * The error of the chat.
   * This property is used for UI display, you can use it to show the error message.
   * NOTE: If the chat is busy, you can't send a new message.
   *
   * @deprecated Don't care about the error state of the chat object.
   *             If you can handle the error, you can find it from item of sections.
   */
  readonly error: { reason: unknown } | null;

  /**
   * Append a user message to the chat and send a request.
   * NOTE: If a chat is busy or error, you can't send a new message.
   * NOTE: The last message must be a user message (can't send multiple messages continuously with same role).
   *
   * @param content - The content of the message.
   */
  next(content: string): void;

  /**
   * Request if needed.
   */
  requestIfNeeded(): Promise<void>;

  /**
   * Append a content message to the chat without sending a request.
   * NOTE: If a chat is busy or error, you can't append a content message.
   * NOTE: Can't send multiple messages continuously with same role.
   *
   * @param role - The role of the message.
   * @param content - The content of the message.
   */
  appendContentMessage(role: ChatMessageRole, content: string): void;

  /**
   * Rewind to the message before the given message.
   * @param message - The message to rewind to.
   */
  rewindToBefore(message: ChatMessage): void;

  /**
   * Abort the current processing message.
   * @param reason
   */
  abort(reason?: Error): void;

  /**
   * Watch the `messages` state change.
   * NOTE: This method ONLY watch the state change of messages.
   *       Such as a new message appended, or a message done, or a message error, etc.
   *       It does not watch the content change of messages as it is too frequent.
   *
   * @param handler - The function to call when the messages state are updated.
   */
  watch(handler: () => void): void;

  /**
   * Remove a watch handler.
   * @param handler - The function to unwatch.
   */
  unwatch(handler: () => void): void;

  /**
   * Register a native tool.
   * NOTE: Only tools in `additional_native_tools` can be registered.
   * @param declarationFunction - The declaration function of the tool.
   * @param implement - The implementation function of the tool.
   */
  registerNativeTool(
    declarationFunction: ToolDeclarationFunction<string>,
    implement: (args: Record<string, unknown>) => Promise<unknown>
  ): void;

  /**
   * Register environment variables.
   * @param variables - The environment variables to register.
   */
  registerEnvironmentVariables(variables: Record<string, string>): void;
}

export interface ChatMessage {
  readonly index: number;
  readonly role: ChatMessageRole;
  readonly sections: IterableBuffer<Section>;
}

export enum ChatMessageRole {
  User = "user",
  Assistant = "assistant",
  System = "system",
}

export interface SectionWithCall {
  readonly type: "call";
  readonly value: {
    readonly name: IterableBuffer<string>;
    readonly arguments: IterableBuffer<string>;
    readonly result: IterableBuffer<string>;
  };
}

export interface SectionWithString {
  readonly type: "think" | "content";
  readonly value: IterableBuffer<string>;
}

export type Section = SectionWithCall | SectionWithString;

export interface JsonSchemaPrimitive {
  readonly type: "string" | "number" | "boolean";
  readonly description: string;
}

export interface JsonSchemaObject<K extends string = string, V = JsonSchemaValue> {
  readonly type: "object";
  readonly description?: string;
  readonly properties?: Record<K, V | undefined>;
  readonly required?: readonly string[];
  readonly additionalProperties?: JsonSchemaValue;
  readonly patternProperties?: Record<string, JsonSchemaValue>;
}

export interface JsonSchemaArray<T extends JsonSchemaValue = JsonSchemaValue> {
  readonly type: "array";
  readonly items: T;
  readonly description?: string;
  readonly minItems?: number;
  readonly maxItems?: number;
}

export type JsonSchemaValue = JsonSchemaPrimitive | JsonSchemaObject | JsonSchemaArray;

export interface ToolDeclarationFunction<T extends string = string> {
  readonly name: string;
  readonly description: string;
  readonly parameters?: JsonSchemaObject<T>;
}

export interface ToolDeclaration<T extends string = string> {
  readonly type: "function";
  readonly function: ToolDeclarationFunction<T>;
}

export type ApiToolDict<K extends string, V> = Omit<
  JsonSchemaObject<K, V>,
  "type" | "additionalProperties" | "patternProperties"
> & {
  readonly type?: "object";
};

export interface ApiToolOptions {
  readonly name: string;

  readonly method: "GET" | "POST" | "PUT" | "DELETE" | "PATCH" | "HEAD";
  readonly url: string;
  readonly description: string;
  readonly headers?: Readonly<Record<string, string>>;

  readonly pathVariables?: Readonly<Record<string, JsonSchemaPrimitive>>;
  readonly query?: ApiToolDict<
    string,
    JsonSchemaPrimitive | JsonSchemaArray<JsonSchemaPrimitive> | string | readonly string[]
  >;
  readonly body?: ApiToolDict<string, JsonSchemaValue>;
  readonly response?: JsonSchemaValue;
}

export interface ApiToolResponse {
  readonly statusCode: number;
  readonly errorBody?: string;
  readonly result?: Readonly<Record<string, unknown>>;
}

export interface CommonChatOptions extends ToolCallManagerOptions {
  /**
   * The first system message of the chat.
   * NOTE: If additional_messages is not empty, this property will be ignored.
   *       Because it will be append into the additional_messages.
   */
  readonly welcome_message?: string;
}
