import { ChatMessage, ChatMessageRole, Section } from "./Chat";
import { LiveBuffer, IterableBuffer } from "./IterableBuffer";
import { parseTaggedContent } from "./parseTaggedContent";
import { neverReachHere } from "./utils";

interface MessageForString extends Record<string, unknown> {
  role: "user" | "system";
  content_type?: "text";
  content: string;
}

export type ToolCallFunction = {
  name: string;
  arguments: string;
};

export interface ToolCall {
  id?: string;
  index?: number;
  type: "function";
  function: ToolCallFunction;
}

interface MessageForAssistant {
  role: "assistant";
  content: string;
  tool_calls?: ToolCall[];
}

interface MessageForTool {
  role: "tool";
  tool_call_id?: string;
  content: string;
}

export type OpenAiMessage = MessageForString | MessageForAssistant | MessageForTool;

class ToolCallsAssistant implements ChatMessage {
  readonly index: number;
  readonly role: ChatMessageRole.Assistant;
  readonly sections: LiveBuffer<Section>;
  readonly #pendingResults: LiveBuffer<string>[];

  constructor(index: number) {
    this.index = index;
    this.role = ChatMessageRole.Assistant;
    this.sections = new LiveBuffer<Section>();
    this.#pendingResults = [];
  }

  public close() {
    this.sections.closeIfNeeded();
    this.#pendingResults.splice(0).forEach((result) => result.closeIfNeeded());
  }

  public addContent(value: IterableBuffer<string>) {
    this.sections.enqueue({ type: "content", value });
  }

  public addThink(value: IterableBuffer<string>) {
    this.sections.enqueue({ type: "think", value });
  }

  public addToolCall(call: { name: IterableBuffer<string>; arguments: IterableBuffer<string> }) {
    const result = new LiveBuffer<string>();
    this.#pendingResults.push(result);
    this.sections.enqueue({
      type: "call",
      value: { name: call.name, arguments: call.arguments, result },
    });
  }

  public answerToolCall(content: string) {
    const result = this.#pendingResults.shift();
    if (result) {
      result.enqueue(content);
      result.close();
    }
  }
}

export const convertOamToMessages = (oaMessages: OpenAiMessage[] | undefined) => {
  const messages: ChatMessage[] = [];
  if (!oaMessages || oaMessages.length === 0) return messages;

  let currentAssistant: ToolCallsAssistant | null = null;

  for (let index = 0; index < oaMessages.length; index++) {
    const message = oaMessages[index];
    if (message.role === "user") {
      if (currentAssistant) {
        currentAssistant.close();
        currentAssistant = null;
      }
      messages.push({
        index,
        role: ChatMessageRole.User,
        sections: LiveBuffer.closure([{ type: "content", value: LiveBuffer.closure([message.content]) }]),
      });
    } else if (message.role === "assistant") {
      if (!currentAssistant) {
        currentAssistant = new ToolCallsAssistant(index);
        messages.push(currentAssistant);
      }
      for (const cs of parseTaggedContent(LiveBuffer.closure([message.content])).buffer) {
        switch (cs.type) {
          case "call":
            currentAssistant.addToolCall(cs.value);
            break;
          case "think":
            currentAssistant.addThink(cs.value);
            break;
          case "content":
            currentAssistant.addContent(cs.value);
            break;
          default:
            neverReachHere(cs);
        }
      }
      for (const tc of message.tool_calls || []) {
        currentAssistant.addToolCall({
          name: LiveBuffer.closure([tc.function.name]),
          arguments: LiveBuffer.closure([tc.function.arguments]),
        });
      }
    } else if (message.role === "tool") {
      if (currentAssistant) {
        currentAssistant.answerToolCall(message.content);
      }
    }
  }
  if (currentAssistant) {
    currentAssistant.close();
    currentAssistant = null;
  }
  return messages;
};
