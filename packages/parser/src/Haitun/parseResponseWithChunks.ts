import { LiveBuffer } from "../IterableBuffer";
import { AssistantOutputItem } from "../AssistantOutputItem";
import { Choice } from "./HaitunChunkExtractor";

export const parseResponseWithChunks = (output: LiveBuffer<AssistantOutputItem>, readable: ReadableStream<Choice>) => {
  let content: LiveBuffer<string> | null = null;
  let lastArgsOfCall: LiveBuffer<string> | null = null;

  readable.pipeTo(
    new WritableStream({
      write(choice) {
        const { delta = {}, finish_reason } = choice;
        if (typeof finish_reason === "string") {
          output.enqueue({ type: "finish", value: finish_reason });
          return;
        }
        if (typeof delta.content === "string") {
          if (!content) {
            content = new LiveBuffer<string>();
            // If the message is before any call, send it immediately.
            if (!lastArgsOfCall) output.enqueue({ type: "content", value: content });
          }
          content.enqueue(delta.content);
          if (lastArgsOfCall) {
            // If the content was not been sent,
            if (output.buffer[output.buffer.length - 1].value !== content) {
              // And the content is not empty,
              if (content.buffer.join("").trim() != "") {
                output.enqueue({ type: "content", value: content });
              }
            }
          }
          return;
        }
        if (delta.tool_calls instanceof Array) {
          for (const fc of delta.tool_calls) {
            content?.close();
            content = null;
            const { id, function: funcInfo, index } = (fc || {}) as Record<string, unknown>;
            if (!funcInfo) continue;
            const { name, arguments: argsChunk } = (funcInfo || {}) as Record<string, unknown>;
            if (typeof name === "string") {
              lastArgsOfCall?.close();
              lastArgsOfCall = new LiveBuffer<string>();
              const ltc = {
                id: typeof id === "string" ? id : undefined,
                index: typeof index === "number" ? index : undefined,
                type: "function" as const,
                function: { name: LiveBuffer.closure([name]), arguments: lastArgsOfCall },
              };
              output.enqueue({ type: "call", value: ltc });
            }
            if (typeof argsChunk === "string" && argsChunk) {
              lastArgsOfCall?.enqueue(argsChunk);
            }
          }
          return;
        }
      },
      close() {
        lastArgsOfCall?.close();
        content?.close();
        output.close();
      },
      abort(reason) {
        lastArgsOfCall?.error(reason);
        content?.error(reason);
        output.error(reason);
      },
    })
  );
};
