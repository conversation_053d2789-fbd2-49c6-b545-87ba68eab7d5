import type { ToolDeclaration } from "../Chat";
import type { DeepFreeze } from "src/utils";
import { LiveBuffer } from "../IterableBuffer";
import { SseTransform } from "../SseTransform";
import { HaitunChunkExtractor } from "./HaitunChunkExtractor";
import { OpenAiMessage } from "../OpenAiMessage";

import { AssistantOutputItem } from "../AssistantOutputItem";
import { parseResponseWithChunks } from "./parseResponseWithChunks";
import { parseResponseWithJson } from "./parseResponseWithJson";

export interface HaitunOptions {
  model: string;
  messages: OpenAiMessage[];

  temperature?: number;
  max_tokens?: number;
  tool_choice?: "auto" | "none";
  parallel_tool_calls?: boolean;
  stream?: boolean;

  tools?: ToolDeclaration[];
}

export const loadHaitunData = (
  url: string,
  { options, signal }: { options: DeepFreeze<HaitunOptions> & Record<string, unknown>; signal: AbortSignal }
) => {
  const { stream = true, ...restOptions } = options;
  const response = fetch(url, {
    method: "POST",
    headers: {
      "Server-App-Id": "ios-haitun-inference-svc",
      "Haitun-From-Appid": "fe-van-web",
      "Content-Type": "application/json",
      Accept: stream ? "text/event-stream" : "application/json",
    },
    body: JSON.stringify({ stream, ...restOptions }),
    credentials: "include",
    signal,
  });

  const result = new LiveBuffer<AssistantOutputItem>();

  response
    .then(async (res) => {
      if (!res.ok || !res.body) throw new Error("Failed to fetch");
      if (stream) {
        const chunks = res.body
          .pipeThrough(new TextDecoderStream())
          .pipeThrough(new SseTransform())
          .pipeThrough(new HaitunChunkExtractor());
        parseResponseWithChunks(result, chunks);
      } else {
        parseResponseWithJson(result, await res.json());
      }
    })
    .catch((error) => {
      result.error(error);
    });

  return result;
};
