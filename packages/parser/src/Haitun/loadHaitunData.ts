import type { ToolDeclaration } from "../Chat";
import type { DeepFreeze } from "src/utils";
import { LiveBuffer } from "../IterableBuffer";
import { SseTransform } from "../SseTransform";
import { HaitunChoicesExtractor } from "./HaitunChunkExtractor";
import { OpenAiMessage } from "../OpenAiMessage";

import { AssistantOutputItem } from "../AssistantOutputItem";
import { ChatBasic } from "../ChatBasic";
import { HaitunReceiverWritable } from "./HaitunReceiverWritable";

export interface HaitunOptions {
  model: string;
  messages: OpenAiMessage[];

  temperature?: number;
  max_tokens?: number;
  tool_choice?: "auto" | "none";
  parallel_tool_calls?: boolean;
  stream?: boolean;

  tools?: ToolDeclaration[];
}

export const loadHaitunData = (
  url: string,
  { options, signal }: { options: DeepFreeze<HaitunOptions> & Record<string, unknown>; signal: AbortSignal }
) => {
  const { stream = true, ...restOptions } = options;
  const response = ChatBasic.fetch(url, {
    method: "POST",
    headers: {
      "Server-App-Id": "ios-haitun-inference-svc",
      "Haitun-From-Appid": "fe-van-web",
      "Content-Type": "application/json",
      Accept: stream ? "text/event-stream" : "application/json",
    },
    body: JSON.stringify({ stream, ...restOptions }),
    credentials: "include",
    signal,
  });

  const result = new LiveBuffer<AssistantOutputItem>();

  response
    .then(async (res) => {
      if (!res.ok || !res.body) throw new Error("Failed to fetch");
      if (stream) {
        res.body
          .pipeThrough(new TextDecoderStream())
          .pipeThrough(new SseTransform())
          .pipeThrough(new HaitunChoicesExtractor())
          .pipeTo(new HaitunReceiverWritable(result));
      } else {
        const jsonRes = await res.json();
        new ReadableStream({
          start(controller) {
            const { choices } = Object(jsonRes);
            if (choices instanceof Array) {
              for (const choice of choices) {
                const { message, finish_reason } = Object(choice);
                const { role, content, tool_calls } = Object(message);
                if (role !== "assistant") {
                  console.warn(`Unsupported response message role: ${role}`);
                  continue;
                }
                controller.enqueue({ delta: { content, tool_calls }, finish_reason });
              }
            }
            controller.close();
          },
        }).pipeTo(new HaitunReceiverWritable(result));
      }
    })
    .catch((error) => {
      result.error(error);
    });

  return result;
};
