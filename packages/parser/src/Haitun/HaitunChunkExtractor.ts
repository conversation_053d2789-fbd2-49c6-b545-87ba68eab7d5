import { SseEvent } from "../SseTransform";

export type Delta = Record<string, unknown>;

export interface Choice extends Record<string, unknown> {
  delta: Delta;
  finish_reason: string | null;
}

export class HaitunChunkExtractor extends TransformStream<SseEvent, Choice> {
  constructor() {
    super({
      transform(event, controller) {
        const { data } = event;
        if (!data?.startsWith("{")) return;
        const { object, choices } = Object(JSON.parse(data));
        if (object !== "chat.completion.chunk") return;
        if (!(choices instanceof Array)) return;
        for (const choice of choices) {
          controller.enqueue(choice);
        }
      },
    });
  }
}
