import { IterableBuffer, LiveBuffer } from "../IterableBuffer";
import { Chat, ChatMessage, ChatMessageRole, CommonChatOptions, Section, ToolDeclarationFunction } from "../Chat";
import { convertOamToMessages, OpenAiMessage } from "../OpenAiMessage";
import { HaitunOptions, loadHaitunData } from "./loadHaitunData";
import { parseTaggedContent as parseTaggedContent } from "../parseTaggedContent";
import { ChatBasic } from "../ChatBasic";
import { PartialOptional, deepCopy } from "../utils";
import { LiveToolCall } from "../AssistantOutputItem";
import { convertToAdditionalMessage } from "../convertToAdditionalMessage";
import { ToolCallManager, ToolCallManagerOptions } from "../ToolCallManager";

export interface HaitunChatOptions extends Omit<HaitunOptions, "tools">, CommonChatOptions, ToolCallManagerOptions {
  /**
   * The first system message of the chat.
   * NOTE: If additional_messages is not empty, this property will be ignored.
   *       Because it will be append into the additional_messages.
   */
  system_message?: string;
}

export class HaitunChat extends ChatBasic<HaitunChatOptions> implements Chat<HaitunOptions> {
  #abortController?: AbortController;
  #additionalMessages: OpenAiMessage[];
  readonly #messages: ChatMessage[];
  readonly #toolCallManager: ToolCallManager;

  constructor(url: string, options: PartialOptional<HaitunChatOptions, "messages"> & Record<string, unknown>) {
    const { messages: additional_messages, tools, system_message, welcome_message, ...restOptions } = options;

    if (tools)
      throw new Error("The `tools` parameter is not allowed in HaitunChatOptions, use `registerTool` instead.");

    const am: OpenAiMessage[] = [];
    const mm: ChatMessage[] = [];
    super(url, { ...deepCopy(restOptions), messages: am }, mm);
    this.#additionalMessages = am;
    this.#messages = mm;
    this.#toolCallManager = new ToolCallManager(options);

    // Convert messages to groups.
    // This code is used to recover the history of a conversation.
    if (additional_messages && additional_messages.length > 0) {
      this.#messages.push(...convertOamToMessages(additional_messages));
      this.#additionalMessages.push(...additional_messages);
    } else {
      if (typeof system_message === "string") {
        this.appendContentMessage(ChatMessageRole.System, system_message);
      }
      if (typeof welcome_message === "string") {
        this.appendContentMessage(ChatMessageRole.Assistant, welcome_message);
      }
    }

    this.notify();
  }

  public async requestIfNeeded() {
    if (this.options.messages[this.options.messages.length - 1]?.role === "user") {
      await this.generateNextAssistant();
    }
  }

  public appendContentMessage(role: ChatMessageRole, content: string) {
    if (this.busy) throw new Error("It's busy, please wait for the current response to complete");
    if (this.lastMessage?.role === role) {
      throw new Error("Cannot append a message with the same role with the last message");
    }
    if (role !== "system") {
      this.#messages.push({
        index: this.options.messages.length,
        role,
        sections: LiveBuffer.closure([{ type: "content", value: LiveBuffer.closure([content]) }]),
      });
    }
    this.#additionalMessages.push({ role, content });
    this.notify();
  }

  /**
   * NOTE: This method is synchronous if the input buffer is not in Open state.
   */
  private async generateNextAssistant() {
    const sections = new LiveBuffer<Section>();
    const group: ChatMessage = {
      index: this.options.messages.length,
      role: ChatMessageRole.Assistant,
      sections,
    };
    this.#messages.push(group);
    this.notify();

    // TODO: This is not necessary, we can commit message to #additionalMessages directly.
    const uncommited: OpenAiMessage[] = [];

    try {
      for (;;) {
        if (!this.#abortController) this.#abortController = new AbortController();
        // Call with appended uncommitedMessages.
        const { messages, ...restOptions } = this.options;

        const output = loadHaitunData(this.url, {
          options: {
            ...restOptions,
            messages: messages.concat(uncommited),
            tools: this.#toolCallManager.getToolDeclarations(),
            // This is a extension property, it never be send to LLM.
            additional_api_tools: undefined,
          },
          signal: this.#abortController.signal,
        });

        const contentArray: IterableBuffer<string>[] = [];
        const callArray: LiveToolCall[] = [];
        const invokings: IterableBuffer<string>[] = [];

        let finishReason: string | null = null;

        try {
          for await (const item of output) {
            if (item.type === "call") {
              callArray.push(item.value);
              const result = this.#toolCallManager.invokeTool(item.value.function, this.#abortController);
              sections.enqueue({ type: "call", value: { ...item.value.function, result } });
              invokings.push(result);
            } else if (item.type === "content") {
              contentArray.push(item.value);
              for await (const contentfulSection of parseTaggedContent(item.value)) {
                if (contentfulSection.type === "call") {
                  const result = this.#toolCallManager.invokeTool(contentfulSection.value, this.#abortController);
                  invokings.push(result);
                  sections.enqueue({ type: "call", value: { ...contentfulSection.value, result } });
                } else sections.enqueue(contentfulSection);
              }
            } else if (item.type === "finish") {
              finishReason = item.value;
            }
          }
        } finally {
          uncommited.push(convertToAdditionalMessage(output));

          for (let i = 0; i < invokings.length; i++) {
            const content = (await invokings[i]).join("");
            uncommited.push({ role: "tool", tool_call_id: callArray[i]?.id, content });
          }
        }

        if (invokings.length > 0 || finishReason === "tool_calls") {
          continue;
        }
        break;
      }
    } catch (error) {
      console.error(error);
      sections.error(error);
    } finally {
      // Push uncommitedMessages to messages after all responses are processed.
      this.#additionalMessages.push(...uncommited);
      // This code must be after the `this.options.messages.push(...uncommited);`
      // Because the `close` handler may be access the newest `options.messages`
      // And the `sections` being close indicates that all paragraphs of the message are finished.
      sections.closeIfNeeded();

      this.#abortController = undefined;
      this.notify();
    }
  }

  public next(content: string) {
    this.appendContentMessage(ChatMessageRole.User, content);
    this.requestIfNeeded();
  }

  public rewindToBefore(message: ChatMessage) {
    this.abort();
    const index = this.#messages.indexOf(message);
    if (index === -1) throw new Error("Message not found");
    this.#additionalMessages.splice(message.index);
    this.#messages.splice(index);
    this.notify();
    this.requestIfNeeded();
  }

  public abort(reason?: Error) {
    this.#abortController?.abort(reason || new Error("Aborted"));
  }

  public registerTool<T extends string>(
    declarationFunction: ToolDeclarationFunction<T>,
    implement: (args: Record<T, unknown>) => Promise<unknown>
  ) {
    this.#toolCallManager.registerTool(declarationFunction, implement);
  }

  public registerNativeTool(
    declarationFunction: ToolDeclarationFunction,
    implement: (args: Record<string, unknown>) => Promise<unknown>
  ) {
    this.#toolCallManager.registerNativeTool(declarationFunction, implement);
  }

  public registerEnvironmentVariables(variables: Record<string, string>) {
    this.#toolCallManager.registerEnvironmentVariables(variables);
  }
}
