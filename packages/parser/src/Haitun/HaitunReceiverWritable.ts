import { AssistantOutputItem } from "../AssistantOutputItem";
import { ChoiceContentProcessor } from "../ChoiceContentProcessor";
import { LiveBuffer } from "../IterableBuffer";
import { Choice } from "./HaitunChunkExtractor";

export class HaitunReceiverWritable extends WritableStream<Choice> {
  constructor(receiver: LiveBuffer<AssistantOutputItem>) {
    const cc = new ChoiceContentProcessor(receiver);
    super({
      write(choice) {
        const { delta, finish_reason } = choice;
        const { content, tool_calls } = Object(delta) as Record<string, unknown>;
        if (typeof content === "string") {
          cc.enqueueContent(content);
        }
        if (tool_calls instanceof Array) {
          cc.enqueueToolCallChunk(tool_calls);
        }
        if (typeof finish_reason === "string") {
          receiver.enqueue({ type: "finish", value: finish_reason });
        }
      },
      close() {
        cc.close();
        receiver.closeIfNeeded();
      },
      abort(reason) {
        cc.error(reason);
        receiver.errorIfNeeded(reason);
      },
    });
  }
}
