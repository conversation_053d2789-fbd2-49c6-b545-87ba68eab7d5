import { LiveBuffer } from "../IterableBuffer";
import { AssistantOutputItem } from "../AssistantOutputItem";

export const parseResponseWithJson = (result: LiveBuffer<AssistantOutputItem>, what: unknown) => {
  const { choices } = Object(what);
  if (choices instanceof Array) {
    for (const choice of choices) {
      const { message, finish_reason } = choice;
      if (typeof message === "object" && message !== null) {
        const { role, content, tool_calls } = Object(message);
        if (role !== "assistant") {
          console.warn("Unsupported responsemessage role", message);
          continue;
        }
        result.enqueue({ type: "content", value: LiveBuffer.closure([content]) });
        if (tool_calls instanceof Array) {
          for (const tool_call of tool_calls) {
            const { id, index, type, function: fn } = Object(tool_call);
            if (type !== "function") {
              console.warn("Unsupported tool call type", tool_calls);
              continue;
            }
            const { name, arguments: args } = Object(fn);
            result.enqueue({
              type: "call",
              value: {
                id,
                index,
                type,
                function: { name: LiveBuffer.closure([name]), arguments: LiveBuffer.closure([args]) },
              },
            });
          }
        }
      }
      if (typeof finish_reason === "string") {
        result.enqueue({ type: "finish", value: finish_reason });
      }
    }
  }
  result.close();
};
