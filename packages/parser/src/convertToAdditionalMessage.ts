import { IterableBuffer } from "./IterableBuffer";
import { AssistantOutputItem } from "./AssistantOutputItem";
import { ToolCall } from "./OpenAiMessage";

// TODO: refactor this.
export const convertToAdditionalMessage = (output: IterableBuffer<AssistantOutputItem>) => {
  const callArray: ToolCall[] = [];
  const contentArray: string[] = [];
  for (const item of output.buffer) {
    if (item.type === "call") {
      const { function: fn, ...rest } = item.value;
      callArray.push({
        ...rest,
        function: { name: fn.name.buffer.join(""), arguments: fn.arguments.buffer.join("") },
      });
    } else if (item.type === "content") {
      contentArray.push(item.value.buffer.join(""));
    }
  }
  return {
    role: "assistant" as const,
    content: contentArray.join("\n\n"),
    tool_calls: callArray.length ? callArray : undefined,
  };
};
