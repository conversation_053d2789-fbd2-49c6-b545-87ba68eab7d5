import { AssistantOutputItem } from "../AssistantOutputItem";
import { ChoiceContentProcessor } from "../ChoiceContentProcessor";
import { LiveBuffer } from "../IterableBuffer";

const hackReceiver = (receiver: LiveBuffer<AssistantOutputItem>) => {
  const buffer = new LiveBuffer<AssistantOutputItem>();
  buffer.tee({
    enqueue(item) {
      if (item.type === "call") {
        const {
          function: { name, ...fnRest },
          ...rest
        } = item.value;

        const fixedName = new LiveBuffer<string>();
        let chunks: string = "";
        name.tee({
          enqueue(chunk) {
            chunks += chunk;
          },
          close() {
            fixedName.enqueue(chunks.replace(/^custom_tools---/, ""));
            fixedName.close();
          },
          error(reason) {
            fixedName.error(reason);
          },
        });

        receiver.enqueue({ type: "call", value: { ...rest, function: { name: fixedName, ...fnRest } } }); // TODO:
      } else receiver.enqueue(item);
    },
    close() {
      receiver.close();
    },
    error(reason) {
      receiver.error(reason);
    },
  });
  return buffer;
};

export class WorkflowReceiverWritable extends WritableStream<Record<string, unknown>> {
  constructor(receiver: LiveBuffer<AssistantOutputItem>, output_key: string, tool_calls_key: string) {
    receiver = hackReceiver(receiver);
    const cc = new ChoiceContentProcessor(receiver);
    super({
      write({ type, content }) {
        // For stream output.
        if (type === "answer" && typeof content === "string") {
          cc.enqueueContent(content);
        } else if (type === "function_call" && content instanceof Array) {
          cc.enqueueToolCallChunk(content);
        }

        // For variable output.
        if (type === "answer" && typeof content === "object" && content) {
          const { [output_key]: output, [tool_calls_key]: toolCalls } = Object(content);
          if (typeof output === "string") {
            receiver.enqueue({ type: "content", value: LiveBuffer.closure([output]) });
          }
          if (toolCalls instanceof Array) {
            for (const tc of toolCalls) {
              const { id, function: funcInfo, index } = (tc || {}) as Record<string, unknown>;
              const { name, arguments: argsChunk } = (funcInfo || {}) as Record<string, unknown>;
              if (typeof name !== "string" || typeof argsChunk !== "string") continue;
              receiver.enqueue({
                type: "call",
                value: {
                  id: typeof id === "string" ? id : undefined,
                  index: typeof index === "number" ? index : undefined,
                  type: "function" as const,
                  function: { name: LiveBuffer.closure([name]), arguments: LiveBuffer.closure([argsChunk]) },
                },
              });
            }
          }
        }
      },
      close() {
        cc.close();
        receiver.close();
      },
      abort(reason) {
        cc.error(reason);
        receiver.error(reason);
      },
    });
  }
}
