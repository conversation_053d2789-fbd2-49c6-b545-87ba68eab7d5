import { ChatBasic } from "../ChatBasic";
import { type ChatMessage, ChatMessageRole, CommonChatOptions, Section, ToolDeclarationFunction } from "../Chat";
import { IterableBuffer, LiveBuffer } from "../IterableBuffer";
import { loadWorkflowData, WorkflowOptions } from "./loadWorkflowData";
import { deepCopy } from "../utils";
import { convertOamToMessages, OpenAiMessage } from "../OpenAiMessage";
import { LiveToolCall } from "../AssistantOutputItem";
import { parseTaggedContent } from "../parseTaggedContent";
import { convertToAdditionalMessage } from "../convertToAdditionalMessage";
import { ToolCallManager, ToolCallManagerOptions } from "../ToolCallManager";

export interface WorkflowChatOptions
  extends Omit<WorkflowOptions, "chat_history">,
    CommonChatOptions,
    ToolCallManagerOptions {
  chat_history?: OpenAiMessage[];
}

export class WorkflowChat extends ChatBasic<WorkflowOptions & Record<string, unknown>> {
  readonly #additionalMessages: OpenAiMessage[];
  readonly #messages: ChatMessage[];
  readonly #toolCallManager: ToolCallManager;

  #abortController?: AbortController;

  constructor(url: string, options: WorkflowChatOptions & Record<string, unknown>) {
    const { chat_history, welcome_message, system_message, ...restOptions } = options;

    const am: OpenAiMessage[] = [];
    const mm: ChatMessage[] = [];
    super(url, { ...deepCopy(restOptions), chat_history: am }, mm);
    this.#additionalMessages = am;
    this.#messages = mm;
    this.#toolCallManager = new ToolCallManager(options);

    // Recover chat history from the additional_messages.
    if (chat_history?.length) {
      this.#messages.push(...convertOamToMessages(chat_history));
      this.#additionalMessages.push(...chat_history);
    } else {
      if (typeof system_message === "string") {
        this.appendContentMessage(ChatMessageRole.System, system_message);
      }
      if (typeof welcome_message === "string") {
        this.appendContentMessage(ChatMessageRole.Assistant, welcome_message);
      }
    }
  }

  public async requestIfNeeded() {
    const a = this.#additionalMessages;
    if (a[a.length - 1]?.role === "user") {
      await this.generateNextAssistant();
    }
  }

  public appendContentMessage(role: ChatMessageRole, content: string) {
    if (this.busy) throw new Error("It's busy, please wait for the current response to complete");
    if (this.lastMessage?.role === role) {
      throw new Error("Cannot append a message with the same role with the last message");
    }
    if (role !== ChatMessageRole.System) {
      this.#messages.push({
        index: this.#additionalMessages.length,
        role,
        sections: LiveBuffer.closure([{ type: "content", value: LiveBuffer.closure([content]) }]),
      });
    }
    this.#additionalMessages.push({ role, content });
    this.notify();
  }

  /**
   * NOTE: This method is synchronous if the input buffer is not in Open state.
   */
  private async generateNextAssistant() {
    const sections = new LiveBuffer<Section>();
    const group: ChatMessage = {
      index: this.#additionalMessages.length,
      role: ChatMessageRole.Assistant,
      sections,
    };
    this.#messages.push(group);
    this.notify();

    // TODO: This is not necessary, we can commit message to #additionalMessages directly.
    const uncommited: OpenAiMessage[] = [];

    try {
      for (;;) {
        if (!this.#abortController) this.#abortController = new AbortController();
        // Call with appended uncommitedMessages.
        const { chat_history, ...restOptions } = this.options;

        const output = loadWorkflowData(this.url, {
          options: {
            ...restOptions,
            chat_history: chat_history.concat(uncommited),
            extra_data: { tools: this.#toolCallManager.getToolDeclarations() },
            // This is a extension property, it never be send to LLM.
            additional_api_tools: undefined,
          },
          signal: this.#abortController.signal,
        });

        const contentArray: IterableBuffer<string>[] = [];
        const callArray: LiveToolCall[] = [];
        const invokings: IterableBuffer<string>[] = [];

        let finishReason: string | null = null;

        try {
          for await (const item of output) {
            if (item.type === "call") {
              callArray.push(item.value);
              const result = this.#toolCallManager.invokeTool(item.value.function, this.#abortController);
              sections.enqueue({ type: "call", value: { ...item.value.function, result } });
              invokings.push(result);
            } else if (item.type === "content") {
              contentArray.push(item.value);
              for await (const contentfulSection of parseTaggedContent(item.value)) {
                if (contentfulSection.type === "call") {
                  const result = this.#toolCallManager.invokeTool(contentfulSection.value, this.#abortController);
                  sections.enqueue({ type: "call", value: { ...contentfulSection.value, result } });
                  invokings.push(result);
                } else sections.enqueue(contentfulSection);
              }
            } else if (item.type === "finish") {
              finishReason = item.value;
            }
          }
        } finally {
          uncommited.push(convertToAdditionalMessage(output));

          for (let i = 0; i < invokings.length; i++) {
            const content = (await invokings[i]).join("");
            uncommited.push({ role: "tool", tool_call_id: callArray[i]?.id, content });
          }
        }

        if (invokings.length > 0 || finishReason === "tool_calls") {
          continue;
        }
        break;
      }
    } catch (error) {
      console.error(error);
      sections.error(error);
    } finally {
      // Push uncommitedMessages to messages after all responses are processed.
      this.#additionalMessages.push(...uncommited);
      // This code must be after the `this.options.messages.push(...uncommited);`
      // Because the `close` handler may be access the newest `options.messages`
      // And the `sections` being close indicates that all paragraphs of the message are finished.
      sections.closeIfNeeded();

      this.#abortController = undefined;
      this.notify();
    }
  }

  public next(content: string) {
    this.appendContentMessage(ChatMessageRole.User, content);
    this.requestIfNeeded();
  }

  public rewindToBefore(message: ChatMessage) {
    this.abort();
    const index = this.#messages.indexOf(message);
    if (index === -1) throw new Error("Message not found");
    this.#additionalMessages.splice(message.index);
    this.#messages.splice(index);
    this.notify();
    this.requestIfNeeded();
  }

  public abort(reason?: Error) {
    this.#abortController?.abort(reason || new Error("Aborted"));
  }

  public registerTool<T extends string>(
    declarationFunction: ToolDeclarationFunction<T>,
    implement: (args: Record<T, unknown>) => Promise<unknown>
  ) {
    this.#toolCallManager.registerTool(declarationFunction, implement);
  }

  public registerNativeTool(
    declarationFunction: ToolDeclarationFunction,
    implement: (args: Record<string, unknown>) => Promise<unknown>
  ) {
    this.#toolCallManager.registerNativeTool(declarationFunction, implement);
  }

  public registerEnvironmentVariables(variables: Record<string, string>) {
    this.#toolCallManager.registerEnvironmentVariables(variables);
  }
}
