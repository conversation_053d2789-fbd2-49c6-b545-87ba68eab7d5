import { DeepFreeze, neverReach<PERSON>ere } from "src/utils";

import { IterableBuffer, LiveBuffer } from "../IterableBuffer";
import { SseTransform } from "../SseTransform";
import { OpenAiMessage } from "../OpenAiMessage";
import { AssistantOutputItem } from "../AssistantOutputItem";
import { WorkflowReceiverWritable } from "./WorkflowReceiverWritable";
import { WorkflowObjectExtractor } from "./WorkflowObjectExtractor";
import { ChatBasic } from "../ChatBasic";

export interface WorkflowOptions {
  workflow_id: string;
  user_id: string;
  enable_heartbeat: boolean;
  chat_history: OpenAiMessage[];
  output_key?: string;
  tool_calls_key?: string;
  parameters?: Record<string, unknown>;
}

export const loadWorkflowData = (
  url: string,
  { options, signal }: { options: DeepFreeze<WorkflowOptions> & Record<string, unknown>; signal: AbortSignal }
): IterableBuffer<AssistantOutputItem> => {
  const {
    output_key = "output",
    tool_calls_key = "tool_calls",
    parameters: additionalParameters,
    ...restOptions
  } = options;
  const chat_history = options.chat_history?.slice(0) ?? []; // Copy
  const lastMessage = chat_history[chat_history.length - 1];
  if (!lastMessage) throw new Error("The chat history must not be empty here");
  const parameters = { ...additionalParameters, BOT_USER_INPUT: "" };
  switch (lastMessage.role) {
    case "user":
      parameters.BOT_USER_INPUT = lastMessage.content;
      chat_history.pop();
      break;
    case "tool":
    case "system":
      break;
    case "assistant":
      throw new Error("The last message of the chat history must not be a assistant message");
    default:
      neverReachHere(lastMessage);
  }
  const response = ChatBasic.fetch(url, {
    method: "POST",
    headers: {
      "Server-App-Id": "ios-lma-svc",
      "Content-Type": "application/json",
      Accept: "text/event-stream",
    },
    // TODO: Implement the non-stream mode.
    body: JSON.stringify({ ...restOptions, parameters, chat_history }),
    credentials: "include",
    signal,
  });

  const result = new LiveBuffer<AssistantOutputItem>();

  response
    .then(async (res) => {
      if (!res.ok || !res.body) throw new Error("Failed to fetch");
      const type = res.headers.get("content-type");
      if (type && /^application\/json\b/.test(type)) {
        const { msg } = Object(await res.json());
        if (typeof msg === "string" && msg) throw new Error(msg);
        throw new Error("Received a non-stream response " + type);
      }
      res.body
        .pipeThrough(new TextDecoderStream())
        .pipeThrough(new SseTransform())
        .pipeThrough(new WorkflowObjectExtractor())
        .pipeTo(new WorkflowReceiverWritable(result, output_key, tool_calls_key));
    })
    .catch((error) => {
      result.error(error);
    });

  return result;
};
