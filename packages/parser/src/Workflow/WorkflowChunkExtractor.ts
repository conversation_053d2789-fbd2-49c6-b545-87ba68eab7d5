import { SseEvent } from "../SseTransform";

interface Content {
  output?: string;
  toolCalls: {
    id: string;
    type: "function";
    function: {
      name: string;
      arguments: string;
    };
  }[];
}

export class WorkflowChunkExtractor extends TransformStream<SseEvent, Content> {
  constructor(output_key: string, tool_calls_key: string) {
    super({
      transform(e, controller) {
        const { data, event } = e;
        if (event !== "message") return;
        if (!data?.startsWith("{")) return;
        const { event_type, content, last_error } = Object(JSON.parse(data));
        if (event_type === "conversation.chat.failed") {
          const { msg, code } = Object(last_error);
          controller.error(Object.assign(new Error(msg || "Failed to chat"), { code }));
          return;
        }
        if (event_type !== "conversation.message.delta") return;
        if (typeof content === "string") {
          controller.enqueue({ output: content, toolCalls: [] });
        } else {
          if (content instanceof Array) {
            controller.enqueue({ output: undefined, toolCalls: content });
            return;
          } else if (typeof content === "string") {
            controller.enqueue({ output: content, toolCalls: [] });
            return;
          } else if (content && typeof content === "object") {
            let { [output_key]: output, [tool_calls_key]: toolCalls } = Object(content);
            if (typeof output !== "string") output = undefined;
            if (!(toolCalls instanceof Array)) toolCalls = [];
            controller.enqueue({ output, toolCalls });
          }
        }
      },
    });
  }
}
