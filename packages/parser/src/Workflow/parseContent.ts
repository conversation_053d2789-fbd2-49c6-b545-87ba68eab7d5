import { Section } from "../Chat";
import { IterableBuffer, LiveBuffer } from "../IterableBuffer";
import { SequenceDigester } from "../SequenceDigest";

export const parseContent = (liveContent: IterableBuffer<string>) => {
  const sections = new LiveBuffer<Section>();

  const digester = new SequenceDigester(function* () {
    let current: LiveBuffer<string> | null = null;

    for (;;) {
      if (yield "<think>") {
        if (current) {
          current.close();
          current = null;
        }
        const buf = new LiveBuffer<string>();
        let sent = false;
        for (;;) {
          if (yield "</think>") break;
          const c = yield 1;
          if (!c) break; // EOS
          buf.enqueue(c);
          // Defer to send the think section until it has some non-blank content.
          if (!sent && buf.buffer.join("").trim()) {
            sections.enqueue({ type: "think", value: buf });
            sent = true;
          }
        }
        buf.close();
        continue;
      }

      const c = yield 1;
      if (!c) break; // EOF

      if (!current) {
        current = new LiveBuffer<string>();
        sections.enqueue({ type: "content", value: current });
      }

      current.enqueue(c);
    }
    if (current) current.close();
  });

  liveContent.tee({
    enqueue: (chunk) => digester.write(chunk),
    close: () => {
      digester.close();
      sections.close();
    },
    error: (reason) => {
      sections.error(reason);
    },
  });

  return sections;
};
