import { SseEvent } from "../SseTransform";


export class WorkflowObjectExtractor extends TransformStream<SseEvent, Record<string, unknown>> {
  constructor() {
    super({
      transform(e, controller) {
        const { data, event } = e;
        if (event !== "message") return;
        if (!data?.startsWith("{")) return;
        const obj = Object(JSON.parse(data)) as Record<string, unknown>;
        const { event_type, last_error } = obj;
        if (event_type === "conversation.chat.failed") {
          const { msg, code } = Object(last_error);
          controller.error(Object.assign(new Error(msg || "Failed to chat"), { code }));
          return;
        }
        if (event_type === "conversation.message.delta") {
          controller.enqueue(obj);
        }
      },
    });
  }
}
