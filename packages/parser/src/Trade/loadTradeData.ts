import { DeepFreeze } from "src/utils";
import { IterableBuffer, LiveBuffer } from "../IterableBuffer";
import { SseTransform } from "../SseTransform";
import { MsgExtractor } from "./MsgExtractor";

export type TradeMessageRole = "user" | "assistant" | "system";
export type TradeMessageType = "text";

export interface TradeSerializedMessage {
  role: TradeMessageRole;
  content: string;
  content_type: TradeMessageType;
}

export interface TradeOptions {
  chatId: string;
  account: string;
  userMessage: string;
  platform: string;
  assistant: string;
  model: string;
}

export class TradeMessage implements TradeSerializedMessage {
  public readonly role: TradeMessageRole;
  public readonly content_type: TradeMessageType;
  public readonly content: string;
  constructor(raw: TradeSerializedMessage);
  constructor(role: TradeMessageRole, text: string);
  constructor(...args: [TradeSerializedMessage] | [TradeMessageRole, string]) {
    if (args.length === 1) {
      const [raw] = args;
      this.role = raw.role;
      this.content_type = raw.content_type;
      this.content = raw.content;
    } else {
      const [role, text] = args;
      this.role = role;
      this.content_type = "text";
      this.content = text;
    }
  }
  public static wrap(message: TradeSerializedMessage) {
    if (message instanceof TradeMessage) return message;
    return new TradeMessage(message);
  }
}

export const loadTradeData = (
  url: string,
  { options, signal }: { options: DeepFreeze<TradeOptions> & Record<string, unknown>; signal: AbortSignal }
): IterableBuffer<string> => {
  const { additional_messages, ...rest } = options;
  const response = fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Accept: "text/event-stream",
    },
    // SPECIAL: The userMessage is the last user message, and the orignal `additional_messages` is not sent.
    body: JSON.stringify({ ...rest }),
    signal,
    credentials: "include",
  });

  const result = new LiveBuffer<string>();

  response
    .then((res) => {
      if (!res.ok || !res.body) throw new Error("Failed to fetch");
      return res.body;
    })
    .then(
      async (body) => {
        body
          .pipeThrough(new TextDecoderStream())
          .pipeThrough(new SseTransform())
          .pipeThrough(new MsgExtractor())
          .pipeTo(
            new WritableStream({
              write(chunk) {
                result.enqueue(chunk);
              },
              close() {
                result.close();
              },
              abort(reason) {
                result.error(reason);
              },
            })
          );
      },
      (error) => {
        result.error(error);
      }
    );

  return result;
};
