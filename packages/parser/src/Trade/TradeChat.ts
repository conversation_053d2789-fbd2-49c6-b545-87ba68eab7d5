import { ChatBasic } from "../ChatBasic";
import { type ChatMessage, ChatMessageRole, CommonChatOptions, Section, ToolDeclarationFunction } from "../Chat";
import { BufferState, IterableBuffer, LiveBuffer } from "../IterableBuffer";
import { TradeMessage, loadTradeData, TradeOptions, TradeSerializedMessage } from "./loadTradeData";
import { PartialOptional, deepCopy } from "../utils";
import { parseTaggedContent } from "../parseTaggedContent";

export interface TradeChatOptions extends Omit<TradeOptions, "userMessage">, CommonChatOptions {
  additional_messages: TradeSerializedMessage[];
}

export class TradeChat extends ChatBasic<TradeChatOptions> {
  readonly #additionalMessages: TradeSerializedMessage[];
  readonly #messages: ChatMessage[];
  #abortController?: AbortController;

  constructor(
    url: string,
    options: PartialOptional<TradeChatOptions, "additional_messages"> & Record<string, unknown>
  ) {
    const { additional_messages, welcome_message, ...restOptions } = options;

    const am: TradeSerializedMessage[] = [];
    const mm: ChatMessage[] = [];
    super(url, { ...deepCopy(restOptions), additional_messages: am }, mm);
    this.#additionalMessages = am;
    this.#messages = mm;

    if (additional_messages?.length) {
      for (const message of additional_messages) {
        const { role, content } = TradeMessage.wrap(message);
        switch (role) {
          case "user":
            this.appendContentMessage(ChatMessageRole.User, content);
            break;
          case "assistant":
            this.generateNextAssistant(LiveBuffer.closure([content]));
            break;
          default:
            throw new Error(`Unknown role: ${role}`);
        }
      }
    } else {
      if (typeof welcome_message === "string") {
        this.appendContentMessage(ChatMessageRole.Assistant, welcome_message);
      }
    }

    // Follow Doubao logic, there is never sending a request in the constructor.
    //
    // this.requestIfNeeded();
  }

  public async requestIfNeeded() {
    const a = this.#additionalMessages;
    const lastMessage = a[a.length - 1];
    if (lastMessage?.role === "user") {
      this.#abortController = new AbortController();
      const buffer = loadTradeData(this.url, {
        options: {
          ...this.options,
          userMessage: lastMessage.content,
          // The trade chat is not support chat history, so we need to clear the additional_messages.
          additional_messages: undefined,
          // This is a extension property, it never be send to LLM.
          additional_api_tools: undefined,
          additional_mcp_servers: undefined,
          additional_native_tools: undefined,
        },
        signal: this.#abortController.signal,
      });
      buffer.then(
        () => (this.#abortController = undefined),
        () => (this.#abortController = undefined)
      );

      await this.generateNextAssistant(buffer, true);
    }
  }

  public appendContentMessage(role: ChatMessageRole, content: string) {
    if (this.busy) throw new Error("It's busy, please wait for the current response to complete");
    if (this.lastMessage?.role === role) {
      throw new Error("Cannot append a message with the same role with the last message");
    }
    this.#messages.push({
      index: this.#additionalMessages.length,
      role,
      sections: LiveBuffer.closure([{ type: "content", value: LiveBuffer.closure([content]) }]),
    });
    this.#additionalMessages.push(new TradeMessage(role, content));
    this.notify();
  }

  /**
   * NOTE: This method is synchronous if the input buffer is not in Open state.
   */
  private async generateNextAssistant(buffer: IterableBuffer<string>, needToCall = false) {
    const sections = new LiveBuffer<Section>();
    this.#messages.push({
      role: ChatMessageRole.Assistant,
      index: this.#additionalMessages.length,
      sections,
    });
    this.notify();

    parseTaggedContent(buffer).tee({
      enqueue: (section) => {
        switch (section.type) {
          case "call":
            if (needToCall) {
              this.toolCallManager.invokeTool(section.value, this.#abortController);
            }
            sections.enqueue({
              type: "call",
              value: {
                name: section.value.name,
                arguments: section.value.arguments,
                result: LiveBuffer.closure(["null"]),
              },
            });
            break;
          case "think":
          case "content":
            sections.enqueue(section);
        }
      },
      close: () => {
        sections.close();
      },
      error: (e) => {
        sections.error(e);
      },
    });

    try {
      if (buffer.state === BufferState.Open) await buffer;
    } finally {
      this.#additionalMessages.push(new TradeMessage("assistant", buffer.buffer.join("")));
      this.notify();
    }
  }

  public next(content: string) {
    this.appendContentMessage(ChatMessageRole.User, content);
    this.requestIfNeeded();
  }

  public rewindToBefore(message: ChatMessage) {
    this.abort();
    const index = this.#messages.indexOf(message);
    if (index === -1) throw new Error("Message not found");
    this.#additionalMessages.splice(message.index);
    this.#messages.splice(index);
    this.notify();
    this.requestIfNeeded();
  }

  public abort(reason?: Error) {
    this.#abortController?.abort(reason || new Error("Aborted"));
  }

  /**
   * Register a native tool.
   * NOTE: Only tools in `additional_native_tools` can be registered.
   * @param declarationFunction - The declaration function of the tool.
   * @param implement - The implementation function of the tool.
   * @returns Whether the tool is registered successfully.
   */
  public registerNativeTool(
    declarationFunction: ToolDeclarationFunction,
    implement: (args: Record<string, unknown>) => Promise<unknown>
  ) {
    this.toolCallManager.registerNativeTool(declarationFunction, implement);
  }

  public registerEnvironmentVariables(variables: Record<string, string>) {
    this.toolCallManager.registerEnvironmentVariables(variables);
  }
}
