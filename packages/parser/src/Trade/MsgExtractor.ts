import { SseEvent } from "../SseTransform";

export class MsgExtractor extends TransformStream<SseEvent, string> {
  constructor() {
    const parseJsonData = (json: string) => {
      try {
        return JSON.parse(json);
      } catch (e) {
        console.error("Failed to parse line:", json);
      }
    };

    super({
      transform(event: SseEvent, controller: TransformStreamDefaultController<string>) {
        const json = event.data;
        // Ignore bad events.
        if (typeof json !== "string") return;
        // Ignore events if it's not a json object.
        if (json[0] !== "{") return;

        const data: Record<string, unknown> = parseJsonData(json);

        if (typeof data.msg === "string") {
          controller.enqueue(data.msg);
        } else {
          console.error("Invalid content from data event ", data);
        }
      },
    });
  }
}
