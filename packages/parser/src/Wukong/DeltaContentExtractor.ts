import { SseEvent } from "../SseTransform";

export class DeltaContentExtractor extends TransformStream<SseEvent, string> {
  constructor() {
    const parseJsonData = (json: string) => {
      try {
        return JSON.parse(json);
      } catch (e) {
        console.error("Failed to parse line:", json);
      }
    };

    super({
      transform(event: SseEvent, controller: TransformStreamDefaultController<string>) {
        // Ignore non-data events.
        if (event.event !== "data") return;
        const json = event.data;
        // Ignore bad events.
        if (typeof json !== "string") return;
        // Ignore events if it's not a json object.
        if (json[0] !== "{") return;

        const data: Record<string, unknown> = parseJsonData(json);

        if (data.type === "answer" && data.event_type === "conversation.message.delta") {
          if (typeof data.content === "string") {
            controller.enqueue(data.content);
          } else {
            console.error("Invalid content from data event ", data);
          }
        }
      },
    });
  }
}
