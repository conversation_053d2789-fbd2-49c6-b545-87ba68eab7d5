import { IterableBuffer, LiveBuffer } from "../IterableBuffer";
import { Digest } from "../SequenceDigest";

export enum ParagraphType {
  Answer,
  Action,
  ActionInput,
  Observation,
}

export interface Paragraph {
  type: ParagraphType;
  value: IterableBuffer<string>;
}

const singleLineTypes = new Set([ParagraphType.Action, ParagraphType.ActionInput, ParagraphType.Observation]);

const strongTypes = [[ParagraphType.Action, "Action"]] as const;
const weakTypes = [[ParagraphType.Observation, "Observation"]] as const;

const allTypes = [
  [ParagraphType.Answer, "Answer"],
  ...strongTypes,
  [ParagraphType.ActionInput, "Action Input"],
  ...weakTypes,
] as const;

export function* paragraphing(receiver: LiveBuffer<Paragraph>): Digest {
  let paragraph: (Paragraph & { value: LiveBuffer<string> }) | null = null;

  const nextSection = (type: ParagraphType) => {
    closeSection();
    paragraph = { type, value: new LiveBuffer<string>() };
    receiver.enqueue(paragraph);
    return paragraph;
  };

  const closeSection = () => {
    if (paragraph) {
      paragraph.value.close();
      paragraph = null;
    }
  };

  // Read until EOS.
  while (yield -1) {
    // If the parser state currently is not in any paragraph, start a new paragraph.
    // Lookup the first label to determine the type of the paragraph, and fallback to `Answer`.
    if (!paragraph) {
      let WukongSectionType = ParagraphType.Answer;
      for (const [type, label] of allTypes) {
        if (yield label + ":") {
          WukongSectionType = type;
          yield " ";
          break;
        }
      }
      paragraph = nextSection(WukongSectionType);
    }

    // If the paragraph is a single line, read the content until the EOL.
    if (singleLineTypes.has(paragraph.type)) {
      for (;;) {
        const c = yield 1;
        if (!c || c === "\n") break;
        paragraph.value.enqueue(c);
      }
      closeSection();
      continue;
    }

    // If the paragraph is not a single line, read the content until any strong label occurs.
    if ((paragraph.value.buffer.join("").match(/^```/gm)?.length ?? 0) % 2 === 0) {
      let found;
      for (const [type, label] of strongTypes) {
        if (yield label + ":") {
          found = type;
        }
      }
      if (found) {
        nextSection(found);
        yield " ";
        continue;
      }
    }

    // After EOL, check if the next line is a weak label.
    if (yield "\n") {
      paragraph.value.enqueue("\n"); // Push the EOL to the paragraph.
      // If any weak label occurs, start a new paragraph with the weak type.
      let found;
      for (const [type, label] of weakTypes) {
        if (yield label + ":") {
          found = type;
          break;
        }
      }
      if (found) {
        nextSection(found);
        yield " ";
      }
      continue;
    }

    // Read and parse the dirty JSON only if the paragraph buffer is blank.
    // Require the paragraph buffer is blank, because the dirty JSON may start with certain spaces.
    // if (/^\s*$/.test(paragraph.value.buffer.join(""))) {
    //   const resolveNestedDirtyJson = true;
    //   if (resolveNestedDirtyJson) {
    //     const pg = new LiveBuffer<Paragraph>();
    //     const sd = new SequenceDigester(() => paragraphing(pg));
    //     const sb = new LiveBuffer<string>();
    //     sb.readAll({ enqueue: sd.write.bind(sd), close: () => {}, error: () => {} });
    //     pg.readAll({
    //       enqueue: (sub) => {
    //         closeSection();
    //         receiver.push(sub);
    //       },
    //       close: () => {},
    //       error: () => {},
    //     });
    //     const matched = yield* readAndParseDirtyJson(sb);
    //     if (matched) continue;
    //   } else {
    //     const matched = yield* readAndParseDirtyJson(paragraph.value);
    //     if (matched) continue;
    //   }
    // }

    // Otherwise, push the next character to the paragraph anyway.
    paragraph.value.enqueue(yield 1);
  }
}
