import type { Section } from "../Chat";
import { B<PERSON>erState, IterableBuffer, LiveBuffer } from "../IterableBuffer";
import { parseTaggedContent } from "../parseTaggedContent";
import { ToolCallManager } from "../ToolCallManager";
import { neverReachHere } from "../utils";
import { Paragraph, ParagraphType } from "./Paragraphing";

export const convertParagraphsToSections = (
  paragraphs: IterableBuffer<Paragraph>,
  toolCallManager?: ToolCallManager,
  abortController?: AbortController
): IterableBuffer<Section> => {
  const sections = new LiveBuffer<Section>();

  let lastCall: { name: IterableBuffer<string>; arguments: LiveBuffer<string>; result: LiveBuffer<string> };
  paragraphs.tee({
    enqueue: (paragraph) => {
      switch (paragraph.type) {
        case ParagraphType.Answer:
          parseTaggedContent(paragraph.value).tee({
            enqueue: (section) => {
              switch (section.type) {
                case "call":
                  toolCallManager?.invokeTool(section.value, abortController);
                  sections.enqueue({
                    type: "call",
                    value: {
                      name: section.value.name,
                      arguments: section.value.arguments,
                      result: LiveBuffer.closure(["null"]),
                    },
                  });
                  break;
                case "content":
                case "think":
                  sections.enqueue(section);
                  break;
                default:
                  neverReachHere(section);
              }
            },
            error: (reason) => {
              sections.error(reason);
            },
          });
          break;
        case ParagraphType.Action:
          lastCall = { name: paragraph.value, arguments: new LiveBuffer<string>(), result: new LiveBuffer<string>() };
          sections.enqueue({ type: "call", value: lastCall });
          break;
        case ParagraphType.ActionInput:
          if (lastCall) paragraph.value.tee(lastCall.arguments);
          break;
        case ParagraphType.Observation:
          if (lastCall) paragraph.value.tee(lastCall.result);
          break;
        default:
          neverReachHere(paragraph.type);
      }
    },
    close: () => {
      if (lastCall) {
        lastCall.result.closeIfNeeded();
        lastCall.arguments.closeIfNeeded();
      }
      sections.close();
    },
    error: (reason) => {
      if (lastCall) {
        if (lastCall.result.state === BufferState.Open) lastCall.result.error(reason);
        if (lastCall.arguments.state === BufferState.Open) lastCall.arguments.error(reason);
      }
      sections.error(reason);
    },
  });
  return sections;
};
