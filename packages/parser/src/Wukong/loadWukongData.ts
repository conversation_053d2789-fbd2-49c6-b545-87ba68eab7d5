import { Deep<PERSON>reeze } from "src/utils";
import { It<PERSON>bleBuffer, LiveBuffer } from "../IterableBuffer";
import { SseTransform } from "../SseTransform";
import { DeltaContentExtractor } from "./DeltaContentExtractor";
import { ChatBasic } from "../ChatBasic";

export type WukongMessageRole = "user" | "assistant" | "system";
export type WukongMessageType = "text";

export interface WukongSerializedMessage {
  role: WukongMessageRole;
  content: string;
  content_type: WukongMessageType;
}

export interface WukongOptions {
  agent_id: string;
  user_id: string;
  business_alias: string;
  project_alias: string;
  additional_messages: WukongSerializedMessage[];
}

export class WukongMessage implements WukongSerializedMessage {
  public readonly role: WukongMessageRole;
  public readonly content_type: WukongMessageType;
  public readonly content: string;
  constructor(raw: WukongSerializedMessage);
  constructor(role: WukongMessageRole, text: string);
  constructor(...args: [WukongSerializedMessage] | [WukongMessageRole, string]) {
    if (args.length === 1) {
      const [raw] = args;
      this.role = raw.role;
      this.content_type = raw.content_type;
      this.content = raw.content;
    } else {
      const [role, text] = args;
      this.role = role;
      this.content_type = "text";
      this.content = text;
    }
  }
  public static wrap(message: WukongSerializedMessage) {
    if (message instanceof WukongMessage) return message;
    return new WukongMessage(message);
  }
}

export const loadWukongData = (
  url: string,
  { options, signal }: { options: DeepFreeze<WukongOptions> & Record<string, unknown>; signal: AbortSignal }
): IterableBuffer<string> => {
  const { stream = true, ...restOptions } = options;
  const response = ChatBasic.fetch(url, {
    method: "POST",
    headers: {
      "Server-App-Id": "ios-lma-svc",
      "Content-Type": "application/json",
      Accept: "text/event-stream",
    },
    // TODO: Implement the non-stream mode.
    body: JSON.stringify({ ...restOptions, stream }),
    credentials: "include",
    signal,
  });

  const result = new LiveBuffer<string>();

  response
    .then(async (res) => {
      if (!res.ok || !res.body) throw new Error("Failed to fetch");
      if (stream) {
        const type = res.headers.get("content-type");
        if (type && /^application\/json\b/.test(type)) {
          const { msg } = Object(await res.json());
          if (typeof msg === "string" && msg) throw new Error(msg);
          throw new Error("Received a non-stream response " + type);
        }
        res.body
          .pipeThrough(new TextDecoderStream())
          .pipeThrough(new SseTransform())
          .pipeThrough(new DeltaContentExtractor())
          .pipeTo(
            new WritableStream({
              write: (chunk) => result.enqueue(chunk),
              close: () => result.close(),
              abort: (reason) => result.error(reason),
            })
          );
      } else {
        const { data } = Object(await res.json());
        const { messages } = Object(data);
        if (messages instanceof Array) {
          for (const message of messages) {
            const { role, content, content_type } = Object(message);
            if (role !== "assistant") console.log("Wukong: Unexpected role", role);
            if (content_type !== "text") console.log("Wukong: Unexpected content_type", content_type);
            if (typeof content === "string" && content) {
              result.enqueue(content);
            }
          }
        }
        result.close();
      }
    })
    .catch((error) => {
      result.error(error);
    });

  return result;
};
