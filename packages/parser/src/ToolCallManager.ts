import { ToolDeclarationFunction, ApiToolOptions } from "./Chat";
import { buildApiTool } from "./buildApiTool";
import { ImplementedTool } from "./ImplementedTool";
import { IterableBuffer, LiveBuffer } from "./IterableBuffer";

export interface ToolCallManagerOptions {
  /**
   * Register API tools.
   * NOTE: This is a extension property, it never be send to LLM.
   */
  additional_api_tools?: ApiToolOptions[];

  /**
   * Select which native tools can be used.
   * NOTE: This is a extension property, it never be send to LLM.
   */
  additional_native_tools?: string[];
}

export class ToolCallManager {
  readonly #tools: ImplementedTool<string>[];
  readonly #allowedNativeTools: Set<string>;
  readonly #environmentVariables: Record<string, string>;

  constructor(options: ToolCallManagerOptions) {
    this.#tools = [];
    this.#allowedNativeTools = new Set();
    this.#environmentVariables = Object.create(null);

    if (options.additional_native_tools instanceof Array) {
      for (const tool of options.additional_native_tools) {
        if (typeof tool === "string") {
          this.#allowedNativeTools.add(tool);
        } else {
          console.warn("The `additional_native_tools` must be an array of strings, but got", tool);
        }
      }
    }

    if (options.additional_api_tools instanceof Array) {
      for (const tool of options.additional_api_tools) {
        if (typeof tool === "object" && tool !== null) {
          this.registerApiTool(tool);
        } else {
          console.warn("The `additional_api_tools` must be an array of objects, but got", tool);
        }
      }
    }
  }

  public getToolDeclarations() {
    return this.#tools;
  }

  public findTool(name: string) {
    return this.#tools.find((t) => t.name === name);
  }

  public registerTool<T extends string>(
    declarationFunction: ToolDeclarationFunction<T>,
    implement: (args: Record<T, unknown>) => Promise<unknown>
  ) {
    this.#tools.push(new ImplementedTool(declarationFunction, implement));
  }

  public registerApiTool(options: ApiToolOptions) {
    const { declaration, implementation } = buildApiTool(options, this.#environmentVariables);
    this.registerTool(declaration, implementation);
  }

  public registerNativeTool(
    declarationFunction: ToolDeclarationFunction,
    implement: (args: Record<string, unknown>) => Promise<unknown>
  ) {
    if (!this.#allowedNativeTools.has(declarationFunction.name)) return false;
    this.registerTool(declarationFunction, implement);
    return true;
  }

  public registerEnvironmentVariables(variables: Record<string, string>) {
    for (const [name, value] of Object.entries(variables)) {
      this.#environmentVariables[name] = value;
    }
  }

  /**
   * NOTE: This method will never be rejected.
   */
  private async invokeToolAndCatchError(
    name: string | IterableBuffer<string>,
    jsonArgs: string | IterableBuffer<string>,
    abortController: AbortController | undefined
  ) {
    if (typeof name !== "string") name = (await name).join("");
    if (typeof jsonArgs !== "string") jsonArgs = (await jsonArgs).join("");
    try {
      const tool = this.findTool(name);
      if (!tool) throw new Error(`Tool '${name}' is not found`);

      const parsedArgs = JSON.parse(jsonArgs || "{}");

      //`race` with an `aborter` to make the `implementation.call` can be aborted.
      const result = await Promise.race([
        tool.implementation.call(null, parsedArgs),
        new Promise((_, reject) => {
          abortController?.signal.addEventListener("abort", ({ target }) => {
            if (target instanceof AbortSignal) return reject(target.reason);
            throw new Error("Never reach here: AbortSignal is not an instance of AbortSignal in abort event handler");
          });
        }),
      ]);
      // Assert the result can be serialized to JSON.
      JSON.stringify(result);
      return { result };
    } catch (error) {
      if (error instanceof Error) {
        return { error: error.message };
      } else {
        return { error: "Unknown error" };
      }
    }
  }

  /**
   * NOTE: This method will never be rejected.
   */
  public invokeTool(
    func: {
      name: IterableBuffer<string> | string;
      arguments: IterableBuffer<string> | string;
    },
    abortController: AbortController | undefined
  ) {
    const result = new LiveBuffer<string>();
    this.invokeToolAndCatchError(func.name, func.arguments, abortController).then((res) => {
      result.enqueue(JSON.stringify(res.result));
      result.close();
    });
    return result;
  }
}
