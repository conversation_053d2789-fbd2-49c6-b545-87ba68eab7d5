import { <PERSON><PERSON><PERSON>State, LiveBuffer } from "../IterableBuffer";
import {
  readJsonString,
  readJsonStringToBuffer,
  readJsonValue,
  readNumber,
  readSpaces,
  SequenceDigester,
} from "../SequenceDigest";

describe("SequenceDigester", () => {
  describe("basic reading functionality", () => {
    it("should read specified number of characters", () => {
      const digester = new SequenceDigester(function* () {
        const result = yield 3;
        expect(result).toBe("abc");
        const result2 = yield 2;
        expect(result2).toBe("de");
      });

      digester.write("abcde");
    });

    it("should handle multiple writes", () => {
      const digester = new SequenceDigester(function* () {
        const result = yield 3;
        expect(result).toBe("abc");
        const result2 = yield 2;
        expect(result2).toBe("de");
      });

      digester.write("ab");
      digester.write("c");
      digester.write("de");
    });
  });

  describe("lookup functionality", () => {
    it("should lookup without consuming buffer", () => {
      const digester = new SequenceDigester(function* () {
        const peek = yield -3;
        expect(peek).toBe("abc");
        const read = yield 3;
        expect(read).toBe("abc");
      });

      digester.write("abcde");
    });
  });

  describe("string matching", () => {
    it("should match and consume string prefix", () => {
      const digester = new SequenceDigester(function* () {
        const result = yield "hello";
        expect(result).toBe("hello");
        const remaining = yield 2;
        expect(remaining).toBe("12");
      });

      digester.write("hello12");
    });

    it("should return empty string when prefix does not match", () => {
      const digester = new SequenceDigester(function* () {
        const result = yield "hello";
        expect(result).toBe("");
        const remaining = yield 5;
        expect(remaining).toBe("world");
      });

      digester.write("world");
    });
  });

  describe("error handling", () => {
    it("should throw error when reading zero size chunk", () => {
      const digester = new SequenceDigester(function* () {
        yield 0;
      });

      expect(() => digester.write("test")).toThrow(RangeError);
    });

    it("should throw error when reading empty string", () => {
      const digester = new SequenceDigester(function* () {
        yield "";
      });

      expect(() => digester.write("test")).toThrow(RangeError);
    });

    it("should throw error when digest function yields nothing", () => {
      const digester = new SequenceDigester(function* () {
        // Empty generator
      });

      expect(() => digester.write("test")).toThrow("The digest function must yield something");
    });
  });

  describe("buffer management", () => {
    it("should maintain buffer state between writes", () => {
      const digester = new SequenceDigester(function* () {
        const first = yield 3;
        expect(first).toBe("abc");
        const second = yield 3;
        expect(second).toBe("def");
      });

      digester.write("ab");
      expect(digester.getRestBuffer()).toBe("ab");
      digester.write("c");
      expect(digester.getRestBuffer()).toBe("");
      digester.write("def");
      expect(digester.getRestBuffer()).toBe("");
    });
  });

  describe("close functionality", () => {
    it("should process remaining buffer when closed", () => {
      const originalConsoleWarn = console.warn;
      console.warn = jest.fn();

      const digester = new SequenceDigester(function* () {
        const result = yield 3;
        expect(result).toBe("abc");
        const result2 = yield 2; // Not enough bytes to read
        expect(result2).toBe("");
      });

      digester.write("abcd");
      digester.close();
      expect(digester.getRestBuffer()).toBe("d");
      expect(console.warn).toHaveBeenCalledWith("Some bytes are not consumed by the digest function");

      console.warn = originalConsoleWarn;
    });

    it("should warn when there are unprocessed bytes after close", () => {
      const originalConsoleWarn = console.warn;
      console.warn = jest.fn();

      const digester = new SequenceDigester(function* () {
        const result = yield 2;
        expect(result).toBe("ab");
        yield 3;
      });

      digester.write("abcd");
      digester.close();
      expect(console.warn).toHaveBeenCalledWith("Some bytes are not consumed by the digest function");
      expect(digester.getRestBuffer()).toBe("cd");

      console.warn = originalConsoleWarn;
    });

    it("should handle multiple close calls", () => {
      const digester = new SequenceDigester(function* () {
        const result = yield 3;
        expect(result).toBe("abc");
      });

      digester.write("abc");
      digester.close();
      expect(() => digester.close()).toThrow("Is already done");
      expect(digester.getRestBuffer()).toBe("");
    });

    it("should handle empty buffer on close", () => {
      const digester = new SequenceDigester(function* () {
        const result = yield 3;
        expect(result).toBe("abc");
      });

      digester.write("abc");
      digester.close();
      expect(() => digester.write("")).toThrow("Is already done");
      expect(digester.getRestBuffer()).toBe("");
    });
  });

  describe("bad digest function", () => {
    it("endless", () => {
      const digester = new SequenceDigester(function* () {
        yield "abc";
      });
      expect(() => digester.write("abcdef")).toThrow(
        "Nothing is consumed by the digest function, infinite loop may happen"
      );
    });
  });
});

describe("JSON parsing functions", () => {
  describe("readJsonString", () => {
    it("should parse simple JSON string", () => {
      const digester = new SequenceDigester(function* () {
        const result = yield* readJsonString();
        expect(result).toBe("hello world");
      });

      digester.write('"hello world"');
    });

    it("should parse JSON string with escape sequences", () => {
      const digester = new SequenceDigester(function* () {
        const result = yield* readJsonString();
        expect(result).toBe('hello\nworld\t"quote"');
      });

      digester.write('"hello\\nworld\\t\\"quote\\""');
    });

    it("should parse JSON string with unicode escape sequences", () => {
      const digester = new SequenceDigester(function* () {
        const result = yield* readJsonString();
        expect(result).toBe("hello\u0041world");
      });

      digester.write('"hello\\u0041world"');
    });

    it("should parse JSON string with hex escape sequences", () => {
      const digester = new SequenceDigester(function* () {
        const result = yield* readJsonString();
        expect(result).toBe("hello\x41world");
      });

      digester.write('"hello\\x41world"');
    });

    it("should throw error when string doesn't start with quote", () => {
      const digester = new SequenceDigester(function* () {
        yield* readSpaces();
        yield* readJsonString();
      });

      expect(() => digester.write("  hello world")).toThrow("Expected '\"' but got 'h'");
    });

    it("should throw error on unexpected newline in string", () => {
      const digester = new SequenceDigester(function* () {
        yield* readJsonString();
      });

      expect(() => digester.write('"hello\nworld"')).toThrow("Unexpected newline in JSON string");
    });

    it("should throw error on unexpected EOF in string", () => {
      const digester = new SequenceDigester(function* () {
        yield* readJsonString();
      });

      digester.write('"hello');
      expect(() => digester.close()).toThrow("Unexpected EOF in JSON string");
    });

    it("should handle streaming input", () => {
      const digester = new SequenceDigester(function* () {
        const result = yield* readJsonString();
        expect(result).toBe("hello world");
      });

      digester.write('"hello');
      digester.write(' world"');
    });
  });

  describe("readJsonStringToBuffer", () => {
    it("should parse string to buffer", () => {
      const digester = new SequenceDigester(function* () {
        const buffer = new LiveBuffer<string>();
        yield* readJsonStringToBuffer(buffer);
        expect(buffer.state).toBe(BufferState.Open);
        expect(buffer.buffer.join("")).toBe("hello world");
      });

      digester.write('"hello world"');
    });

    it("should return false when string doesn't start with quote", () => {
      const digester = new SequenceDigester(function* () {
        const lb = new LiveBuffer<string>();
        yield* readJsonStringToBuffer(lb);
      });

      expect(() => digester.write("hello world")).toThrow("Expected '\"' but got 'h'");
    });

    it("should handle escape sequences in buffer", () => {
      const digester = new SequenceDigester(function* () {
        const buffer = new LiveBuffer<string>();
        yield* readJsonStringToBuffer(buffer);
        expect(buffer.buffer.join("")).toBe('hello\nworld\t"quote"');
      });

      digester.write('"hello\\nworld\\t\\"quote\\""');
    });
  });

  describe("readJsonValue", () => {
    it("should parse boolean true", () => {
      const digester = new SequenceDigester(function* () {
        const result = yield* readJsonValue();
        expect(result).toBe(true);
      });

      digester.write("true");
    });

    it("should parse boolean false", () => {
      const digester = new SequenceDigester(function* () {
        const result = yield* readJsonValue();
        expect(result).toBe(false);
      });

      digester.write("false");
    });

    it("should parse null", () => {
      const digester = new SequenceDigester(function* () {
        const result = yield* readJsonValue();
        expect(result).toBe(null);
      });

      digester.write("null");
    });

    it("should parse numbers", () => {
      const digester = new SequenceDigester(function* () {
        const result = yield* readJsonValue();
        expect(result).toBe(123.45);
      });

      digester.write("123.45");
    });

    it("should parse integers", () => {
      const digester = new SequenceDigester(function* () {
        const result = yield* readJsonValue();
        expect(result).toBe(123);
      });

      digester.write("123");
    });

    it("should parse JSON strings", () => {
      const digester = new SequenceDigester(function* () {
        const result = yield* readJsonValue();
        expect(result).toBe("hello world");
      });

      digester.write('"hello world"');
    });

    it("should parse empty arrays", () => {
      const digester = new SequenceDigester(function* () {
        const result = yield* readJsonValue();
        expect(result).toEqual([]);
      });

      digester.write("[]");
    });

    it("should parse arrays with values", () => {
      const digester = new SequenceDigester(function* () {
        const result = yield* readJsonValue();
        expect(result).toEqual([1, "hello", true]);
      });

      digester.write('[1, "hello", true]');
    });

    it("should parse nested arrays", () => {
      const digester = new SequenceDigester(function* () {
        const result = yield* readJsonValue();
        expect(result).toEqual([1, [2, 3], 4]);
      });

      digester.write("[1, [2, 3], 4]");
    });

    it("should parse empty objects", () => {
      const digester = new SequenceDigester(function* () {
        const result = yield* readJsonValue();
        expect(result).toEqual({});
      });

      digester.write("{}");
    });

    it("should parse objects with key-value pairs", () => {
      const digester = new SequenceDigester(function* () {
        const result = yield* readJsonValue();
        expect(result).toEqual({ name: "John", age: 30, active: true });
      });

      digester.write('{"name": "John", "age": 30, "active": true}');
    });

    it("should parse nested objects", () => {
      const digester = new SequenceDigester(function* () {
        const result = yield* readJsonValue();
        expect(result).toEqual({ user: { name: "John", age: 30 } });
      });

      digester.write('{"user": {"name": "John", "age": 30}}');
    });

    it("should handle whitespace in arrays", () => {
      const digester = new SequenceDigester(function* () {
        const result = yield* readJsonValue();
        expect(result).toEqual([1, 2, 3]);
      });

      digester.write("[ 1 , 2 , 3 ]");
    });

    it("should handle whitespace in objects", () => {
      const digester = new SequenceDigester(function* () {
        const result = yield* readJsonValue();
        expect(result).toEqual({ a: 1, b: 2 });
      });

      digester.write('{ "a" : 1 , "b" : 2 }');
    });

    it("should throw error on invalid array syntax", () => {
      const digester = new SequenceDigester(function* () {
        yield* readJsonValue();
      });
      digester.write("[1, 2");
      expect(() => digester.close()).toThrow("Expected ',' or ']' but got EOF");
    });

    it("should throw error on invalid object syntax", () => {
      const digester = new SequenceDigester(function* () {
        yield* readJsonValue();
      });
      digester.write('{"name"');
      expect(() => digester.close()).toThrow("Expected ':' but got EOF");
    });

    it("should handle streaming input for complex JSON", () => {
      const digester = new SequenceDigester(function* () {
        const result = yield* readJsonValue();
        expect(result).toEqual({ users: [{ name: "John", age: 30 }] });
      });

      digester.write('{"users": [');
      digester.write('{"name": "John", "age": 30}');
      digester.write("]}");
    });
  });

  describe("readSpaces", () => {
    it("should read and return whitespace characters", () => {
      const digester = new SequenceDigester(function* () {
        const spaces = yield* readSpaces();
        expect(spaces).toBe("  \t\n\r");
        const remaining = yield 3;
        expect(remaining).toBe("abc");
      });

      digester.write("  \t\n\rabc");
    });

    it("should return empty string when no whitespace", () => {
      const digester = new SequenceDigester(function* () {
        const spaces = yield* readSpaces();
        expect(spaces).toBe("");
        const remaining = yield 3;
        expect(remaining).toBe("abc");
      });

      digester.write("abc");
    });

    it("should handle mixed whitespace and content", () => {
      const digester = new SequenceDigester(function* () {
        const spaces1 = yield* readSpaces();
        expect(spaces1).toBe("  ");
        const content = yield 3;
        expect(content).toBe("abc");
        const spaces2 = yield* readSpaces();
        expect(spaces2).toBe("\t");
        const remaining = yield 2;
        expect(remaining).toBe("de");
      });

      digester.write("  abc\tde");
    });
  });

  describe("readNumber", () => {
    it("should parse integers", () => {
      const digester = new SequenceDigester(function* () {
        const number = yield* readNumber();
        expect(number).toBe(123);
        const remaining = yield 2;
        expect(remaining).toBe("ab");
      });

      digester.write("123ab");
    });

    it("should parse decimal numbers", () => {
      const digester = new SequenceDigester(function* () {
        const number = yield* readNumber();
        expect(number).toBe(123.45);
        const remaining = yield 2;
        expect(remaining).toBe("ab");
      });

      digester.write("123.45ab");
    });

    it("should return null when no number found", () => {
      const digester = new SequenceDigester(function* () {
        const number = yield* readNumber();
        expect(number).toBe(null);
        const remaining = yield 3;
        expect(remaining).toBe("abc");
      });

      digester.write("abc");
    });

    it("should handle multiple decimal points", () => {
      const digester = new SequenceDigester(function* () {
        const number = yield* readNumber();
        expect(number).toBe(123.45);
        const remaining = yield 5;
        expect(remaining).toBe(".67ab");
      });

      digester.write("123.45.67ab");
    });

    it("should handle numbers at end of input", () => {
      const digester = new SequenceDigester(function* () {
        const number = yield* readNumber();
        expect(number).toBe(123.45);
      });

      digester.write("123.45");
    });

    // New comprehensive test cases
    it("should parse negative integers", () => {
      const digester = new SequenceDigester(function* () {
        const number = yield* readNumber();
        expect(number).toBe(-123);
        const remaining = yield 2;
        expect(remaining).toBe("ab");
      });

      digester.write("-123ab");
    });

    it("should parse negative decimal numbers", () => {
      const digester = new SequenceDigester(function* () {
        const number = yield* readNumber();
        expect(number).toBe(-123.45);
        const remaining = yield 2;
        expect(remaining).toBe("ab");
      });

      digester.write("-123.45ab");
    });

    it("should parse zero", () => {
      const digester = new SequenceDigester(function* () {
        const number = yield* readNumber();
        expect(number).toBe(0);
        const remaining = yield 2;
        expect(remaining).toBe("ab");
      });

      digester.write("0ab");
    });

    it("should parse negative zero", () => {
      const digester = new SequenceDigester(function* () {
        const number = yield* readNumber();
        expect(number).toBe(-0);
        const remaining = yield 2;
        expect(remaining).toBe("ab");
      });

      digester.write("-0ab");
    });

    it("should handle leading zeros correctly", () => {
      const digester = new SequenceDigester(function* () {
        const number = yield* readNumber();
        expect(number).toBe(0);
        const remaining = yield 2;
        expect(remaining).toBe("12");
      });

      digester.write("012");
    });

    it("should handle negative leading zeros correctly", () => {
      const digester = new SequenceDigester(function* () {
        const number = yield* readNumber();
        expect(number).toBe(-0);
        const remaining = yield 2;
        expect(remaining).toBe("12");
      });

      digester.write("-012");
    });

    it("should parse numbers with uppercase exponent", () => {
      const digester = new SequenceDigester(function* () {
        const number = yield* readNumber();
        expect(number).toBe(123e2);
        const remaining = yield 2;
        expect(remaining).toBe("ab");
      });

      digester.write("123E2ab");
    });

    it("should parse numbers with lowercase exponent", () => {
      const digester = new SequenceDigester(function* () {
        const number = yield* readNumber();
        expect(number).toBe(123e-2);
        const remaining = yield 2;
        expect(remaining).toBe("ab");
      });

      digester.write("123e-2ab");
    });

    it("should parse numbers with positive exponent", () => {
      const digester = new SequenceDigester(function* () {
        const number = yield* readNumber();
        expect(number).toBe(123e2);
        const remaining = yield 2;
        expect(remaining).toBe("ab");
      });

      digester.write("123E+2ab");
    });

    it("should parse complex numbers with decimal and exponent", () => {
      const digester = new SequenceDigester(function* () {
        const number = yield* readNumber();
        expect(number).toBe(-123.45e-3);
        const remaining = yield 2;
        expect(remaining).toBe("ab");
      });

      digester.write("-123.45e-3ab");
    });

    it("should return null when point is the first character", () => {
      const digester = new SequenceDigester(function* () {
        const number = yield* readNumber();
        expect(number).toBe(null);
        expect(yield 4).toBe(".123");
        expect(yield 2).toBe("ab");
      });

      digester.write(".123ab");
    });

    it("should throw error when point after minus sign", () => {
      const digester = new SequenceDigester(function* () {
        yield* readNumber();
      });

      expect(() => digester.write("-.123ab")).toThrow("No number after minus sign in JSON");
    });

    it("should parse numbers with only exponent part", () => {
      const digester = new SequenceDigester(function* () {
        const number = yield* readNumber();
        expect(number).toBe(1e2);
        const remaining = yield 2;
        expect(remaining).toBe("ab");
      });

      digester.write("1E2ab");
    });

    it("should handle very large numbers", () => {
      const digester = new SequenceDigester(function* () {
        const number = yield* readNumber();
        expect(number).toBe(123456789);
        const remaining = yield 2;
        expect(remaining).toBe("ab");
      });

      digester.write("123456789ab");
    });

    it("should handle very small decimal numbers", () => {
      const digester = new SequenceDigester(function* () {
        const number = yield* readNumber();
        expect(number).toBe(0.000001);
        const remaining = yield 2;
        expect(remaining).toBe("ab");
      });

      digester.write("0.000001ab");
    });

    it("should handle numbers with multiple digits in exponent", () => {
      const digester = new SequenceDigester(function* () {
        const number = yield* readNumber();
        expect(number).toBe(123e123);
        const remaining = yield 2;
        expect(remaining).toBe("ab");
      });

      digester.write("123E123ab");
    });

    it("should handle numbers with multiple digits in negative exponent", () => {
      const digester = new SequenceDigester(function* () {
        const number = yield* readNumber();
        expect(number).toBe(123e-123);
        const remaining = yield 2;
        expect(remaining).toBe("ab");
      });

      digester.write("123e-123ab");
    });

    it("should handle streaming input for large numbers", () => {
      const digester = new SequenceDigester(function* () {
        const number = yield* readNumber();
        expect(number).toBe(123456.789);
        const remaining = yield 2;
        expect(remaining).toBe("ab");
      });

      digester.write("123");
      digester.write("456.789ab");
    });

    it("should handle streaming input for numbers with exponents", () => {
      const digester = new SequenceDigester(function* () {
        const number = yield* readNumber();
        expect(number).toBe(123e-45);
        const remaining = yield 2;
        expect(remaining).toBe("ab");
      });

      digester.write("123e");
      digester.write("-45ab");
    });

    // Error cases
    it("should throw error for unterminated fractional number", () => {
      const digester = new SequenceDigester(function* () {
        yield* readNumber();
      });

      digester.write("123.");
      expect(() => digester.close()).toThrow("Unterminated fractional number in JSON");
    });

    it("should throw error for missing number after minus sign", () => {
      const digester = new SequenceDigester(function* () {
        yield* readNumber();
      });

      digester.write("-");
      expect(() => digester.close()).toThrow("No number after minus sign in JSON");
    });

    it("should throw error for missing number in exponent part", () => {
      const digester = new SequenceDigester(function* () {
        yield* readNumber();
      });

      digester.write("123E");
      expect(() => digester.close()).toThrow("Exponent part is missing a number in JSON");
    });

    it("should throw error for missing number in negative exponent part", () => {
      const digester = new SequenceDigester(function* () {
        yield* readNumber();
      });

      digester.write("123e-");
      expect(() => digester.close()).toThrow("Exponent part is missing a number in JSON");
    });

    it("should throw error for missing number in positive exponent part", () => {
      const digester = new SequenceDigester(function* () {
        yield* readNumber();
      });

      digester.write("123E+");
      expect(() => digester.close()).toThrow("Exponent part is missing a number in JSON");
    });

    it("should throw error when minus sign followed by non-digit", () => {
      const digester = new SequenceDigester(function* () {
        yield* readNumber();
      });

      expect(() => digester.write("-a")).toThrow("No number after minus sign in JSON");
    });

    it("should handle edge case with decimal point followed by non-digit", () => {
      const digester = new SequenceDigester(function* () {
        const number = yield* readNumber();
        expect(number).toBe(null);
        const remaining = yield 1;
        expect(remaining).toBe(".");
      });

      digester.write(".");
    });

    it("should handle edge case with exponent followed by non-digit", () => {
      const digester = new SequenceDigester(function* () {
        const number = yield* readNumber();
        expect(number).toBe(null);
        const remaining = yield 1;
        expect(remaining).toBe("E");
      });

      digester.write("E");
    });
  });

  describe("JSON parsing edge cases", () => {
    it("should handle empty string input", () => {
      const digester = new SequenceDigester(function* () {
        const result = yield* readJsonString();
        expect(result).toBe("");
      });

      digester.write('""');
    });

    it("should handle string with only escape sequences", () => {
      const digester = new SequenceDigester(function* () {
        const result = yield* readJsonString();
        expect(result).toBe('\n\t\r"\\');
      });

      digester.write('"\\n\\t\\r\\"\\\\"');
    });

    it("should handle complex nested JSON", () => {
      const complexJson = {
        users: [
          { name: "John", age: 30, active: true },
          { name: "Jane", age: 25, active: false },
        ],
        metadata: {
          count: 2,
          timestamp: 1234567890,
        },
      };

      const digester = new SequenceDigester(function* () {
        const result = yield* readJsonValue();
        expect(result).toEqual(complexJson);
      });

      digester.write(JSON.stringify(complexJson));
    });

    it("should handle malformed JSON gracefully", () => {
      const digester = new SequenceDigester(function* () {
        yield* readJsonValue();
      });
      digester.write('{"name": "John"');

      expect(() => digester.close()).toThrow("Expected ',' or '}' but got EOF");
    });

    it("should handle streaming of large JSON objects", () => {
      const largeObject = {
        data: Array.from({ length: 100 }, (_, i) => ({ id: i, value: `item${i}` })),
      };

      const digester = new SequenceDigester(function* () {
        const result = yield* readJsonValue();
        expect(result).toEqual(largeObject);
      });

      const jsonString = JSON.stringify(largeObject);
      // Split into chunks to simulate streaming
      const chunkSize = 50;
      for (let i = 0; i < jsonString.length; i += chunkSize) {
        digester.write(jsonString.slice(i, i + chunkSize));
      }
    });
  });
});
