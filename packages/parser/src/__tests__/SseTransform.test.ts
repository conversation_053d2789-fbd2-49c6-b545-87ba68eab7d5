import { SseTransform, SseEvent } from "../SseTransform";

describe("SseTransform", () => {
  let transform: SseTransform;
  let events: SseEvent[];

  beforeEach(() => {
    events = [];
    transform = new SseTransform();
    const reader = transform.readable.getReader();

    // 读取所有事件
    (async () => {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        events.push(value);
      }
    })();
  });

  it("should parse simple data events", async () => {
    const writer = transform.writable.getWriter();
    await writer.write("data: hello\n\n");
    await writer.close();

    expect(events).toHaveLength(1);
    expect(events[0]).toEqual({
      data: "hello",
    });
  });

  it("should parse multiple data lines", async () => {
    const writer = transform.writable.getWriter();
    await writer.write("data: line1\ndata: line2\n\n");
    await writer.close();

    expect(events).toHaveLength(1);
    expect(events[0]).toEqual({
      data: "line1\nline2",
    });
  });

  it("should parse event type", async () => {
    const writer = transform.writable.getWriter();
    await writer.write("event: custom\ndata: message\n\n");
    await writer.close();

    expect(events).toHaveLength(1);
    expect(events[0]).toEqual({
      event: "custom",
      data: "message",
    });
  });

  it("should parse id field", async () => {
    const writer = transform.writable.getWriter();
    await writer.write("id: 123\ndata: message\n\n");
    await writer.close();

    expect(events).toHaveLength(1);
    expect(events[0]).toEqual({
      id: "123",
      data: "message",
    });
  });

  it("should parse retry field", async () => {
    const writer = transform.writable.getWriter();
    await writer.write("retry: 5000\ndata: message\n\n");
    await writer.close();

    expect(events).toHaveLength(1);
    expect(events[0]).toEqual({
      retry: "5000",
      data: "message",
    });
  });

  it("should parse comments", async () => {
    const writer = transform.writable.getWriter();
    await writer.write(": this is a comment\ndata: message\n\n");
    await writer.close();

    expect(events).toHaveLength(1);
    expect(events[0]).toEqual({
      comment: " this is a comment",
      data: "message",
    });
  });

  it("should handle multiple events", async () => {
    const writer = transform.writable.getWriter();
    await writer.write("data: message1\n\ndata: message2\n\n");
    await writer.close();

    expect(events).toHaveLength(2);
    expect(events[0]).toEqual({
      data: "message1",
    });
    expect(events[1]).toEqual({
      data: "message2",
    });
  });

  it("should handle CRLF line endings", async () => {
    const writer = transform.writable.getWriter();
    await writer.write("data: message\r\n\r\n");
    await writer.close();

    expect(events).toHaveLength(1);
    expect(events[0]).toEqual({
      data: "message",
    });
  });

  it("should handle field without space after colon", async () => {
    const writer = transform.writable.getWriter();
    await writer.write("data:hello\n\n");
    await writer.close();

    expect(events).toHaveLength(1);
    expect(events[0]).toEqual({
      data: "hello",
    });
  });

  it("should handle field with multiple spaces after colon", async () => {
    const writer = transform.writable.getWriter();
    await writer.write("data:   hello\n\n");
    await writer.close();

    expect(events).toHaveLength(1);
    expect(events[0]).toEqual({
      data: "  hello",
    });
  });
});
