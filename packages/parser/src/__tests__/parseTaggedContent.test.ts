import { parseTaggedContent, ContentfulSection } from "../parseTaggedContent";
import { BufferState, LiveBuffer } from "../IterableBuffer";

const valueOfSections = (sections: readonly ContentfulSection[]) => {
  return sections.map((s) => {
    if (s.type === "content" || s.type === "think") {
      return [s.type, s.value.buffer.join("")];
    }
    if (s.type === "call") {
      const { name, arguments: args } = s.value;
      return [s.type, name.buffer.join(""), args.buffer.join("")];
    }
    throw new Error(`Unknown section type: ${s.type}`);
  });
};

describe("parseContent", () => {
  describe("basic functionality", () => {
    it("should parse plain text content", async () => {
      const input = new LiveBuffer<string>();
      input.enqueue("Hello world");
      input.close();

      const result = parseTaggedContent(input);
      const sections = await result;

      expect(sections).toHaveLength(1);
      expect(valueOfSections(sections)).toEqual([["content", "Hello world"]]);
    });

    it("should parse empty content", async () => {
      const input = new LiveBuffer<string>();
      input.close();

      const result = parseTaggedContent(input);
      const sections = await result;

      expect(sections).toHaveLength(0);
    });

    it("should handle multiple content sections", async () => {
      const input = new LiveBuffer<string>();
      input.enqueue("First section");
      input.enqueue("Second section");
      input.close();

      const result = parseTaggedContent(input);
      const sections = await result;

      expect(sections).toHaveLength(1);
      expect(valueOfSections(sections)).toEqual([["content", "First sectionSecond section"]]);
    });
  });

  describe("think sections", () => {
    it("should parse think sections", async () => {
      const input = new LiveBuffer<string>();
      input.enqueue("<think>");
      input.enqueue("This is a thought");
      input.enqueue("</think>");
      input.close();

      const result = parseTaggedContent(input);
      const sections = await result;

      expect(sections).toHaveLength(1);
      expect(valueOfSections(sections)).toEqual([["think", "This is a thought"]]);
    });

    it("should parse think sections with multiple lines", async () => {
      const input = new LiveBuffer<string>();
      input.enqueue("<think>");
      input.enqueue("Line 1");
      input.enqueue("Line 2");
      input.enqueue("Line 3");
      input.enqueue("</think>");
      input.close();

      const result = parseTaggedContent(input);
      const sections = await result;

      expect(sections).toHaveLength(1);
      expect(valueOfSections(sections)).toEqual([["think", "Line 1Line 2Line 3"]]);
    });

    it("should handle content before and after think sections", async () => {
      const input = new LiveBuffer<string>();
      input.enqueue("Before think");
      input.enqueue("<think>");
      input.enqueue("Inside think");
      input.enqueue("</think>");
      input.enqueue("After think");
      input.close();

      const result = parseTaggedContent(input);
      const sections = await result;

      expect(sections).toHaveLength(3);

      expect(valueOfSections(sections)).toEqual([
        ["content", "Before think"],
        ["think", "Inside think"],
        ["content", "After think"],
      ]);
    });
  });

  describe("tool_call sections", () => {
    it("should parse tool_call sections with name and arguments", async () => {
      const input = new LiveBuffer<string>();
      input.enqueue("<tool_call>");
      input.enqueue(' { "name": "test_tool", "arguments": "test_args" }');
      input.enqueue("</tool_call>");
      input.close();

      const result = parseTaggedContent(input);
      const sections = await result;

      expect(sections).toHaveLength(1);

      expect(valueOfSections(sections)).toEqual([["call", "test_tool", "test_args"]]);
    });

    it("should parse tool_call sections without JSON object", async () => {
      const input = new LiveBuffer<string>();
      input.enqueue("<tool_call>");
      input.enqueue("Some content");
      input.enqueue("</tool_call>");
      input.close();

      const originalWarn = console.warn;
      console.warn = jest.fn();

      const result = parseTaggedContent(input);
      const sections = await result;

      expect(console.warn).toHaveBeenCalledWith('Unexpected content "Some content" before </tool_call> tag');
      console.warn = originalWarn;

      expect(sections).toHaveLength(1);
      expect(valueOfSections(sections)).toEqual([["call", "", ""]]);
    });

    it("should handle tool_call with complex JSON", async () => {
      const input = new LiveBuffer<string>();
      input.enqueue("<tool_call>");
      input.enqueue(' { "name": "complex_tool", "arguments": "{\\"key\\": \\"value\\"}" }');
      input.enqueue("</tool_call>");
      input.close();

      const result = parseTaggedContent(input);
      const sections = await result;

      expect(sections).toHaveLength(1);
      expect(valueOfSections(sections)).toEqual([["call", "complex_tool", '{"key": "value"}']]);
    });
  });

  describe("mixed content", () => {
    it("should parse mixed content with think and tool_call sections", async () => {
      const input = new LiveBuffer<string>();
      input.enqueue("Start content");
      input.enqueue("<think>");
      input.enqueue("Thinking...");
      input.enqueue("</think>");
      input.enqueue("Middle content");
      input.enqueue("<tool_call>");
      input.enqueue(' { "name": "test", "arguments": "args" }');
      input.enqueue("</tool_call>");
      input.enqueue("End content");
      input.close();

      const result = parseTaggedContent(input);
      const sections = await result;

      expect(sections).toHaveLength(5);

      expect(valueOfSections(sections)).toEqual([
        ["content", "Start content"],
        ["think", "Thinking..."],
        ["content", "Middle content"],
        ["call", "test", "args"],
        ["content", "End content"],
      ]);
    });

    it("should handle consecutive think sections", async () => {
      const input = new LiveBuffer<string>();
      input.enqueue("<think>");
      input.enqueue("First thought");
      input.enqueue("</think>");
      input.enqueue("<think>");
      input.enqueue("Second thought");
      input.enqueue("</think>");
      input.close();

      const result = parseTaggedContent(input);
      const sections = await result;

      expect(sections).toHaveLength(2);
      expect(valueOfSections(sections)).toEqual([
        ["think", "First thought"],
        ["think", "Second thought"],
      ]);
    });

    it("should handle consecutive tool_call sections", async () => {
      const input = new LiveBuffer<string>();
      input.enqueue("<tool_call>");
      input.enqueue(' { "name": "tool1", "arguments": "args1" }');
      input.enqueue("</tool_call>");
      input.enqueue("<tool_call>");
      input.enqueue(' { "name": "tool2", "arguments": "args2" }');
      input.enqueue("</tool_call>");
      input.close();

      const result = parseTaggedContent(input);
      const sections = await result;

      expect(sections).toHaveLength(2);

      expect(valueOfSections(sections)).toEqual([
        ["call", "tool1", "args1"],
        ["call", "tool2", "args2"],
      ]);
    });
  });

  describe("edge cases", () => {
    it("should handle incomplete think section", async () => {
      const input = new LiveBuffer<string>();
      input.enqueue("<think>");
      input.enqueue("Incomplete thought");
      input.close();

      const result = parseTaggedContent(input);
      const sections = await result;

      expect(sections).toHaveLength(1);
      expect(valueOfSections(sections)).toEqual([["think", "Incomplete thought"]]);
    });

    it("should handle incomplete tool_call section", async () => {
      const input = new LiveBuffer<string>();
      input.enqueue("<tool_call>");
      input.enqueue(' { "name": "incomplete"');
      input.close();

      const originalWarn = console.warn;
      console.warn = jest.fn();

      const result = parseTaggedContent(input);
      const sections = await result;

      expect(console.warn).toHaveBeenCalledWith("Cannot parse JSON in <tool_call> tag", expect.any(Error));
      console.warn = originalWarn;

      expect(sections).toHaveLength(1);
      expect(valueOfSections(sections)).toEqual([["call", "incomplete", ""]]);
    });

    it("should ignore empty think section", async () => {
      const input = new LiveBuffer<string>();
      input.enqueue("<think>");
      input.enqueue("</think>");
      input.close();

      const result = parseTaggedContent(input);
      const sections = await result;

      expect(valueOfSections(sections)).toEqual([]);
    });

    it("should ignore blnak think section", async () => {
      const input = new LiveBuffer<string>();
      input.enqueue("<think>");
      input.enqueue("   ");
      input.enqueue("</think>");
      input.close();

      const result = parseTaggedContent(input);
      const sections = await result;

      expect(valueOfSections(sections)).toEqual([]);
    });

    it("should handle empty tool_call section", async () => {
      const input = new LiveBuffer<string>();
      input.enqueue("<tool_call>");
      input.enqueue("</tool_call>");
      input.close();

      const result = parseTaggedContent(input);
      const sections = await result;
      expect(valueOfSections(sections)).toEqual([["call", "", ""]]);
    });

    it("should handle malformed JSON in tool_call", async () => {
      const input = new LiveBuffer<string>();
      input.enqueue("<tool_call>");
      input.enqueue(' { "name": "test", "arguments": }'); // Malformed JSON
      input.enqueue("</tool_call>");
      input.close();

      const originalWarn = console.warn;
      console.warn = jest.fn();

      const result = parseTaggedContent(input);
      const sections = await result;

      expect(console.warn).toHaveBeenCalledWith("Cannot parse JSON in <tool_call> tag", expect.any(Error));
      console.warn = originalWarn;

      expect(valueOfSections(sections)).toEqual([["call", "test", ""]]);
    });
  });

  describe("streaming behavior", () => {
    it("should handle streaming input", async () => {
      const input = new LiveBuffer<string>();
      const result = parseTaggedContent(input);

      // Write content in chunks
      input.enqueue("Hello");
      input.enqueue(" ");
      input.enqueue("world");
      input.close();

      const sections = await result;
      expect(sections).toHaveLength(1);
      expect(valueOfSections(sections)).toEqual([["content", "Hello world"]]);
    });

    it("should handle streaming with mixed sections", async () => {
      const input = new LiveBuffer<string>();
      const result = parseTaggedContent(input);

      // Write in chunks
      input.enqueue("Start");
      input.enqueue("<think>");
      input.enqueue("Thinking");
      input.enqueue("</think>");
      input.enqueue("End");
      input.close();

      const sections = await result;
      expect(sections).toHaveLength(3);

      expect(valueOfSections(sections)).toEqual([
        ["content", "Start"],
        ["think", "Thinking"],
        ["content", "End"],
      ]);
    });

    it("should handle streaming input with tool_call and chunked JSON", async () => {
      const input = new LiveBuffer<string>();
      const result = parseTaggedContent(input);

      // Write in chunks
      input.enqueue("Start");
      input.enqueue("<think>");
      input.enqueue("Think");

      expect(valueOfSections(result.buffer)).toEqual([
        ["content", "Start"],
        ["think", "Think"],
      ]);

      input.enqueue("ing");
      input.enqueue("</think>");
      input.enqueue("End");

      expect(valueOfSections(result.buffer)).toEqual([
        ["content", "Start"],
        ["think", "Thinking"],
        ["content", "End"],
      ]);

      input.enqueue("<tool_call>");
      input.enqueue('{ "name": "te');

      const tc = result.buffer[3];
      if (tc?.type !== "call") throw new Error("Expected call section");

      expect(valueOfSections(result.buffer)).toEqual([
        ["content", "Start"],
        ["think", "Thinking"],
        ["content", "End"],
        ["call", "te", ""],
      ]);
      expect(tc.value.name.state).toBe(BufferState.Open);
      expect(tc.value.arguments.state).toBe(BufferState.Open);

      input.enqueue('st", "arguments": "ar');

      expect(valueOfSections(result.buffer)).toEqual([
        ["content", "Start"],
        ["think", "Thinking"],
        ["content", "End"],
        ["call", "test", "ar"],
      ]);
      expect(tc.value.name.state).toBe(BufferState.Closed);
      expect(tc.value.arguments.state).toBe(BufferState.Open);

      input.enqueue('gs"');

      expect(tc.value.name.state).toBe(BufferState.Closed);
      expect(tc.value.arguments.state).toBe(BufferState.Closed);

      input.enqueue("}");

      expect(valueOfSections(result.buffer)).toEqual([
        ["content", "Start"],
        ["think", "Thinking"],
        ["content", "End"],
        ["call", "test", "args"],
      ]);

      input.enqueue("</tool_call>");

      expect(result.state).toBe(BufferState.Open);

      input.close();

      expect(result.state).toBe(BufferState.Closed);

      expect(valueOfSections(result.buffer)).toEqual([
        ["content", "Start"],
        ["think", "Thinking"],
        ["content", "End"],
        ["call", "test", "args"],
      ]);
    });

    it("should handle streaming input with tool_call and illeagal JSON", async () => {
      const input = new LiveBuffer<string>();
      const result = parseTaggedContent(input);

      input.enqueue("<tool_call>");
      input.enqueue('{ "name": "te');

      const tc = result.buffer[0];
      if (tc?.type !== "call") throw new Error("Expected call section");

      expect(valueOfSections(result.buffer)).toEqual([["call", "te", ""]]);
      expect(tc.value.name.state).toBe(BufferState.Open);
      expect(tc.value.arguments.state).toBe(BufferState.Open);

      input.enqueue('st", "arguments": "ar');

      expect(valueOfSections(result.buffer)).toEqual([["call", "test", "ar"]]);
      expect(tc.value.name.state).toBe(BufferState.Closed);
      expect(tc.value.arguments.state).toBe(BufferState.Open);

      input.enqueue("gs");

      expect(tc.value.name.state).toBe(BufferState.Closed);
      expect(tc.value.arguments.state).toBe(BufferState.Open);

      const originalWarn = console.warn;
      console.warn = jest.fn();

      input.enqueue("\n}");

      expect(console.warn).toHaveBeenCalledWith("Cannot parse JSON in <tool_call> tag", expect.any(Error));

      expect(tc.value.name.state).toBe(BufferState.Closed);

      expect(tc.value.arguments.state).toBe(BufferState.Error);

      expect(valueOfSections(result.buffer)).toEqual([["call", "test", "args"]]);

      expect(result.state).toBe(BufferState.Open);

      console.warn = jest.fn();

      input.close();

      expect(console.warn).toHaveBeenCalledWith('Unexpected content "}" before </tool_call> tag');
      console.warn = originalWarn;

      expect(result.state).toBe(BufferState.Closed);
    });
  });

  describe("error handling", () => {
    it("should propagate errors from input", async () => {
      const input = new LiveBuffer<string>();
      const result = parseTaggedContent(input);

      input.enqueue("Some content");
      input.error(new Error("Test error"));

      await expect(result).rejects.toThrow("Test error");
    });

    it("should handle error after partial content", async () => {
      const input = new LiveBuffer<string>();
      const result = parseTaggedContent(input);

      input.enqueue("<think>");
      input.enqueue("Partial thought");
      input.error(new Error("Test error"));

      await expect(result).rejects.toThrow("Test error");
    });
  });

  describe("type definitions", () => {
    it("should return correct types for content sections", () => {
      const input = new LiveBuffer<string>();
      input.enqueue("test");
      input.close();

      const result = parseTaggedContent(input);
      const sections = result as AsyncIterable<ContentfulSection>;

      expect(sections).toBeDefined();
    });

    it("should have correct structure for think sections", async () => {
      const input = new LiveBuffer<string>();
      input.enqueue("<think>");
      input.enqueue("test");
      input.enqueue("</think>");
      input.close();

      const result = parseTaggedContent(input);
      const sections = await result;

      expect(valueOfSections(sections)).toEqual([["think", "test"]]);
    });

    it("should have correct structure for call sections", async () => {
      const input = new LiveBuffer<string>();
      input.enqueue("<tool_call>");
      input.enqueue(' { "name": "test", "arguments": "args" }');
      input.enqueue("</tool_call>");
      input.close();

      const result = parseTaggedContent(input);
      const sections = await result;

      expect(valueOfSections(sections)).toEqual([["call", "test", "args"]]);
    });
  });
});
