import { BufferState, LiveBuffer } from "../IterableBuffer";

describe("LiveBuffer", () => {
  describe("initialization", () => {
    it("should initialize with empty buffer and state Open", () => {
      const buffer = new LiveBuffer<number>();
      expect(buffer.buffer).toEqual([]);
      expect(buffer.state).toBe(BufferState.Open);
    });
  });

  describe("write", () => {
    it("should add items to buffer", () => {
      const buffer = new LiveBuffer<number>();
      buffer.enqueue(1);
      buffer.enqueue(2);
      expect(buffer.buffer).toEqual([1, 2]);
    });

    it("should throw error when writing to closed buffer", () => {
      const buffer = new LiveBuffer<number>();
      buffer.close();
      expect(() => buffer.enqueue(1)).toThrow("Cannot call `enqueue` on a closed LiveBuffer");
    });

    it("should throw error when writing to errored buffer", () => {
      const buffer = new LiveBuffer<number>();
      buffer.error("test error");
      expect(() => buffer.enqueue(1)).toThrow("Cannot call `enqueue` on a closed LiveBuffer");
    });
  });

  describe("close", () => {
    it("should change state to Closed", () => {
      const buffer = new LiveBuffer<number>();
      buffer.close();
      expect(buffer.state).toBe(BufferState.Closed);
    });

    it("should throw error when closing already closed buffer", () => {
      const buffer = new LiveBuffer<number>();
      buffer.close();
      expect(() => buffer.close()).toThrow("Cannot call `close` on a closed LiveBuffer");
    });
  });

  describe("error", () => {
    it("should change state to Error and set reason", () => {
      const buffer = new LiveBuffer<number>();
      const error = new Error("test error");
      buffer.error(error);
      expect(buffer.state).toBe(BufferState.Error);
      expect(buffer.reason).toBe(error);
    });

    it("should throw error when calling error on closed buffer", () => {
      const buffer = new LiveBuffer<number>();
      buffer.close();
      expect(() => buffer.error("test")).toThrow("Cannot call `error` on a closed LiveBuffer");
    });
  });

  describe("stream", () => {
    it("should create a readable stream with existing items", async () => {
      const buffer = new LiveBuffer<number>();
      buffer.enqueue(1);
      buffer.enqueue(2);
      buffer.close();

      const stream = buffer.stream();
      const reader = stream.getReader();

      const result1 = await reader.read();
      expect(result1.value).toBe(1);
      expect(result1.done).toBe(false);

      const result2 = await reader.read();
      expect(result2.value).toBe(2);
      expect(result2.done).toBe(false);

      const result3 = await reader.read();
      expect(result3.done).toBe(true);
    });

    it("should close stream when buffer is closed", async () => {
      const buffer = new LiveBuffer<number>();
      buffer.enqueue(1);
      buffer.close();

      const stream = buffer.stream();
      const reader = stream.getReader();

      const result1 = await reader.read();
      expect(result1.value).toBe(1);
      expect(result1.done).toBe(false);

      const result2 = await reader.read();
      expect(result2.done).toBe(true);
    });

    it("should error stream when buffer is errored", async () => {
      const buffer = new LiveBuffer<number>();
      const error = new Error("test error");
      buffer.error(error);

      const stream = buffer.stream();
      const reader = stream.getReader();

      await expect(reader.read()).rejects.toThrow("test error");
    });
  });

  describe("promise", () => {
    it("should resolve with buffer contents when closed", async () => {
      const buffer = new LiveBuffer<number>();
      buffer.enqueue(1);
      buffer.enqueue(2);
      buffer.close();

      const result = await buffer;
      expect(result).toEqual([1, 2]);
    });

    it("should reject with error when buffer is errored", async () => {
      const buffer = new LiveBuffer<number>();
      const error = new Error("test error");
      buffer.error(error);

      await expect(buffer).rejects.toThrow("test error");
    });

    it("should wait for stream to complete when in state Open", async () => {
      const buffer = new LiveBuffer<number>();
      buffer.enqueue(1);
      buffer.enqueue(2);

      const promise = buffer;
      buffer.close();

      const result = await promise;
      expect(result).toEqual([1, 2]);
    });
  });

  describe("tee", () => {
    it("should send existing buffer items to controller and add to waitings when buffer is Open", () => {
      const buffer = new LiveBuffer<number>();
      buffer.enqueue(1);
      buffer.enqueue(2);

      const controller = {
        enqueue: jest.fn(),
        close: jest.fn(),
        error: jest.fn(),
      };

      buffer.tee(controller);

      // Should send existing items
      expect(controller.enqueue).toHaveBeenCalledWith(1);
      expect(controller.enqueue).toHaveBeenCalledWith(2);
      expect(controller.enqueue).toHaveBeenCalledTimes(2);
      expect(controller.close).not.toHaveBeenCalled();
      expect(controller.error).not.toHaveBeenCalled();

      // Should add controller to waitings
      buffer.enqueue(3);
      expect(controller.enqueue).toHaveBeenCalledWith(3);
      expect(controller.enqueue).toHaveBeenCalledTimes(3);
    });

    it("should send existing buffer items and close controller when buffer is Closed", () => {
      const buffer = new LiveBuffer<number>();
      buffer.enqueue(1);
      buffer.enqueue(2);
      buffer.close();

      const controller = {
        enqueue: jest.fn(),
        close: jest.fn(),
        error: jest.fn(),
      };

      buffer.tee(controller);

      // Should send existing items
      expect(controller.enqueue).toHaveBeenCalledWith(1);
      expect(controller.enqueue).toHaveBeenCalledWith(2);
      expect(controller.enqueue).toHaveBeenCalledTimes(2);

      // Should close controller
      expect(controller.close).toHaveBeenCalledTimes(1);
      expect(controller.error).not.toHaveBeenCalled();
    });

    it("should send existing buffer items and error controller when buffer is Error", () => {
      const buffer = new LiveBuffer<number>();
      buffer.enqueue(1);
      buffer.enqueue(2);
      const error = new Error("test error");
      buffer.error(error);

      const controller = {
        enqueue: jest.fn(),
        close: jest.fn(),
        error: jest.fn(),
      };

      buffer.tee(controller);

      // Should send existing items
      expect(controller.enqueue).toHaveBeenCalledWith(1);
      expect(controller.enqueue).toHaveBeenCalledWith(2);
      expect(controller.enqueue).toHaveBeenCalledTimes(2);

      // Should error controller with reason
      expect(controller.error).toHaveBeenCalledWith(error);
      expect(controller.error).toHaveBeenCalledTimes(1);
      expect(controller.close).not.toHaveBeenCalled();
    });

    it("should handle controller with missing optional methods", () => {
      const buffer = new LiveBuffer<number>();
      buffer.enqueue(1);
      buffer.close();

      const controller = {
        enqueue: jest.fn(),
        // close and error are missing
      };

      // Should not throw error
      expect(() => buffer.tee(controller)).not.toThrow();

      // Should still call enqueue
      expect(controller.enqueue).toHaveBeenCalledWith(1);
    });

    it("should handle empty buffer correctly", () => {
      const buffer = new LiveBuffer<number>();

      const controller = {
        enqueue: jest.fn(),
        close: jest.fn(),
        error: jest.fn(),
      };

      buffer.tee(controller);

      // Should not call enqueue for empty buffer
      expect(controller.enqueue).not.toHaveBeenCalled();

      // Should add to waitings since buffer is Open
      buffer.enqueue(1);
      expect(controller.enqueue).toHaveBeenCalledWith(1);
    });

    it("should handle controller with only enqueue method", () => {
      const buffer = new LiveBuffer<number>();
      buffer.enqueue(1);
      buffer.close();

      const controller = {
        enqueue: jest.fn(),
      };

      buffer.tee(controller);

      expect(controller.enqueue).toHaveBeenCalledWith(1);
      // Should not throw when close is undefined
    });

    it("should handle controller with only close method", () => {
      const buffer = new LiveBuffer<number>();
      buffer.close();

      const controller = {
        close: jest.fn(),
      };

      buffer.tee(controller);

      expect(controller.close).toHaveBeenCalledTimes(1);
      // Should not throw when enqueue is undefined
    });

    it("should handle controller with only error method", () => {
      const buffer = new LiveBuffer<number>();
      const error = new Error("test error");
      buffer.error(error);

      const controller = {
        error: jest.fn(),
      };

      buffer.tee(controller);

      expect(controller.error).toHaveBeenCalledWith(error);
      // Should not throw when enqueue is undefined
    });
  });
});
