import { paragraphing, ParagraphType, Paragraph } from "../Wukong/Paragraphing";
import { LiveBuffer } from "../IterableBuffer";
import { SequenceDigester } from "../SequenceDigest";

describe("Paragraphing", () => {
  describe("basic paragraph types", () => {
    it("should create Answer paragraph by default", () => {
      const receiver = new LiveBuffer<Paragraph>();
      const digester = new SequenceDigester(() => paragraphing(receiver));

      digester.write("This is an answer paragraph");
      digester.close();

      expect(receiver.buffer).toHaveLength(1);
      expect(receiver.buffer[0].type).toBe(ParagraphType.Answer);
      expect(receiver.buffer[0].value.buffer.join("")).toBe("This is an answer paragraph");
    });

    it("should create Action paragraph when Action: prefix is found", () => {
      const receiver = new LiveBuffer<Paragraph>();
      const digester = new SequenceDigester(() => paragraphing(receiver));

      digester.write("Action: search for information");
      digester.close();

      expect(receiver.buffer).toHaveLength(1);
      expect(receiver.buffer[0].type).toBe(ParagraphType.Action);
      expect(receiver.buffer[0].value.buffer.join("")).toBe("search for information");
    });

    it("should create ActionInput paragraph when Action Input: prefix is found", () => {
      const receiver = new LiveBuffer<Paragraph>();
      const digester = new SequenceDigester(() => paragraphing(receiver));

      digester.write("Action Input: query parameter");
      digester.close();

      expect(receiver.buffer).toHaveLength(1);
      expect(receiver.buffer[0].type).toBe(ParagraphType.ActionInput);
      expect(receiver.buffer[0].value.buffer.join("")).toBe("query parameter");
    });

    it("should create Observation paragraph when Observation: prefix is found", () => {
      const receiver = new LiveBuffer<Paragraph>();
      const digester = new SequenceDigester(() => paragraphing(receiver));

      digester.write("Observation: search results found");
      digester.close();

      expect(receiver.buffer).toHaveLength(1);
      expect(receiver.buffer[0].type).toBe(ParagraphType.Observation);
      expect(receiver.buffer[0].value.buffer.join("")).toBe("search results found");
    });
  });

  describe("single line paragraph types", () => {
    it("should close Action paragraph after single line", () => {
      const receiver = new LiveBuffer<Paragraph>();
      const digester = new SequenceDigester(() => paragraphing(receiver));

      digester.write("Action: search\n");
      digester.close();

      expect(receiver.buffer).toHaveLength(1);
      expect(receiver.buffer[0].type).toBe(ParagraphType.Action);
      expect(receiver.buffer[0].value.buffer.join("")).toBe("search");
      expect(receiver.buffer[0].value.state).toBe(1); // closed
    });

    it("should close ActionInput paragraph after single line", () => {
      const receiver = new LiveBuffer<Paragraph>();
      const digester = new SequenceDigester(() => paragraphing(receiver));

      digester.write("Action Input: query\n");
      digester.close();

      expect(receiver.buffer).toHaveLength(1);
      expect(receiver.buffer[0].type).toBe(ParagraphType.ActionInput);
      expect(receiver.buffer[0].value.buffer.join("")).toBe("query");
      expect(receiver.buffer[0].value.state).toBe(1); // closed
    });

    it("should close Observation paragraph after single line", () => {
      const receiver = new LiveBuffer<Paragraph>();
      const digester = new SequenceDigester(() => paragraphing(receiver));

      digester.write("Observation: result\n");
      digester.close();

      expect(receiver.buffer).toHaveLength(1);
      expect(receiver.buffer[0].type).toBe(ParagraphType.Observation);
      expect(receiver.buffer[0].value.buffer.join("")).toBe("result");
      expect(receiver.buffer[0].value.state).toBe(1); // closed
    });
  });

  describe("multi-line Answer paragraphs", () => {
    it("should continue Answer paragraph across multiple lines", () => {
      const receiver = new LiveBuffer<Paragraph>();
      const digester = new SequenceDigester(() => paragraphing(receiver));

      digester.write("This is line 1\n");
      digester.write("This is line 2\n");
      digester.write("This is line 3");
      digester.close();

      expect(receiver.buffer).toHaveLength(1);
      expect(receiver.buffer[0].type).toBe(ParagraphType.Answer);
      expect(receiver.buffer[0].value.buffer.join("")).toBe("This is line 1\nThis is line 2\nThis is line 3");
    });

    it("should continue Answer paragraph when no strong labels are found", () => {
      const receiver = new LiveBuffer<Paragraph>();
      const digester = new SequenceDigester(() => paragraphing(receiver));

      digester.write("Answer content\n");
      digester.write("More content\n");
      digester.write("Final content");
      digester.close();

      expect(receiver.buffer).toHaveLength(1);
      expect(receiver.buffer[0].type).toBe(ParagraphType.Answer);
      expect(receiver.buffer[0].value.buffer.join("")).toBe("Answer content\nMore content\nFinal content");
    });
  });

  describe("paragraph transitions", () => {
    it("should transition from Answer to Action when Action: is found", () => {
      const receiver = new LiveBuffer<Paragraph>();
      const digester = new SequenceDigester(() => paragraphing(receiver));

      digester.write("Answer content\n");
      digester.write("Action: perform action");
      digester.close();

      expect(receiver.buffer).toHaveLength(2);
      expect(receiver.buffer[0].type).toBe(ParagraphType.Answer);
      expect(receiver.buffer[0].value.buffer.join("")).toBe("Answer content\n");
      expect(receiver.buffer[1].type).toBe(ParagraphType.Action);
      expect(receiver.buffer[1].value.buffer.join("")).toBe("perform action");
    });

    it("should transition from Answer to Observation when Observation: is found after newline", () => {
      const receiver = new LiveBuffer<Paragraph>();
      const digester = new SequenceDigester(() => paragraphing(receiver));

      digester.write("Answer content\n");
      digester.write("Observation: observation result");
      digester.close();

      expect(receiver.buffer).toHaveLength(2);
      expect(receiver.buffer[0].type).toEqual(ParagraphType.Answer);
      expect(receiver.buffer[0].value.buffer.join("")).toBe("Answer content\n");
      expect(receiver.buffer[1].type).toEqual(ParagraphType.Observation);
      expect(receiver.buffer[1].value.buffer.join("")).toBe("observation result");
    });

    it("should not transition on weak labels within the same line", () => {
      const receiver = new LiveBuffer<Paragraph>();
      const digester = new SequenceDigester(() => paragraphing(receiver));

      digester.write("Answer content Observation: this should not create new paragraph");
      digester.close();

      expect(receiver.buffer).toHaveLength(1);
      expect(receiver.buffer[0].type).toBe(ParagraphType.Answer);
      expect(receiver.buffer[0].value.buffer.join("")).toBe(
        "Answer content Observation: this should not create new paragraph"
      );
    });
  });

  describe("strong vs weak labels", () => {
    it("should prioritize strong labels (Action) over weak labels (Observation)", () => {
      const receiver = new LiveBuffer<Paragraph>();
      const digester = new SequenceDigester(() => paragraphing(receiver));

      digester.write("Answer content\n");
      digester.write("Action: strong action\n");
      digester.write("Observation: weak observation");
      digester.close();

      expect(receiver.buffer).toHaveLength(3);
      expect(receiver.buffer[0].type).toBe(ParagraphType.Answer);
      expect(receiver.buffer[1].type).toBe(ParagraphType.Action);
      expect(receiver.buffer[2].type).toBe(ParagraphType.Observation);
    });

    it("should handle multiple strong labels in sequence", () => {
      const receiver = new LiveBuffer<Paragraph>();
      const digester = new SequenceDigester(() => paragraphing(receiver));

      digester.write("Action: first action\n");
      digester.write("Action: second action");
      digester.close();

      expect(receiver.buffer).toHaveLength(2);
      expect(receiver.buffer[0].type).toBe(ParagraphType.Action);
      expect(receiver.buffer[0].value.buffer.join("")).toBe("first action");
      expect(receiver.buffer[1].type).toBe(ParagraphType.Action);
      expect(receiver.buffer[1].value.buffer.join("")).toBe("second action");
    });
  });

  describe("edge cases", () => {
    it("should handle empty input", () => {
      const receiver = new LiveBuffer<Paragraph>();
      const digester = new SequenceDigester(() => paragraphing(receiver));

      digester.close();

      expect(receiver.buffer).toHaveLength(0);
    });

    it("should handle whitespace-only input", () => {
      const receiver = new LiveBuffer<Paragraph>();
      const digester = new SequenceDigester(() => paragraphing(receiver));

      digester.write("   \n  \t  ");
      digester.close();

      expect(receiver.buffer).toHaveLength(1);
      expect(receiver.buffer[0].type).toBe(ParagraphType.Answer);
      expect(receiver.buffer[0].value.buffer.join("")).toBe("   \n  \t  ");
    });

    it("should handle labels with extra spaces", () => {
      const receiver = new LiveBuffer<Paragraph>();
      const digester = new SequenceDigester(() => paragraphing(receiver));

      digester.write("Action:  action with spaces");
      digester.close();

      expect(receiver.buffer).toHaveLength(1);
      expect(receiver.buffer[0].type).toBe(ParagraphType.Action);
      expect(receiver.buffer[0].value.buffer.join("")).toBe(" action with spaces");
    });

    it("should handle partial label matches", () => {
      const receiver = new LiveBuffer<Paragraph>();
      const digester = new SequenceDigester(() => paragraphing(receiver));

      digester.write("Act: this should not match Action");
      digester.close();

      expect(receiver.buffer).toHaveLength(1);
      expect(receiver.buffer[0].type).toBe(ParagraphType.Answer);
      expect(receiver.buffer[0].value.buffer.join("")).toBe("Act: this should not match Action");
    });

    // it("should handle invalid JSON gracefully", () => {
    //   const originalConsoleError = console.error;
    //   console.error = jest.fn();

    //   const receiver = new LiveBuffer<Paragraph>();
    //   const digester = new SequenceDigester(() => paragraphing(receiver));

    //   digester.write('[{"type":"text",invalid json}]');
    //   digester.close();

    //   expect(receiver.buffer).toHaveLength(1);
    //   expect(receiver.buffer[0].type).toBe(ParagraphType.Answer);
    //   expect(receiver.buffer[0].value.buffer.join("")).toContain("[❌Bad JSON]");

    //   // Restore original console.error
    //   console.error = originalConsoleError;
    // });
  });

  describe("complex scenarios", () => {
    it("should handle a complete conversation flow", () => {
      const receiver = new LiveBuffer<Paragraph>();
      const digester = new SequenceDigester(() => paragraphing(receiver));

      digester.write("I'll help you search for information.\n");
      digester.write("Action: search\n");
      digester.write("Action Input: query parameter\n");
      digester.write("Observation: search results found\n");
      digester.write("Based on the search results, here is the answer.");
      digester.close();

      expect(receiver.buffer).toHaveLength(5);
      expect(receiver.buffer[0].type).toBe(ParagraphType.Answer);
      expect(receiver.buffer[1].type).toBe(ParagraphType.Action);
      expect(receiver.buffer[2].type).toBe(ParagraphType.ActionInput);
      expect(receiver.buffer[3].type).toBe(ParagraphType.Observation);
      expect(receiver.buffer[4].type).toBe(ParagraphType.Answer);
    });

    it("should handle mixed content with JSON-like structures", () => {
      const receiver = new LiveBuffer<Paragraph>();
      const digester = new SequenceDigester(() => paragraphing(receiver));

      digester.write("Processing data:\n");
      digester.write('[{"type":"text","text":"sample data"}]\n');
      digester.write("Action: process\n");
      digester.write("Action Input: data parameter\n");
      digester.write("Observation: processing complete");
      digester.close();

      expect(receiver.buffer).toHaveLength(4);
      expect(receiver.buffer[0].type).toBe(ParagraphType.Answer);
      expect(receiver.buffer[1].type).toBe(ParagraphType.Action);
      expect(receiver.buffer[2].type).toBe(ParagraphType.ActionInput);
      expect(receiver.buffer[3].type).toBe(ParagraphType.Observation);
    });
  });

  describe("LiveBuffer integration", () => {
    it("should handle receiver buffer state correctly", () => {
      const receiver = new LiveBuffer<Paragraph>();
      const digester = new SequenceDigester(() => paragraphing(receiver));

      digester.write("Test content");
      digester.close();

      expect(receiver.state).toBe(0); // receiver should still be open for further processing
    });
  });
});
