import { Chat, ChatMessage, ChatMessageRole, ToolDeclarationFunction } from "./Chat";
import { DeepFreeze } from "./utils";
import { BufferState } from "./IterableBuffer";
import { ToolCallManager, ToolCallManagerOptions } from "./ToolCallManager";

export abstract class ChatBasic<T extends ToolCallManagerOptions> implements Chat<T> {
  public static fetch = window.fetch.bind(window);
  public readonly url: string;
  public readonly options: DeepFreeze<T>;
  public readonly messages: readonly ChatMessage[];
  public readonly toolCallManager: ToolCallManager;

  readonly #watchers: (() => void)[];

  constructor(url: string, options: DeepFreeze<T>, messages: readonly ChatMessage[]) {
    this.messages = messages;
    this.#watchers = [];
    this.url = url;
    this.options = options;
    this.toolCallManager = new ToolCallManager(options);
  }

  public get busy() {
    return this.lastMessage?.sections.state === BufferState.Open;
  }

  /**
   * @deprecated Don't care about the error state of the chat object.
   *             If you can handle the error, you can find it from item of sections.
   */
  public get error() {
    if (this.lastMessage?.sections.state === BufferState.Error) {
      return { reason: this.lastMessage.sections.reason };
    }
    return null;
  }

  abstract appendContentMessage(role: ChatMessageRole, content: string): void;
  abstract next(content: string): void;
  abstract rewindToBefore(index: ChatMessage): void;
  abstract abort(reason?: Error): void;
  abstract requestIfNeeded(): Promise<void>;

  public get lastMessage() {
    return this.messages[this.messages.length - 1] ?? null;
  }

  public watch(update: () => void) {
    this.#watchers.push(update);
  }

  public unwatch(update: () => void) {
    const index = this.#watchers.indexOf(update);
    if (index !== -1) this.#watchers.splice(index, 1);
  }

  protected notify() {
    for (const watcher of this.#watchers) watcher();
  }

  abstract registerNativeTool(
    declarationFunction: ToolDeclarationFunction,
    implement: (args: Record<string, unknown>) => Promise<unknown>
  ): void;

  abstract registerEnvironmentVariables(variables: Record<string, string>): void;
}
