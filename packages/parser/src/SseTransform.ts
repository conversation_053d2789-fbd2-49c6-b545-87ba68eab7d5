const knownFields = new Set(["data", "event", "id", "retry"] as const);
type KnownField = typeof knownFields extends Set<infer T> ? T : never;
const isKnownField = (s: any): s is KnownField => knownFields.has(s);

export interface SseEvent extends Partial<Record<KnownField, string>> {
  comment?: string;
}

// https://html.spec.whatwg.org/multipage/server-sent-events.html#parsing-an-event-stream
export class SseTransform extends TransformStream<string, SseEvent> {
  constructor() {
    let buffer = "";

    let event: SseEvent = {};
    let dataLines: string[] = [];
    const processLine = (line: string, controller: TransformStreamDefaultController<SseEvent>) => {
      // If the tail character is a CR, remove it.
      // This code is for compatibility with Windows processes that use CRLF.
      if (line.endsWith("\r")) line = line.slice(0, -1);

      // Empty line means the end of the event.
      // Send the event to the controller and reset the reading context
      if (line == "") {
        if (dataLines.length) event.data = dataLines.join("\n");
        controller.enqueue(event);
        event = {};
        dataLines = [];
        return;
      }

      const index = line.indexOf(":");
      if (index !== -1) {
        const name = line.slice(0, index);

        if (isKnownField(name)) {
          // If a space follows the colon, the value is the rest of the line.
          // For example:
          //
          // id: 233
          // data:   hello
          //
          // The id is "233" not " 233".
          // The data is "  233" not "  233".
          const value = line[index + 1] === " " ? line.slice(index + 2) : line.slice(index + 1);
          if (name === "data") {
            dataLines.push(value);
          } else {
            event[name] = value;
          }
        } else {
          // No space after colon, it's a comment.
          event.comment = line.slice(index + 1);
        }
      }
    };

    super({
      transform(chunk: string, controller: TransformStreamDefaultController<SseEvent>) {
        buffer += chunk;
        while (true) {
          const index = buffer.indexOf("\n");
          if (index === -1) break;
          const line = buffer.slice(0, index);
          buffer = buffer.slice(index + 1);
          processLine(line, controller);
        }
      },

      flush(controller: TransformStreamDefaultController<SseEvent>) {
        if (buffer) {
          processLine(buffer, controller);
        }
      },
    });
  }
}
