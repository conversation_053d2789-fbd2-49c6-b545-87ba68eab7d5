import { ToolDeclaration, ToolDeclarationFunction } from "../Chat";
import { IterableBuffer } from "../IterableBuffer";

export class ImplementedTool<T extends string> implements ToolDeclaration<T> {
  public readonly type: "function";
  public readonly implementation: (args: Record<T, unknown>) => Promise<unknown> | IterableBuffer<string>;
  public readonly function: ToolDeclaration<T>["function"];

  // The getter method will not be serialized.
  public get name() {
    return this.function.name;
  }

  constructor(
    declarationFunction: ToolDeclarationFunction<T>,
    implementation: (args: Record<T, unknown>) => Promise<unknown> | IterableBuffer<string>
  ) {
    this.type = "function";
    this.implementation = implementation;
    this.function = declarationFunction;
  }
}
