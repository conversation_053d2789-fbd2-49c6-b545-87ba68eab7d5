import { ToolDeclarationFunction, ApiToolOptions } from "../Chat";
import { buildApiTool } from "./buildApiTool";
import { ImplementedTool } from "./ImplementedTool";
import { IterableBuffer, LiveBuffer } from "../IterableBuffer";
import { mcpCall } from "./mcpCall";
import { McpToolCallError } from "../errors/McpToolCallError";

export interface ToolCallManagerOptions {
  /**
   * Register API tools.
   * NOTE: This is a extension property, it never be send to LLM.
   */
  readonly additional_api_tools?: readonly ApiToolOptions[];

  /**
   * Register MCP services.
   * NOTE: This is a extension property, it never be send to LLM.
   */
  readonly additional_mcp_servers?: readonly string[];

  /**
   * Select which native tools can be used.
   * NOTE: This is a extension property, it never be send to LLM.
   */
  readonly additional_native_tools?: readonly string[];
}

export class ToolCallManager {
  readonly #tools: ImplementedTool<string>[];
  readonly #allowedNativeTools: Set<string>;
  readonly #environmentVariables: Record<string, string>;
  readonly #allowedMcpServers: Set<string>;
  public readonly mcpServersInitializing: Promise<void> | null;

  constructor(options: ToolCallManagerOptions) {
    this.#tools = [];
    this.#allowedNativeTools = new Set();
    this.#allowedMcpServers = new Set();
    this.#environmentVariables = Object.create(null);

    if (options.additional_native_tools instanceof Array) {
      for (const tool of options.additional_native_tools) {
        if (typeof tool === "string") {
          this.#allowedNativeTools.add(tool);
        } else {
          console.warn("The `additional_native_tools` must be an array of strings, but got", tool);
        }
      }
    }

    if (options.additional_api_tools instanceof Array) {
      for (const tool of options.additional_api_tools) {
        if (typeof tool === "object" && tool !== null) {
          this.registerApiTool(tool);
        } else {
          console.warn("The `additional_api_tools` must be an array of objects, but got", tool);
        }
      }
    }

    if (options.additional_mcp_servers instanceof Array) {
      for (const service of options.additional_mcp_servers) {
        if (typeof service === "string") {
          this.#allowedMcpServers.add(service);
        } else {
          console.warn("The `additional_mcp_servers` must be an array of strings, but got", service);
        }
      }
    }

    this.mcpServersInitializing = this.initMcpServers();
  }

  private initMcpServers() {
    if (this.#allowedMcpServers.size === 0) return null;
    return Promise.allSettled(
      Array.from(this.#allowedMcpServers, async (url) => {
        const buffer = await mcpCall(url, "tools/list", {});
        const tools = buffer.flatMap((result) => {
          const { tools } = Object(result);
          if (!tools || !(tools instanceof Array)) return [];
          return tools;
        });
        for (const tool of tools) {
          const { name, description, inputSchema } = Object(tool);
          if (typeof name !== "string") continue;
          if (typeof description !== "string" || description === undefined) continue;
          if (!inputSchema || typeof inputSchema !== "object") continue;
          this.registerTool({ name, description, parameters: inputSchema }, (args: unknown) => {
            const buffer = new LiveBuffer<string>();
            mcpCall(url, "tools/call", { name, arguments: args }).tee({
              enqueue: (result) => {
                const { content, isError } = Object(result);
                const textContent = [content]
                  .flat()
                  .map((i) => Object(i).text)
                  .filter((i) => i)
                  .join("");
                if (isError) {
                  buffer.errorIfNeeded(new McpToolCallError(textContent));
                } else {
                  buffer.enqueue(textContent);
                }
              },
              close: () => buffer.closeIfNeeded(),
              error: (reason) => buffer.errorIfNeeded(reason),
            });
            return buffer;
          });
        }
      })
    ).then((results) => {
      for (const result of results) {
        if (result.status === "rejected") {
          console.error("Failed to initialize MCP server", result.reason);
        }
      }
    });
  }

  public getToolDeclarations() {
    return this.#tools;
  }

  public findTool(name: string) {
    return this.#tools.find((t) => t.name === name);
  }

  public registerTool<T extends string>(
    declarationFunction: ToolDeclarationFunction<T>,
    implement: (args: Record<T, unknown>) => Promise<unknown> | IterableBuffer<string>
  ) {
    this.#tools.push(new ImplementedTool(declarationFunction, implement));
  }

  public registerApiTool(options: ApiToolOptions) {
    const { declaration, implementation } = buildApiTool(options, this.#environmentVariables);
    this.registerTool(declaration, implementation);
  }

  public registerNativeTool(
    declarationFunction: ToolDeclarationFunction,
    implement: (args: Record<string, unknown>) => Promise<unknown>
  ) {
    if (!this.#allowedNativeTools.has(declarationFunction.name)) return false;
    this.registerTool(declarationFunction, implement);
    return true;
  }

  public registerEnvironmentVariables(variables: Record<string, string>) {
    for (const [name, value] of Object.entries(variables)) {
      this.#environmentVariables[name] = value;
    }
  }

  /**
   * NOTE: This method will never be rejected.
   */
  public invokeTool(
    func: {
      name: string | IterableBuffer<string>;
      arguments: string | IterableBuffer<string>;
    },
    abortController: AbortController | undefined
  ) {
    const output = new LiveBuffer<string>();
    Promise.all([func.name, func.arguments])
      .then(async (args) => {
        const [name, jsonArgs] = args.map((u) => (typeof u === "string" ? u : u.join("")));
        console.log("args: ", name, jsonArgs);
        const tool = this.findTool(name);
        if (!tool) throw new Error(`Tool '${name}' is not found`);

        const parsedArgs = JSON.parse(jsonArgs || "{}");

        const buffer = tool.implementation.call(null, parsedArgs);

        const aborted = new Promise<never>((_, reject) => {
          abortController?.signal.addEventListener("abort", ({ target }) => {
            if (target instanceof AbortSignal) return reject(target.reason);
            throw new Error("Never reach here: AbortSignal is not an instance of AbortSignal in abort event handler");
          });
        });

        if (buffer instanceof Promise) {
          const result = await Promise.race([buffer, aborted]);
          output.enqueue(JSON.stringify(result));
          output.close();
        } else {
          const iterator = buffer[Symbol.asyncIterator]();
          for (;;) {
            const v = await Promise.race([iterator.next(), aborted]);
            if (v.done) {
              output.close();
              break;
            }
            output.enqueue(v.value);
          }
        }
      })
      .catch((error) => {
        output.enqueue("<error>");
        if (error instanceof Error) {
          output.enqueue(error.name + ": " + error.message);
        } else {
          output.enqueue("Unknown Error");
        }
        output.enqueue("</error>");
        output.close();
      });
    return output;
  }
}
