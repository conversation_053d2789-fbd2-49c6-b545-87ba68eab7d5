import { ChatBasic } from "../ChatBasic";
import { IterableBuffer, LiveBuffer } from "../IterableBuffer";
import { JsonRpcError } from "../errors/JsonRpcError";
import { SseTransform } from "../SseTransform";

export const mcpCall = (url: string, method: "tools/list" | "tools/call", params: unknown): IterableBuffer<unknown> => {
  const buffer = new LiveBuffer<unknown>();
  Promise.resolve()
    .then(() =>
      ChatBasic.fetch(url, {
        method: "POST",
        credentials: "include",
        headers: { "Content-Type": "application/json", Accept: "event/stream" },
        body: JSON.stringify({ jsonrpc: "2.0", method, id: Date.now(), params }),
      })
    )
    .then(async (response) => {
      if (!response.ok) throw new Error(`Got status code ${response.status} from ${url}`);
      const { body } = response;
      if (!body) throw new Error(`Got empty body from ${url}`);
      const contentType = response.headers.get("Content-Type");
      if (contentType && /^application\/json\s*(;|$)/.test(contentType)) {
        buffer.enqueue(await response.json());
      } else {
        await body
          .pipeThrough(new TextDecoderStream())
          .pipeThrough(new SseTransform())
          .pipeTo(
            new WritableStream({
              write: (event) => {
                if (event.event !== "message") return;
                const { data } = event;
                if (!data || typeof data !== "string" || data[0] !== "{") return;
                try {
                  const { result, error } = JSON.parse(data);
                  if (error) throw new JsonRpcError(error);
                  buffer.enqueue(result);
                } catch (e) {
                  buffer.errorIfNeeded(e);
                }
              },
              close: () => buffer.closeIfNeeded(),
              abort: (e) => buffer.errorIfNeeded(e),
            })
          );
      }
    })
    .catch((e) => {
      buffer.errorIfNeeded(e);
    });
  return buffer;
};
