import { B<PERSON><PERSON><PERSON>tate, IterableBuffer, LiveBuffer } from "./IterableBuffer";
import {
  readObjectEntriesToBuffer,
  readSpaces,
  SequenceDigester,
  TypedObjectEntry,
  valueOfTypedValue,
} from "./SequenceDigest";

interface ContentfulCall {
  type: "call";
  value: {
    name: IterableBuffer<string>;
    arguments: IterableBuffer<string>;
  };
}

interface ContentfulText {
  type: "think" | "content";
  value: IterableBuffer<string>;
}

export type ContentfulSection = ContentfulCall | ContentfulText;

export const parseTaggedContent = (liveContent: IterableBuffer<string>) => {
  const sections = new LiveBuffer<ContentfulSection>();

  const digester = new SequenceDigester(function* () {
    let current: LiveBuffer<string> | null = null;

    for (;;) {
      if (yield "<think>") {
        if (current) {
          current.close();
          current = null;
        }
        const buf = new LiveBuffer<string>();
        let sent = false;
        for (;;) {
          if (yield "</think>") break;
          const c = yield 1;
          if (!c) break; // EOS
          buf.enqueue(c);
          // Defer to send the think section until it has some non-blank content.
          if (!sent && buf.buffer.join("").trim()) {
            sections.enqueue({ type: "think", value: buf });
            sent = true;
          }
        }
        buf.close();
        continue;
      }

      if (yield "<tool_call>") {
        if (current) {
          current.close();
          current = null;
        }
        const value = {
          name: new LiveBuffer<string>(),
          arguments: new LiveBuffer<string>(),
        };
        sections.enqueue({ type: "call", value });
        yield* readSpaces();
        if ((yield -1) === "{") {
          const entries = new LiveBuffer<TypedObjectEntry>();
          entries.tee({
            enqueue: (entry) => {
              if (entry.key === "name" && entry.value.type === "string") {
                entry.value.value.tee(value.name);
              } else if (entry.key === "arguments") {
                if (entry.value.type === "string") {
                  entry.value.value.tee(value.arguments);
                } else if (entry.value.type === "object") {
                  entry.value.value.tee({
                    close: () => {
                      const obj = valueOfTypedValue(entry.value);
                      value.arguments.enqueue(JSON.stringify(obj));
                      value.arguments.close();
                    },
                    error: (e) => {
                      value.arguments.error(e);
                    },
                  });
                }
              }
            },
          });
          try {
            yield* readObjectEntriesToBuffer(entries);
          } catch (e) {
            if (value.name.state === BufferState.Open) value.name.error(e);
            if (value.arguments.state === BufferState.Open) value.arguments.error(e);
            console.warn("Cannot parse JSON in <tool_call> tag", e);
          }
          entries.close();
        }
        value.name.closeIfNeeded();
        value.arguments.closeIfNeeded();
        let restContent = "";
        for (;;) {
          if (yield "</tool_call>") break;
          const c = yield 1;
          if (!c) break; // EOS
          restContent += c;
        }
        restContent = restContent.trim();
        if (restContent) {
          console.warn(`Unexpected content ${JSON.stringify(restContent)} before </tool_call> tag`);
        }
        continue;
      }

      const c = yield 1;
      if (!c) break; // EOF

      if (!current) {
        current = new LiveBuffer<string>();
        sections.enqueue({ type: "content", value: current });
      }

      current.enqueue(c);
    }
    if (current) current.close();
  });

  liveContent.tee({
    enqueue: (chunk) => digester.write(chunk),
    close: () => {
      digester.close();
      sections.close();
    },
    error: (reason) => {
      sections.error(reason);
    },
  });

  return sections;
};
