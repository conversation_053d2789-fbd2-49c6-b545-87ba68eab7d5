import { IterableBuffer, LiveBuffer } from "./IterableBuffer";

export type Directive = number | string;

export type Digest<R = void> = Generator<Directive, R, string>;

interface Iterating {
  iterator: Digest;
  result?: IteratorResult<Directive, void> | null;
  index: number;
}

/**
 * The `SequenceDigester` is used to parse a character sequence and it supports asynchronous streaming.
 *
 * The `digest` function is a generator function that yields directives.
 *
 * In the `digest` function, the `yield` something means read or lookup something from the buffer.
 *
 * - yield a number means read the number of characters from the buffer.
 * - yield a minus number means lookup the number of characters from the buffer.
 * - yield a string means if the string is the prefix of the buffer, read the string from the buffer.
 *
 */
export class SequenceDigester {
  private digest: () => Digest;

  private iterating: Iterating | null;

  #buffer: string;
  #index: number;

  public done: boolean;

  get buffer() {
    return this.#buffer;
  }

  get index() {
    return this.#index;
  }

  public getRestBuffer() {
    return this.#buffer.slice(this.#index);
  }

  constructor(digest: () => Digest) {
    this.done = false;
    this.iterating = null;
    this.digest = digest;
    this.#buffer = "";
    this.#index = 0;
  }

  private processNext() {
    while (true) {
      // If the iterator is not initialized, initialize it.
      // IMPORTANT: the `digest` function must yield something, otherwise the loop will never end.
      if (!this.iterating) {
        if (this.done) {
          if (this.#index < this.#buffer.length) {
            console.warn("Some bytes are not consumed by the digest function");
          }
          return;
        }
        const iterator = this.digest();
        const result = iterator.next();
        this.iterating = { iterator, result, index: this.#index };
        if (result.done) throw new Error("The digest function must yield something");
      }

      // Execute `digest` function until it yields something.
      if (!this.iterating.result) this.iterating.result = this.iterating.iterator.next();

      if (this.iterating.result.done) {
        if (this.#index < this.#buffer.length && this.iterating.index === this.#index) {
          throw new Error("Nothing is consumed by the digest function, infinite loop may happen");
        }
        this.iterating = null;
        // If the buffer is not empty, continue to process the next directive.
        if (this.#index < this.#buffer.length) continue;
        // Return if the buffer is empty, waiting for the next write.
        return;
      }

      const directive = this.iterating.result.value;

      if (typeof directive == "number") {
        if (directive == 0) throw new RangeError("Failed to read a zero size chunk");
        const sign = Math.sign(directive);
        if (this.#buffer.length - this.#index < directive * sign) {
          if (this.done) {
            this.iterating.result = this.iterating.iterator.next("");
            continue;
          } else return;
        }
        const str = this.#buffer.slice(this.#index, this.#index + directive * sign);
        // A negative value means lookup only - it won't consume the buffer.
        if (sign > 0) this.#index += directive * sign;
        this.iterating.result = this.iterating.iterator.next(str);
        continue;
      }

      // If the directive is a string, it means read the string from the buffer.
      else if (typeof directive == "string") {
        if (directive.length == 0) throw new RangeError("Failed to read a zero size chunk");
        // If the buffer is not enough, some cases need to check.
        if (this.#buffer.length - this.#index < directive.length) {
          // First: If the input has been closed, generate an empty string and continue.
          if (this.done) {
            this.iterating.result = this.iterating.iterator.next("");
            continue;
          }
          // Second: If the buffer is missing the prefix of the directive, that means it will never be matched.
          // In this case, waiting is not meaningful, fast reject it.
          // So, generate an empty string and continue to indicate the directive is not matched.
          if (!directive.startsWith(this.#buffer.slice(this.#index))) {
            this.iterating.result = this.iterating.iterator.next("");
            continue;
          }
          // Wait the buffer expand.
          return;
        }
        if (this.#buffer.slice(this.#index, this.#index + directive.length) === directive) {
          this.#index += directive.length;
          this.iterating.result = this.iterating.iterator.next(directive);
        } else {
          this.iterating.result = this.iterating.iterator.next("");
        }
        continue;
      }

      throw new Error(`Unexpected directive: ${directive}`);
    }
  }

  public write(chunk: string) {
    if (this.done) throw new Error("Is already done");
    this.#buffer += chunk;
    this.processNext();
  }

  public close() {
    if (this.done) throw new Error("Is already done");
    this.done = true;
    this.processNext();
  }
}

const butGot = (s: string) => {
  if (s == "") return "EOF";
  if (s == "'") return "a single quote";
  const code = s.charCodeAt(0);
  if (code < 20) return `\\x${code.toString(16).padStart(2, "0")}`;
  return `'${s}'`;
};

export interface TypedObjectEntry {
  key: string;
  value: TypedValue;
}

export type TypedValue =
  | { type: "boolean"; value: boolean }
  | { type: "number"; value: number }
  | { type: "null"; value: null }
  | { type: "string"; value: IterableBuffer<string> }
  | { type: "array"; value: IterableBuffer<TypedValue> }
  | { type: "object"; value: IterableBuffer<TypedObjectEntry> };

export function* readSimpleValue(): Digest<TypedValue | null> {
  if (yield "true") return { type: "boolean", value: true };
  if (yield "false") return { type: "boolean", value: false };
  if (yield "null") return { type: "null", value: null };
  const number = yield* readNumber();
  if (number != null) return { type: "number", value: number };
  return null;
}

export function* readNumber(): Digest<number | null> {
  let buf = "";

  // A JSON number must start with a digit or a minus sign.
  if (yield "-") {
    buf += "-";
    if (!/\d/.test(yield -1)) throw new SyntaxError("No number after minus sign in JSON");
    buf += yield 1;
  } else {
    // Here, not characters are consumed, return null indicates not in the JSON number.
    if (!/\d/.test(yield -1)) return null;
    buf += yield 1;
  }

  // If the number is leading zero, the next character must not be a digit.
  // For example, "01" is not a valid number in JSON.
  if (buf !== "0" && buf !== "-0") {
    while (/\d/.test(yield -1)) buf += yield 1;
  }

  // Process the fractional part.
  if (yield ".") {
    buf += ".";
    while (/\d/.test(yield -1)) buf += yield 1;
    if (buf.endsWith(".")) throw new SyntaxError("Unterminated fractional number in JSON");
  }

  // Process the exponent part.
  if ((yield "E") || (yield "e")) {
    buf += "E";
    if (yield "-") buf += "-";
    else if (yield "+") buf += "+";
    while (/\d/.test(yield -1)) buf += yield 1;
    if (!/\d$/.test(buf)) throw new SyntaxError("Exponent part is missing a number in JSON");
  }

  return Number(buf);
}

export function* readSpaces(): Digest<string> {
  let str = "";
  while (/\s/.test(yield -1)) str += yield 1;
  return str;
}

export function* readJsonString(): Digest<string> {
  let sb = new LiveBuffer<string>();
  yield* readJsonStringToBuffer(sb);
  sb.close();
  return sb.buffer.join("");
}

export function* readJsonStringToBuffer(buffer: LiveBuffer<string>): Digest<void> {
  if (!(yield '"')) throw new SyntaxError(`Expected '"' but got ${butGot(yield -1)}`);
  for (;;) {
    const c: string = yield 1;
    if (c === '"') break;
    let str: string;
    if (c === "\\") {
      const n: string = yield 1;
      if (n === "u") {
        str = String.fromCharCode(parseInt(yield 4, 16));
      } else if (n === "x") {
        str = String.fromCharCode(parseInt(yield 2, 16));
      } else {
        str = JSON.parse(`"\\${n}"`);
      }
    } else if (c === "\n" || c === "\r") {
      throw new SyntaxError(`Unexpected newline in JSON string`);
    } else if (c === "") {
      throw new SyntaxError(`Unexpected EOF in JSON string`);
    } else {
      str = c;
    }
    buffer.enqueue(str);
  }
}

/**
 * Read a value and notify the result to the `notify` function as fast as possible.
 *
 * @param notify - The function to notify the result.
 *                 Note: This function always be called, and called only once.
 */
function* readValueAndFastNotify(notify: (value: TypedValue) => void): Digest<void> {
  let value = yield* readSimpleValue();

  if (value) return notify(value);
  const c = yield -1;

  if (c === "[") {
    const value = new LiveBuffer<TypedValue>();
    notify({ type: "array", value });
    yield* readArrayToBuffer(value);
    value.close();
    return;
  }

  if (c === "{") {
    const value = new LiveBuffer<TypedObjectEntry>();
    notify({ type: "object", value });
    yield* readObjectEntriesToBuffer(value);
    value.close();
    return;
  }

  if (c === '"') {
    const value = new LiveBuffer<string>();
    notify({ type: "string", value });
    yield* readJsonStringToBuffer(value);
    value.close();
    return;
  }

  throw new SyntaxError(`Unexpected ${butGot(c)} after ':'`);
}

export function* readObjectEntriesToBuffer(receiver: LiveBuffer<TypedObjectEntry>): Digest<void> {
  if (!(yield "{")) throw new SyntaxError(`Expected '{' but got ${butGot(yield -1)}`);
  for (;;) {
    yield* readSpaces();
    if (yield "}") break;
    const key = yield* readJsonString();
    yield* readSpaces();

    if (!(yield ":")) throw new SyntaxError(`Expected ':' but got ${butGot(yield -1)}`);
    yield* readSpaces();

    yield* readValueAndFastNotify((value) => receiver.enqueue({ key, value }));

    yield* readSpaces();
    if (yield "}") break;
    if (yield ",") continue;
    throw new SyntaxError(`Expected ',' or '}' but got ${butGot(yield -1)}`);
  }
}

export function* readArrayToBuffer(receiver: LiveBuffer<TypedValue>): Digest<void> {
  if (!(yield "[")) throw new SyntaxError(`Expected '[' but got ${butGot(yield -1)}`);
  for (;;) {
    yield* readSpaces();
    if (yield "]") break;
    yield* readValueAndFastNotify((value) => receiver.enqueue(value));
    yield* readSpaces();
    if (yield ",") continue;
    if (yield "]") break;
    throw new SyntaxError(`Expected ',' or ']' but got ${butGot(yield -1)}`);
  }
}

export type JsonValue = boolean | number | null | string | JsonValue[] | { [p in string]: JsonValue };

export const valueOfTypedValue = (value: TypedValue): JsonValue => {
  switch (value.type) {
    case "boolean":
      return value.value;
    case "number":
      return value.value;
    case "null":
      return null;
    case "string":
      return value.value.buffer.join("");
    case "array":
      return value.value.buffer.map(valueOfTypedValue);
    case "object":
      return Object.fromEntries(value.value.buffer.map((e) => [e.key, valueOfTypedValue(e.value)] as const));
  }
};

/**
 * @deprecated Use `readValueAndFastNotify` instead.
 */
export function* readJsonValue(): Digest<unknown> {
  let receiver: TypedValue | null = null;
  yield* readValueAndFastNotify((value) => {
    receiver = value;
  });
  if (!receiver) throw new SyntaxError("Never happen");
  return valueOfTypedValue(receiver);
}
