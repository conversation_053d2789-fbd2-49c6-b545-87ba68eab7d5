export type PartialOptional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export const deepCopy = <T>(obj: T): T => JSON.parse(JSON.stringify(obj));export type DeepFreeze<T> =
  // Freeze Object
  T extends object ? {
    readonly [K in keyof T]: DeepFreeze<T[K]>;
  } : T extends (infer U)[] ? readonly DeepFreeze<U>[] : T;
export function neverReachHere(_: never): never {
  throw new Error("Never reach here");
}

