{"name": "@hll/section-parser", "version": "1.4.9", "description": "Parse sections from a text stream", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "scripts": {"build": "NODE_ENV=production rollup -c", "dev": "rollup -c -w", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "clean": "rm -rf dist"}, "author": "<EMAIL>", "license": "ISC"}