{"compilerOptions": {"target": "ES2022", "module": "ESNext", "lib": ["DOM", "DOM.Iterable", "DOM.AsyncIterable", "ESNext"], "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "declaration": true, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "esModuleInterop": true, "allowJs": true, "checkJs": true, "declarationDir": "dist", "outDir": "dist", "baseUrl": "."}, "include": ["src/**/*.ts", "src/**/*.d.ts"], "exclude": ["node_modules", "dist"]}