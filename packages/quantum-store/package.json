{"name": "@hll/quantum-store", "version": "1.0.5", "description": "The store for quantum box and quantum playground", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "scripts": {"build": "NODE_ENV=production rollup -c", "dev": "rollup -c -w", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "clean": "rm -rf dist"}, "author": "<EMAIL>", "license": "ISC", "dependencies": {"react": ">=18.0.0", "@hll/use-event-listener": ">=0.0.1"}}