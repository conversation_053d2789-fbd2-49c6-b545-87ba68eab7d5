export type StringTypeSchema = {
  type: "string";
  enum?: string[];
  const?: string;
  description?: string;
};

export type PrimitiveTypeSchema =
  | StringTypeSchema
  | {
      type: "number" | "boolean";
      description?: string;
    };

export type ArrayTypeSchema = {
  type: "array";
  items: ParameterSchema;
  description?: string;
};

export type ObjectTypeSchema = {
  type: "object";
  properties?: Record<string, ParameterSchema>;
  additionalProperties?: ParameterSchema;
  description?: string;
  required?: string[];
};

export type ParameterSchema = PrimitiveTypeSchema | ArrayTypeSchema | ObjectTypeSchema;

export type ConstantStringSchema = PrimitiveTypeSchema | string;

export type AdditionalNativeTool =
  | "quantumBox.addTab"
  | "quantumBox.removeTab"
  | "quantumBox.setActiveTab"
  | "quantumBox.reloadTab"
  | "quantumBox.requestTabs";

export type AdditionalApiTool = {
  name: string;
  description: string;
  method: "GET" | "POST" | "PUT" | "DELETE" | "PATCH" | "HEAD";
  url: string;
  headers?: {
    type: "object";
    properties: Record<string, ConstantStringSchema>;
    required?: string[];
  };
  pathVariables?: Record<string, ConstantStringSchema>;
  query?: {
    properties: Record<
      string,
      | {
          type: "array";
          items: PrimitiveTypeSchema;
          description?: string;
        }
      | PrimitiveTypeSchema
      | string
      | string[]
    >;
    required?: string[];
  };
  body?: {
    properties: Record<string, ParameterSchema>;
    required?: string[];
  };
};

export type HaitunOptions = {
  model?: "Qwen3-14B";
  system_message?: string;
  welcome_message?: string;
  additional_native_tools?: AdditionalNativeTool[];
  additional_api_tools?: AdditionalApiTool[];
};

export type WukongOptions = {
  welcome_message?: string;
  agent_id: string;
  business_alias: string;
  project_alias: string;
  additional_native_tools?: AdditionalNativeTool[];
};

export type TradeOptions = {
  welcome_message?: string;
  platform?: string;
  assistant?: string;
  model?: string;
};

export type WorkflowOptions = {
  workflow_id: string;
  welcome_message?: string;
  parameters?: Record<string, any>;
  output_key?: string;
  tool_calls_key?: string;
  additional_native_tools?: AdditionalNativeTool[];
  additional_api_tools?: AdditionalApiTool[];
};

export type CommonOptions = {
  $schema?: string;
  name?: string;
  description?: string;
  tags?: string[];
  icon?: string;
  disclaimer?: string;
  samplePrompts?: string[];
  aiType?: string;
  options?: object;
};

export type QuantumAgentConfig =
  | { href: string; $schema?: string }
  | (CommonOptions & {
      aiType: "haitun";
      options?: HaitunOptions;
    })
  | (CommonOptions & {
      aiType: "wukong";
      options?: WukongOptions;
    })
  | (CommonOptions & {
      aiType: "trade";
      options?: TradeOptions;
    })
  | (CommonOptions & {
      aiType: "workflow";
      options?: WorkflowOptions;
    });
