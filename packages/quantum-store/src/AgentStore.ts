import { computeIfAbsent } from "./lib/computeIfAbsent";
import type { QuantumAgentConfig } from "./quantumAgentConfig";
import { TagResult, TagStore, TagStoreType } from "./TagStore";

export type FinalQuantumAgentConfig = Extract<QuantumAgentConfig, { aiType: string }>;

export const isFinalQuantumAgentConfig = (data: QuantumAgentConfig): data is FinalQuantumAgentConfig =>
  !("href" in data) || typeof data.href !== "string";

const finalConfigCache = new WeakMap<TagResult<QuantumAgentConfig>, Promise<TagResult<FinalQuantumAgentConfig>>>();

const loadFinalConfigWithoutCache = async (lastResult: TagResult<QuantumAgentConfig>) => {
  const { data, ...rest } = lastResult;
  if (!data) return { data: null, ...rest };
  if (isFinalQuantumAgentConfig(data)) return { data, ...rest };
  const res = await TagStore.fetch(data.href, { credentials: "include" });
  const finalData = Object(await res.json());
  return { data: finalData, ...rest };
};

export class AgentStore extends TagStore<QuantumAgentConfig> {
  public readonly id: string;
  constructor(agentId: string) {
    super(TagStoreType.Protected, `quantum-playground/agents/${agentId}.json`);
    this.id = agentId;
  }

  public async getFinalConfig(): Promise<TagResult<FinalQuantumAgentConfig>> {
    const input = await super.get();

    const cached = finalConfigCache.get(input);
    if (cached) return cached;
    const res = loadFinalConfigWithoutCache(input);
    res.catch((error) => {
      finalConfigCache.delete(input);
      throw error;
    });
    finalConfigCache.set(input, res);
    return res;
  }
}

const agentCache = new Map<string, AgentStore>();
export const getAgentStore = (agentId: string) => {
  return computeIfAbsent(agentCache, agentId, () => new AgentStore(agentId));
};
