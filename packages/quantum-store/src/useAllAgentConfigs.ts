import { useState, useEffect } from "react";
import { getAgentStore } from "./utils";
import { TagStore } from "./TagStore";
import { AgentStore } from "./AgentStore";
import { useTagStore } from "./useTagStore";

export const getAllFinalConfigs = async (stores: AgentStore[]) =>
  Promise.all(
    stores.map(async (store) => {
      try {
        const { data, ...rest } = await store.getFinalConfig();
        if (!data) return null;
        return { id: store.id, data, ...rest };
      } catch (error) {
        console.error(`Failed to get final config for agent ${store.id}:`, error);
        return null;
      }
    })
  ).then((configs) =>
    configs.filter((c) => c !== null).sort((a, b) => (b.timestamp?.getTime() ?? 0) - (a.timestamp?.getTime() ?? 0))
  );

export const useAllAgentConfigs = (store: TagStore<string[]> | undefined) => {
  const { data: ids, loading, error } = useTagStore(store);

  const [state, setState] = useState<{
    data?: Awaited<ReturnType<typeof getAllFinalConfigs>>;
    error?: unknown;
    loading: boolean;
  }>({ loading: true });

  useEffect(() => {
    if (loading || error) return;
    if (!ids || ids.length === 0) {
      setState({ data: [], loading: false });
      return;
    }
    const stores = ids.map((id) => getAgentStore(id));
    const update = async () => {
      setState((prev) => ({ ...prev, loading: true }));
      try {
        const data = await getAllFinalConfigs(stores);
        setState({ data, loading: false });
      } catch (error) {
        setState((prev) => ({ ...prev, error, loading: false }));
      }
    };
    update();

    // Watch for updates to the agent configs
    stores.forEach((store) => store.eventBus.addEventListener("update", update));
    return () => stores.forEach((store) => store.eventBus.removeEventListener("update", update));
  }, [ids, loading, error]);

  return { ...state, loading: state.loading || loading, error: state.error || error };
};
