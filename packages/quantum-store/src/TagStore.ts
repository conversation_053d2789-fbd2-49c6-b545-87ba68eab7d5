import { rejectIfError } from "./lib/rejectIfError";

export enum TagStoreType {
  Public = "public-tags",
  Protected = "protected-tags",
  Private = "private-tags",
}

export interface TagResult<T> {
  readonly head: string;
  readonly owner: string;
  readonly timestamp: Date | null;
  readonly data: T | null;
}

export class TagStore<T = unknown> {
  public static base = "https://fe-tools.huolala.work";
  public static fetch = fetch.bind(window);
  public lastResult: TagResult<T> | null;
  public type: TagStoreType;
  public tagName: string;
  public eventBus: EventTarget;
  private busy?: Promise<TagResult<T>>;

  public get base() {
    return `${TagStore.base}/${this.type}/${this.tagName}`;
  }

  public get head() {
    return this.lastResult?.head ?? "null";
  }

  constructor(type: TagStoreType, tagName: string) {
    this.type = type;
    this.tagName = tagName;
    this.eventBus = new EventTarget();
    this.lastResult = null;
  }

  public async get() {
    return this.lastResult || this.getWithoutCache();
  }

  public getWithoutCache() {
    if (this.busy) return this.busy;
    const headers: Record<string, string> = { "X-Null-If-Not-Found": "true" };
    if (this.lastResult?.head) headers["If-None-Match"] = this.lastResult.head;
    this.busy = TagStore.fetch(this.base, {
      method: "GET",
      headers,
      credentials: "include",
    })
      .then(rejectIfError)
      .then(async (res: Response) => {
        if (res.status === 304) {
          if (!this.lastResult) throw new Error("Got 304 but no last result");
          return this.lastResult;
        }
        const head = res.headers.get("X-Commit-Hash") ?? "null";
        const owner = res.headers.get("X-Commit-Owner") ?? "unknown";
        const timestamp = res.headers.get("X-Commit-Timestamp") ?? null;
        const data: T = await res.json();
        return { head, owner, data, timestamp: timestamp ? new Date(timestamp) : null };
      })
      .then((result) => {
        if (result !== this.lastResult) {
          this.lastResult = result;
          this.eventBus.dispatchEvent(new CustomEvent("update", { detail: result }));
        }
        delete this.busy;
        return result;
      })
      .catch((error) => {
        this.eventBus.dispatchEvent(new CustomEvent("error", { detail: error }));
        delete this.busy;
        throw error;
      });
    return this.busy;
  }

  public async getCommit() {
    return TagStore.fetch(`${this.base}?info=commit`, {
      method: "GET",
      credentials: "include",
    })
      .then(rejectIfError)
      .then(async (res) => {
        const commit = await res.json();
        return commit as {
          parent?: string;
          objectHash: string;
          contentType?: string;
          owner: string;
          meta?: Record<string, string>;
          timestamp: string;
        };
      });
  }

  public async upload(data: T, ifMatchParent: string) {
    await TagStore.fetch(`${this.base}?action=upload`, {
      method: "POST",
      headers: { "If-Match-Parent": ifMatchParent, "Content-Type": "application/json" },
      body: JSON.stringify(data),
      credentials: "include",
    }).then(rejectIfError);
  }

  public async patch(operations: { path: string; op: string; value?: unknown }[], parent: string) {
    await TagStore.fetch(this.base, {
      method: "PATCH",
      body: JSON.stringify(operations),
      headers: { "If-Match-Parent": parent, "Content-Type": "application/json" },
      credentials: "include",
    }).then(rejectIfError);
  }

  public async delete(parent: string) {
    if (this.type === TagStoreType.Public) {
      throw new Error("You cannot delete a public tag");
    }
    await TagStore.fetch(this.base, {
      method: "DELETE",
      headers: { "If-Match-Parent": parent },
      credentials: "include",
    }).then(rejectIfError);
  }

  public async updateAuthorizedUsers(users: string[]) {
    if (this.type !== TagStoreType.Protected) {
      throw new Error("updateAuthorizedUsers is only supported for protected tags");
    }
    await TagStore.fetch(`${this.base}?action=updateAuthorizedUsers`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(users),
      credentials: "include",
    }).then(rejectIfError);
  }

  public async getAuthorizedUsers() {
    if (this.type !== TagStoreType.Protected) {
      throw new Error("getAuthorizedUsers is only supported for protected tags");
    }
    return this.getCommit().then(async (commit) => {
      const authorizedUsers = commit.meta?.authorizedUsers;
      if (typeof authorizedUsers !== "string") return [];
      return authorizedUsers
        .split(",")
        .map((user) => user.trim())
        .filter((i) => i);
    });
  }

  /**
   * Executes a read-modify-write transaction on the tag data.
   *
   * This method ensures data consistency by automatically retrying the operation
   * when a `ConsistencyException` is thrown, typically due to concurrent modifications.
   *
   * @param handler - A function that receives the current tag data and head, and returns a promise
   * @param retries - Maximum number of retry attempts (default: 3)
   *
   * @example
   * ```typescript
   * await tagStore.transaction(async ({ data, head }) => {
   *   // Modify data based on current state
   *   const newData = { value: data.value + 1 };
   *   await tagStore.upload(newData, head);
   * });
   * ```
   */
  public async transaction<R>(handler: ({ data, head }: TagResult<T>) => Promise<R>, retries = 3): Promise<R> {
    for (;;) {
      const current = await this.get();
      try {
        const result = await handler(current);
        // Do not await.
        this.getWithoutCache();
        return result;
      } catch (error) {
        if (retries-- > 0 && Object(error).name === "ConsistencyException") {
          await this.getWithoutCache();
          continue;
        }
        throw error;
      }
    }
  }
}

export class ListTagStore<T> extends TagStore<T[]> {
  constructor(type: TagStoreType, tagName: string) {
    super(type, tagName);
  }

  async add(item: T) {
    return await this.transaction(async ({ head }) => {
      if (head === "null") {
        await this.upload([item], head);
      } else {
        await this.patch([{ path: `/-`, op: "add", value: item }], head);
      }
    });
  }

  async remove(item: T) {
    return await this.transaction(async ({ data, head }) => {
      if (head === "null" || !data) return;
      const index = data?.indexOf(item) ?? -1;
      if (index === -1) return;
      await this.patch([{ path: `/${index}`, op: "remove" }], head ?? "null");
    });
  }
}
