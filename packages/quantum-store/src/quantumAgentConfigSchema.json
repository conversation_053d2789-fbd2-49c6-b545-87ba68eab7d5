{"$schema": "https://json-schema.org/draft/2020-12/schema", "title": "Quantum Box Agent Config <PERSON>", "description": "Configuration for Quantum Box Agent", "definitions": {"stringTypeSchema": {"type": "object", "properties": {"type": {"const": "string"}, "enum": {"type": "array", "items": {"type": "string"}}, "const": {"type": "string"}, "description": {"type": "string"}}, "required": ["type"], "additionalProperties": false}, "primitiveTypeSchema": {"oneOf": [{"$ref": "#/definitions/stringTypeSchema"}, {"type": "object", "properties": {"type": {"type": "string", "enum": ["number", "boolean"]}, "description": {"type": "string"}}, "required": ["type"], "additionalProperties": false}]}, "arrayTypeSchema": {"type": "object", "properties": {"type": {"const": "array"}, "items": {"$ref": "#/definitions/parameterSchema"}, "description": {"type": "string"}}, "required": ["type", "items"], "additionalProperties": false}, "objectTypeSchema": {"type": "object", "properties": {"type": {"const": "object"}, "properties": {"type": "object", "additionalProperties": {"$ref": "#/definitions/parameterSchema"}}, "additionalProperties": {"$ref": "#/definitions/parameterSchema"}, "description": {"type": "string"}, "required": {"type": "array", "items": {"type": "string"}}}, "required": ["type"], "additionalProperties": false}, "parameterSchema": {"oneOf": [{"$ref": "#/definitions/primitiveTypeSchema"}, {"$ref": "#/definitions/arrayTypeSchema"}, {"$ref": "#/definitions/objectTypeSchema"}]}, "constantStringSchema": {"oneOf": [{"$ref": "#/definitions/primitiveTypeSchema"}, {"type": "string", "description": "Provide a constant string (Supports environment variables)"}]}, "additionalNativeTools": {"type": "array", "items": {"type": "string", "enum": ["quantumBox.addTab", "quantumBox.removeTab", "quantumBox.setActiveTab", "quantumBox.reloadTab", "quantumBox.requestTabs"]}}, "additionalApiTools": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "method": {"type": "string", "enum": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD"]}, "url": {"type": "string", "format": "uri"}, "headers": {"type": "object", "properties": {"type": {"const": "object"}, "properties": {"type": "object", "additionalProperties": {"$ref": "#/definitions/constantStringSchema"}}, "required": {"type": "array", "items": {"type": "string"}}}, "required": ["properties"]}, "pathVariables": {"type": "object", "additionalProperties": {"$ref": "#/definitions/constantStringSchema"}}, "query": {"type": "object", "properties": {"properties": {"type": "object", "additionalProperties": {"oneOf": [{"type": "object", "properties": {"type": {"const": "array"}, "items": {"$ref": "#/definitions/primitiveTypeSchema"}, "description": {"type": "string"}}, "required": ["type", "items"]}, {"$ref": "#/definitions/primitiveTypeSchema"}, {"type": "string", "description": "Provide a constant string (Supports environment variables)"}, {"type": "array", "items": {"type": "string"}, "description": "Provide a constant array of strings (Supports environment variables)"}]}}, "required": {"type": "array", "items": {"type": "string"}}}, "required": ["properties"]}, "body": {"type": "object", "properties": {"properties": {"type": "object", "additionalProperties": {"$ref": "#/definitions/parameterSchema"}}, "required": {"type": "array", "items": {"type": "string"}}}, "required": ["properties"]}}, "required": ["name", "description", "method", "url"]}}, "haitunOptions": {"type": "object", "properties": {"model": {"type": "string", "enum": ["Qwen3-14B"]}, "system_message": {"type": "string"}, "welcome_message": {"type": "string"}, "additional_native_tools": {"$ref": "#/definitions/additionalNativeTools"}, "additional_api_tools": {"$ref": "#/definitions/additionalApiTools"}, "additional_mcp_services": {"type": "array", "items": {"type": "string", "format": "uri"}}}, "additionalProperties": false}, "wukongOptions": {"type": "object", "properties": {"welcome_message": {"type": "string"}, "agent_id": {"type": "string"}, "business_alias": {"type": "string"}, "project_alias": {"type": "string"}, "additional_native_tools": {"$ref": "#/definitions/additionalNativeTools"}}, "required": ["agent_id", "business_alias", "project_alias"], "additionalProperties": false}, "tradeOptions": {"type": "object", "properties": {"welcome_message": {"type": "string"}, "platform": {"type": "string"}, "assistant": {"type": "string"}, "model": {"type": "string"}}, "required": [], "additionalProperties": false}, "workflowOptions": {"type": "object", "properties": {"workflow_id": {"type": "string"}, "welcome_message": {"type": "string"}, "parameters": {"type": "object", "description": "For the additional `parameters` of workflow, the `BOT_USER_INPUT` will be set as last user message", "additionalProperties": true}, "output_key": {"type": "string", "description": "The field name of the `output` in workflow response"}, "tool_calls_key": {"type": "string", "description": "The field name of the `tool_calls` in workflow response"}, "additional_native_tools": {"$ref": "#/definitions/additionalNativeTools"}, "additional_api_tools": {"$ref": "#/definitions/additionalApiTools"}, "additional_mcp_services": {"type": "array", "items": {"type": "string", "format": "uri"}}}, "required": ["workflow_id"], "additionalProperties": false}, "commonOptions": {"type": "object", "properties": {"$schema": {"type": "string", "format": "uri"}, "name": {"type": "string", "description": "The name of the agent"}, "description": {"type": "string", "description": "The description of the agent"}, "tags": {"type": "array", "items": {"type": "string"}, "description": "The tags of the agent"}, "icon": {"type": "string", "description": "The icon of the agent"}, "disclaimer": {"type": "string", "description": "The disclaimer of the agent"}, "samplePrompts": {"type": "array", "items": {"type": "string"}, "description": "The sample prompts of the agent"}, "aiType": {"type": "string", "description": "Which AI service provider to use"}, "options": {"type": "object", "description": "The options of the AI service provider"}}, "required": ["name"], "additionalProperties": false}}, "oneOf": [{"type": "object", "properties": {"$schema": {"type": "string", "format": "uri"}, "href": {"type": "string", "format": "uri"}}, "required": ["href"]}, {"allOf": [{"$ref": "#/definitions/commonOptions"}, {"type": "object", "properties": {"aiType": {"type": "string", "const": "haitun"}, "options": {"$ref": "#/definitions/haitunOptions"}}, "required": ["aiType", "options"]}]}, {"allOf": [{"$ref": "#/definitions/commonOptions"}, {"type": "object", "properties": {"aiType": {"type": "string", "const": "wukong"}, "options": {"$ref": "#/definitions/wukongOptions"}}, "required": ["aiType", "options"]}]}, {"allOf": [{"$ref": "#/definitions/commonOptions"}, {"type": "object", "properties": {"aiType": {"type": "string", "const": "trade"}, "options": {"$ref": "#/definitions/tradeOptions"}}, "required": ["aiType", "options"]}]}, {"allOf": [{"$ref": "#/definitions/commonOptions"}, {"type": "object", "properties": {"aiType": {"type": "string", "const": "workflow"}, "options": {"$ref": "#/definitions/workflowOptions"}}, "required": ["aiType", "options"]}]}]}