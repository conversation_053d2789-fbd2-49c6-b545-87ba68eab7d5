import { useEventListener } from "@hll/use-event-listener";
import { useState, useEffect } from "react";
import { TagStore, TagResult } from "../TagStore";

interface TagStoreState<T> {
  result?: TagResult<T> | null;
  error?: unknown;
  loading: boolean;
}

export const useTagStore = <T extends unknown>(store: TagStore<T> | undefined) => {
  const [state, setState] = useState<TagStoreState<T>>({
    result: store?.lastResult,
    loading: store ? store.lastResult === undefined : false,
  });

  useEventListener(
    "update",
    () => {
      if (!store) return;
      if (!store.lastResult) return;
      setState((prev) => ({ ...prev, result: store.lastResult }));
    },
    { target: store?.eventBus }
  );

  useEffect(() => {
    if (!store) return;
    setState((prev) => ({ ...prev, loading: true }));
    store
      .get()
      .then((res) => {
        setState({
          result: res,
          error: undefined,
          loading: false,
        });
      })
      .catch((error) => {
        setState({
          result: null,
          error,
          loading: false,
        });
      });
  }, [store]);

  return { ...state.result, error: state.error, loading: state.loading };
};
