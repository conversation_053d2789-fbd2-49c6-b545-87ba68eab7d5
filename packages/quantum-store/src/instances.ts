import { ListTagStore, TagStoreType } from "./TagStore";

const base = "quantum-playground";

export const publicAgentsStore = new ListTagStore<string>(TagStoreType.Public, `${base}/public-agents.json`);
export const myAgentsStore = new ListTagStore<string>(TagStoreType.Private, `${base}/my-agents.json`);
export const favoriteAgentsStore = new ListTagStore<string>(TagStoreType.Private, `${base}/favorite-agents.json`);

// Legacy data migration
const oldFavoriteAgentsStore = new ListTagStore<string>(TagStoreType.Private, `${base}/my-favorites.json`);
oldFavoriteAgentsStore.get().then(async ({ data: legacyData, head: legacyHead }) => {
  if (legacyData == null) return;
  await favoriteAgentsStore.transaction(async ({ data, head }) => {
    if (data == null) {
      favoriteAgentsStore.upload(legacyData, head);
    } else {
      favoriteAgentsStore.upload([...new Set([...data, ...legacyData])], head);
    }
  });
  await oldFavoriteAgentsStore.delete(legacyHead);
});

export const publicSitesStore = new ListTagStore<string>(TagStoreType.Public, `${base}/public-sites.json`);
export const mySitesStore = new ListTagStore<string>(TagStoreType.Private, `${base}/my-sites.json`);
export const favoriteSitesStore = new ListTagStore<string>(TagStoreType.Private, `${base}/favorite-sites.json`);
