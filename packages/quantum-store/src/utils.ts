import { AgentStore } from "./AgentStore";
import { TagStore, TagStoreType } from "./TagStore";

const agentCache: Record<string, AgentStore | undefined> = Object.create(null);
export const getAgentStore = (agentId: string) => {
  if (agentCache[agentId]) return agentCache[agentId];
  agentCache[agentId] = new AgentStore(agentId);
  return agentCache[agentId];
};

export interface SiteConfig {
  url: string;
  title: string;
  description: string;
  icon: string;
}

type SiteStore = TagStore<SiteConfig>;

const siteCache: Record<string, SiteStore | undefined> = Object.create(null);
export const getSiteStore = (siteId: string) => {
  if (siteCache[siteId]) return siteCache[siteId];
  siteCache[siteId] = new TagStore(TagStoreType.Protected, `quantum-playground/sites/${siteId}.json`);
  return siteCache[siteId];
};
