import { computeIfAbsent } from "./lib/computeIfAbsent";
import { TagStore, TagStoreType } from "./TagStore";

export interface SiteConfig {
  url: string;
  title: string;
  description: string;
  icon: string;
}

export class SiteStore extends TagStore<SiteConfig> {
  constructor(siteId: string) {
    super(TagStoreType.Protected, `quantum-playground/sites/${siteId}.json`);
  }
}

const siteCache = new Map<string, SiteStore>();
export const getSiteStore = (siteId: string) => {
  return computeIfAbsent(siteCache, siteId, () => new SiteStore(siteId));
};
