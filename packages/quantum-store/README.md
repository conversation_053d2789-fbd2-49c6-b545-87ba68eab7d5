# @hll/quantum-store

A TypeScript library for tag-based data storage and agent management, with first-class React support.

---

## Features

- **Tag-based Storage**: Hierarchical, access-controlled data storage (public, protected, private)
- **Pre-configured Stores**: Ready-to-use agent and favorites lists
- **React Integration**: Hooks for seamless state management in React apps
- **Atomic Transactions**: Read-modify-write with automatic retry
- **Event-driven Updates**: Real-time updates via event system

---

## Installation

```bash
npm install @hll/quantum-store
```

---

## Quick Start

### Using Pre-configured Stores

The package provides ready-to-use stores for common agent and favorites lists:

```typescript
import { publicAgents, myAgents, myFavorites } from "@hll/quantum-store";

// Get the public agents list
const publicList = await publicAgents.get();

// Get your own agents
const myList = await myAgents.get();

// Get your favorites
const favorites = await myFavorites.get();
```

You can also add or remove items (for list stores):

```typescript
await myAgents.add("new-agent-id");
await myFavorites.remove("old-agent-id");
```

---

## AgentStore & getAgentStore

For advanced agent configuration management, use `getAgentStore` to obtain an `AgentStore` instance for a specific agent:

```typescript
import { getAgentStore } from "@hll/quantum-store";

// Get an AgentStore instance for a specific agent
const agentStore = getAgentStore("agent-id");

// Fetch the agent's configuration (raw)
const configResult = await agentStore.get();

// Fetch the agent's resolved configuration (follows remote reference if needed)
const finalConfigResult = await agentStore.getFinalConfig();
```

---

## React Hooks

### useTagStore

Bind any tag store to your React component for real-time data and status:

```typescript
import { useTagStore, myAgents } from "@hll/quantum-store";

function MyAgentsList() {
  const { data, loading, error } = useTagStore(myAgents);

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  return (
    <ul>
      {data?.map((agentId) => (
        <li key={agentId}>{agentId}</li>
      ))}
    </ul>
  );
}
```

### useAgentConfig

Quickly load and track a single agent's configuration in React:

```typescript
import { useAgentConfig } from "@hll/quantum-store";

function AgentConfigView({ agentId }) {
  const { data, loading, error } = useAgentConfig(agentId);

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  return <pre>{JSON.stringify(data, null, 2)}</pre>;
}
```

---

## Advanced

### Transactions

All stores support atomic read-modify-write operations:

```typescript
await myAgents.transaction(async ({ data, head }) => {
  // Add a new agent if not present
  if (!data.includes("new-agent-id")) {
    await myAgents.add("new-agent-id");
  }
});
```

### Events

Stores emit events for real-time updates:

```typescript
myAgents.eventBus.addEventListener("update", (event) => {
  console.log("Updated:", event.detail);
});
```

---

## Development

```bash
npm run build         # Build the package
npm run dev           # Development mode
npm test              # Run tests
npm run type-check    # Type checking
```

---

## License

ISC

---

## Author

<EMAIL>
