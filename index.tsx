import React, { useState, useRef, useEffect } from "react";

export type PopoverProps = {
  // ... 其他 props ...
  trigger?: "hover" | "click"; // 新增 trigger 属性
  // ... 其他 props ...
};

export const Popover: React.FC<PopoverProps> = ({
  // ... 其他 props ...
  trigger = "hover", // 默认 hover
  // ... 其他 props ...
}) => {
  const [visible, setVisible] = useState(false);
  const popoverRef = useRef<HTMLDivElement>(null);

  // 处理 click 触发时，点击外部关闭
  useEffect(() => {
    if (trigger !== "click" || !visible) return;
    const handleClickOutside = (event: MouseEvent) => {
      if (
        popoverRef.current &&
        !popoverRef.current.contains(event.target as Node)
      ) {
        setVisible(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [trigger, visible]);

  // 事件绑定
  const triggerProps =
    trigger === "click"
      ? {
          onClick: () => setVisible((v) => !v),
        }
      : {
          onMouseEnter: () => setVisible(true),
          onMouseLeave: () => setVisible(false),
        };

  return (
    <div
      ref={popoverRef}
      {...triggerProps}
      // ... existing code ...
    >
      {/* ... existing code ... */}
      {visible && (
        <div className="popover-content">
          {/* ... existing code ... */}
        </div>
      )}
    </div>
  );
}; 