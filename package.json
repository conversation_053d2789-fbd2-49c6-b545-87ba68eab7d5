{"private": true, "description": "Parse sections from a text stream - Monorepo", "workspaces": ["packages/parser", "packages/quantum-store", "packages/playground"], "scripts": {"build": "npm run build --workspaces", "dev": "npm run dev --workspaces", "type-check": "tsc --build", "type-check:watch": "tsc --build --watch", "test": "npm run test --workspaces", "test:watch": "npm run test:watch --workspaces", "clean": "npm run clean --workspaces && rm -rf node_modules"}, "author": "<EMAIL>", "license": "ISC", "devDependencies": {"@eslint/js": "^9.25.0", "@hll/use-event-listener": "^0.0.1", "@hll/use-slot": "^0.0.6", "@hll/vite-plugin-van": "^0.0.9", "@monaco-editor/react": "^4.7.0", "@rollup/plugin-commonjs": "^25.0.0", "@rollup/plugin-node-resolve": "^15.0.0", "@rollup/plugin-terser": "latest", "@rollup/plugin-typescript": "^11.0.0", "@types/jest": "^29.5.14", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "github-markdown-css": "^5.8.1", "globals": "^16.0.0", "jest": "^29.7.0", "marked": "^15.0.12", "monaco-editor": "^0.52.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "rollup": "^4.41.1", "rollup-plugin-dts": "latest", "ts-jest": "^29.3.4", "tslib": "^2.6.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "u2x": "^1.1.0", "vite": "^6.3.5", "vite-plugin-static-copy": "^3.1.0"}}